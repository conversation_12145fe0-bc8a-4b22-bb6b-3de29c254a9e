# dependencies
node_modules/

# expo
.expo/
dist/
web-build/
expo-env.d.ts

# native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macos
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# yarn
.yarn/*
yarn.lock
# @generated expo-cli sync-8d4afeec25ea8a192358fae2f8e2fc766bdce4ec
# The following patterns were generated by expo-cli

# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# @end expo-cli