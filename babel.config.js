module.exports = function (api) {
  api.cache(true);

  return {
    presets: [
      [
        'babel-preset-expo',
        {
          'react-compiler': {
            // React Compiler configuration
            compilationMode: 'annotation',
            panicThreshold: 'all_errors',
          },
        },
      ],
    ],
    plugins: [
      'react-native-reanimated/plugin', // This plugin must be last
    ],
  };
};
