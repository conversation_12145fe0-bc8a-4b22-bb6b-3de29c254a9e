#!/usr/bin/env node

/**
 * Setup script for Health Consultation database tables in Supabase
 * 
 * This script reads the SQL schema file and executes it against the Supabase database
 * to create the necessary tables for the health consultation feature.
 * 
 * Usage:
 *   node scripts/setup-health-consultation-db.js
 * 
 * Make sure you have the following environment variables set:
 *   - EXPO_PUBLIC_SUPABASE_URL
 *   - EXPO_PUBLIC_SUPABASE_ANON_KEY
 */

const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please make sure EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupDatabase() {
  try {
    console.log('🚀 Setting up Health Consultation database tables...');
    
    // Read the SQL schema file
    const schemaPath = path.join(__dirname, '..', 'database', 'health_consultation_schema.sql');
    const sqlSchema = fs.readFileSync(schemaPath, 'utf8');
    
    console.log('📖 Read schema file successfully');
    
    // Execute the SQL schema
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlSchema });
    
    if (error) {
      console.error('❌ Error executing SQL schema:', error);
      
      // Try alternative approach - execute statements one by one
      console.log('🔄 Trying alternative approach...');
      
      // Split SQL into individual statements
      const statements = sqlSchema
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (const statement of statements) {
        try {
          const { error: stmtError } = await supabase.rpc('exec_sql', { sql: statement });
          if (stmtError) {
            console.warn('⚠️  Warning executing statement:', stmtError.message);
          }
        } catch (stmtErr) {
          console.warn('⚠️  Warning executing statement:', stmtErr.message);
        }
      }
    }
    
    console.log('✅ Database setup completed successfully!');
    console.log('');
    console.log('The following tables have been created:');
    console.log('  - health_conversations');
    console.log('  - health_messages');
    console.log('');
    console.log('You can now use the Health Consultation feature in your app.');
    
  } catch (error) {
    console.error('❌ Error setting up database:', error);
    console.log('');
    console.log('Manual Setup Instructions:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to the SQL Editor');
    console.log('3. Copy and paste the contents of database/health_consultation_schema.sql');
    console.log('4. Execute the SQL script');
    process.exit(1);
  }
}

// Run the setup
setupDatabase();
