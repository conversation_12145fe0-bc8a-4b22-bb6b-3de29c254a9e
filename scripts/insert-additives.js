// Script to insert additives into Supabase database
const { createClient } = require('@supabase/supabase-js');

// Supabase credentials
const supabaseUrl = 'https://qfyfarymbxpwztclgufm.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFmeWZhcnltYnhwd3p0Y2xndWZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3MTQ3MzgsImV4cCI6MjA2MDI5MDczOH0.3Hu_i4h8qCrAKZBlQ5pWTKcGBTsQJ2ji2NTK764OWww';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Additives to insert
const additives = [
  {
    code: 'E121',
    name: { TR: 'Orsein', EN: 'Orcein' },
    safety: 'questionable',
    categoryTr: 'Renklendirici',
    categoryEn: 'Colorant',
    descriptionTr: "E121, kırmızı bir boyadır. AB'de kullanımı yasaklanmıştır.",
    descriptionEn: 'E121 is a red dye. It is banned in the EU.',
    usageTr:
      'Eskiden gıda renklendirmede kullanılırdı, ancak artık onaylı değil.',
    usageEn: 'Formerly used in food coloring, but no longer approved.',
  },
  {
    code: 'E150a',
    name: { TR: 'Karamel', EN: 'Plain Caramel' },
    safety: 'safe',
    categoryTr: 'Renklendirici',
    categoryEn: 'Colorant',
    descriptionTr:
      'E150a, doğal karamel renklendiricisidir. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E150a is a natural caramel colorant. Generally considered safe.',
    usageTr: 'İçecekler, soslar ve şekerlemelerde kullanılır.',
    usageEn: 'Used in beverages, sauces, and confectionery.',
  },
  {
    code: 'E150b',
    name: { TR: 'Kostik Sülfit Karamel', EN: 'Caustic Sulfite Caramel' },
    safety: 'safe',
    categoryTr: 'Renklendirici',
    categoryEn: 'Colorant',
    descriptionTr:
      'E150b, sülfit içeren karamel renklendiricisidir. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E150b is a sulfite-containing caramel colorant. Generally considered safe.',
    usageTr: 'İçecekler ve gıda ürünlerinde kullanılır.',
    usageEn: 'Used in beverages and food products.',
  },
  {
    code: 'E150c',
    name: { TR: 'Amonyak Karamel', EN: 'Ammonia Caramel' },
    safety: 'safe',
    categoryTr: 'Renklendirici',
    categoryEn: 'Colorant',
    descriptionTr:
      'E150c, amonyak ile üretilen karamel renklendiricisidir. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E150c is an ammonia-processed caramel colorant. Generally considered safe.',
    usageTr: 'Bira, soslar ve şekerlemelerde kullanılır.',
    usageEn: 'Used in beer, sauces, and confectionery.',
  },
  {
    code: 'E150d',
    name: { TR: 'Sülfit Amonyak Karamel', EN: 'Sulfite Ammonia Caramel' },
    safety: 'safe',
    categoryTr: 'Renklendirici',
    categoryEn: 'Colorant',
    descriptionTr:
      'E150d, sülfit ve amonyak ile üretilen karamel renklendiricisidir. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E150d is a sulfite and ammonia-processed caramel colorant. Generally considered safe.',
    usageTr: 'Kola, soslar ve gıda ürünlerinde kullanılır.',
    usageEn: 'Used in cola, sauces, and food products.',
  },
  {
    code: 'E171',
    name: { TR: 'Titanyum Dioksit', EN: 'Titanium Dioxide' },
    safety: 'questionable',
    categoryTr: 'Renklendirici',
    categoryEn: 'Colorant',
    descriptionTr:
      "E171, beyaz renklendirici olarak kullanılır. AB'de 2022'de gıda kullanımına yasaklanmıştır.",
    descriptionEn:
      'E171 is used as a white colorant. Banned for food use in the EU in 2022.',
    usageTr: 'Eskiden sakız, şekerleme ve kaplamalarda kullanılırdı.',
    usageEn: 'Formerly used in chewing gum, confectionery, and coatings.',
  },
  {
    code: 'E290',
    name: { TR: 'Karbon Dioksit', EN: 'Carbon Dioxide' },
    safety: 'safe',
    categoryTr: 'Gaz',
    categoryEn: 'Gas',
    descriptionTr:
      'E290, gazlı içeceklerde karbonasyon için kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E290 is used for carbonation in beverages. Generally considered safe.',
    usageTr: 'Gazlı içeceklerde ve bazı gıda paketlemelerinde kullanılır.',
    usageEn: 'Used in carbonated drinks and some food packaging.',
  },
  {
    code: 'E296',
    name: { TR: 'Malik Asit', EN: 'Malic Acid' },
    safety: 'safe',
    categoryTr: 'Asitlik Düzenleyici',
    categoryEn: 'Acidity Regulator',
    descriptionTr:
      'E296, doğal bir asittir ve tat vermek için kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E296 is a natural acid used for flavoring. Generally considered safe.',
    usageTr: 'İçecekler, şekerlemeler ve meyve ürünlerinde kullanılır.',
    usageEn: 'Used in beverages, confectionery, and fruit products.',
  },
  {
    code: 'E297',
    name: { TR: 'Fumarik Asit', EN: 'Fumaric Acid' },
    safety: 'safe',
    categoryTr: 'Asitlik Düzenleyici',
    categoryEn: 'Acidity Regulator',
    descriptionTr:
      'E297, asitlik düzenleyici olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E297 is used as an acidity regulator. Generally considered safe.',
    usageTr: 'İçecekler ve unlu mamullerde kullanılır.',
    usageEn: 'Used in beverages and baked goods.',
  },
  {
    code: 'E301',
    name: { TR: 'Sodyum Askorbat', EN: 'Sodium Ascorbate' },
    safety: 'safe',
    categoryTr: 'Antioksidan',
    categoryEn: 'Antioxidant',
    descriptionTr:
      'E301, C vitamini türevidir ve antioksidan olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E301 is a vitamin C derivative used as an antioxidant. Generally considered safe.',
    usageTr: 'Et ürünleri, içecekler ve konservelerde kullanılır.',
    usageEn: 'Used in meat products, beverages, and canned foods.',
  },
  {
    code: 'E302',
    name: { TR: 'Kalsiyum Askorbat', EN: 'Calcium Ascorbate' },
    safety: 'safe',
    categoryTr: 'Antioksidan',
    categoryEn: 'Antioxidant',
    descriptionTr:
      'E302, C vitamini türevidir ve antioksidan olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E302 is a vitamin C derivative used as an antioxidant. Generally considered safe.',
    usageTr: 'Gıda ürünlerinde oksidasyonu önlemek için kullanılır.',
    usageEn: 'Used to prevent oxidation in food products.',
  },
  {
    code: 'E332',
    name: { TR: 'Potasyum Sitrat', EN: 'Potassium Citrate' },
    safety: 'safe',
    categoryTr: 'Asitlik Düzenleyici',
    categoryEn: 'Acidity Regulator',
    descriptionTr:
      'E332, asitlik düzenleyici ve stabilizatör olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E332 is used as an acidity regulator and stabilizer. Generally considered safe.',
    usageTr: 'İçecekler, reçeller ve süt ürünlerinde kullanılır.',
    usageEn: 'Used in beverages, jams, and dairy products.',
  },
  {
    code: 'E350',
    name: { TR: 'Sodyum Malat', EN: 'Sodium Malate' },
    safety: 'safe',
    categoryTr: 'Asitlik Düzenleyici',
    categoryEn: 'Acidity Regulator',
    descriptionTr:
      'E350, asitlik düzenleyici olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E350 is used as an acidity regulator. Generally considered safe.',
    usageTr: 'İçecekler ve gıda ürünlerinde kullanılır.',
    usageEn: 'Used in beverages and food products.',
  },
  {
    code: 'E351',
    name: { TR: 'Potasyum Malat', EN: 'Potassium Malate' },
    safety: 'safe',
    categoryTr: 'Asitlik Düzenleyici',
    categoryEn: 'Acidity Regulator',
    descriptionTr:
      'E351, asitlik düzenleyici olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E351 is used as an acidity regulator. Generally considered safe.',
    usageTr: 'Gıda ürünlerinde kullanılır.',
    usageEn: 'Used in food products.',
  },
  {
    code: 'E352',
    name: { TR: 'Kalsiyum Malat', EN: 'Calcium Malate' },
    safety: 'safe',
    categoryTr: 'Asitlik Düzenleyici',
    categoryEn: 'Acidity Regulator',
    descriptionTr:
      'E352, asitlik düzenleyici olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E352 is used as an acidity regulator. Generally considered safe.',
    usageTr: 'Gıda ürünlerinde kullanılır.',
    usageEn: 'Used in food products.',
  },
  {
    code: 'E380',
    name: { TR: 'Triamonyum Sitrat', EN: 'Triammonium Citrate' },
    safety: 'safe',
    categoryTr: 'Asitlik Düzenleyici',
    categoryEn: 'Acidity Regulator',
    descriptionTr:
      'E380, asitlik düzenleyici olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E380 is used as an acidity regulator. Generally considered safe.',
    usageTr: 'Peynir ve içeceklerde kullanılır.',
    usageEn: 'Used in cheese and beverages.',
  },
  {
    code: 'E385',
    name: { TR: 'Kalsiyum Disodyum EDTA', EN: 'Calcium Disodium EDTA' },
    safety: 'safe',
    categoryTr: 'Antioksidan',
    categoryEn: 'Antioxidant',
    descriptionTr:
      'E385, koruyucu ve stabilizatör olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E385 is used as a preservative and stabilizer. Generally considered safe.',
    usageTr: 'Konserve gıdalar ve içeceklerde kullanılır.',
    usageEn: 'Used in canned foods and beverages.',
  },
  {
    code: 'E392',
    name: { TR: 'Biberiye Özü', EN: 'Rosemary Extract' },
    safety: 'safe',
    categoryTr: 'Antioksidan',
    categoryEn: 'Antioxidant',
    descriptionTr:
      'E392, doğal bir antioksidan olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E392 is used as a natural antioxidant. Generally considered safe.',
    usageTr: 'Yağlar, et ürünleri ve soslarda kullanılır.',
    usageEn: 'Used in oils, meat products, and sauces.',
  },
  {
    code: 'E411',
    name: { TR: 'Yulaf Sakızı', EN: 'Oat Gum' },
    safety: 'safe',
    categoryTr: 'Kıvam Arttırıcı',
    categoryEn: 'Thickener',
    descriptionTr:
      'E411, kıvam artırıcı olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn: 'E411 is used as a thickener. Generally considered safe.',
    usageTr: 'Süt ürünleri ve soslarda kullanılır.',
    usageEn: 'Used in dairy products and sauces.',
  },
  {
    code: 'E426',
    name: { TR: 'Soya Hemiselüloz', EN: 'Soybean Hemicellulose' },
    safety: 'safe',
    categoryTr: 'Kıvam Arttırıcı',
    categoryEn: 'Thickener',
    descriptionTr:
      'E426, kıvam artırıcı olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn: 'E426 is used as a thickener. Generally considered safe.',
    usageTr: 'İçecekler ve süt ürünlerinde kullanılır.',
    usageEn: 'Used in beverages and dairy products.',
  },
  {
    code: 'E428',
    name: { TR: 'Jelatin', EN: 'Gelatin' },
    safety: 'safe',
    categoryTr: 'Kıvam Arttırıcı',
    categoryEn: 'Thickener',
    descriptionTr:
      'E428, hayvansal kaynaklı bir kıvam artırıcıdır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E428 is an animal-derived thickener. Generally considered safe.',
    usageTr: 'Şekerlemeler, yoğurtlar ve tatlılarda kullanılır.',
    usageEn: 'Used in confectionery, yogurts, and desserts.',
  },
  {
    code: 'E444',
    name: { TR: 'Sukroz Asetat İzobutirat', EN: 'Sucrose Acetate Isobutyrate' },
    safety: 'safe',
    categoryTr: 'Emülgatör',
    categoryEn: 'Emulsifier',
    descriptionTr:
      'E444, emülgatör olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn: 'E444 is used as an emulsifier. Generally considered safe.',
    usageTr: 'İçeceklerde kullanılır.',
    usageEn: 'Used in beverages.',
  },
  {
    code: 'E470a',
    name: {
      TR: 'Sodyum Yağ Asitleri Tuzları',
      EN: 'Sodium Salts of Fatty Acids',
    },
    safety: 'safe',
    categoryTr: 'Emülgatör',
    categoryEn: 'Emulsifier',
    descriptionTr:
      'E470a, emülgatör ve stabilizatör olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E470a is used as an emulsifier and stabilizer. Generally considered safe.',
    usageTr: 'Unlu mamuller ve şekerlemelerde kullanılır.',
    usageEn: 'Used in baked goods and confectionery.',
  },
  {
    code: 'E470b',
    name: {
      TR: 'Magnezyum Yağ Asitleri Tuzları',
      EN: 'Magnesium Salts of Fatty Acids',
    },
    safety: 'safe',
    categoryTr: 'Emülgatör',
    categoryEn: 'Emulsifier',
    descriptionTr:
      'E470b, emülgatör ve stabilizatör olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn:
      'E470b is used as an emulsifier and stabilizer. Generally considered safe.',
    usageTr: 'Gıda ürünlerinde kullanılır.',
    usageEn: 'Used in food products.',
  },
  {
    code: 'E472a',
    name: { TR: 'Asetik Asit Esterleri', EN: 'Acetic Acid Esters' },
    safety: 'safe',
    categoryTr: 'Emülgatör',
    categoryEn: 'Emulsifier',
    descriptionTr:
      'E472a, emülgatör olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn: 'E472a is used as an emulsifier. Generally considered safe.',
    usageTr: 'Unlu mamuller ve süt ürünlerinde kullanılır.',
    usageEn: 'Used in baked goods and dairy products.',
  },
  {
    code: 'E472b',
    name: { TR: 'Laktik Asit Esterleri', EN: 'Lactic Acid Esters' },
    safety: 'safe',
    categoryTr: 'Emülgatör',
    categoryEn: 'Emulsifier',
    descriptionTr:
      'E472b, emülgatör olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn: 'E472b is used as an emulsifier. Generally considered safe.',
    usageTr: 'Unlu mamuller ve krema ürünlerinde kullanılır.',
    usageEn: 'Used in baked goods and cream products.',
  },
  {
    code: 'E472c',
    name: { TR: 'Sitrik Asit Esterleri', EN: 'Citric Acid Esters' },
    safety: 'safe',
    categoryTr: 'Emülgatör',
    categoryEn: 'Emulsifier',
    descriptionTr:
      'E472c, emülgatör olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn: 'E472c is used as an emulsifier. Generally considered safe.',
    usageTr: 'Margarin ve süt ürünlerinde kullanılır.',
    usageEn: 'Used in margarine and dairy products.',
  },
  {
    code: 'E472d',
    name: { TR: 'Tartarik Asit Esterleri', EN: 'Tartaric Acid Esters' },
    safety: 'safe',
    categoryTr: 'Emülgatör',
    categoryEn: 'Emulsifier',
    descriptionTr:
      'E472d, emülgatör olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn: 'E472d is used as an emulsifier. Generally considered safe.',
    usageTr: 'Unlu mamuller ve şekerlemelerde kullanılır.',
    usageEn: 'Used in baked goods and confectionery.',
  },
  {
    code: 'E472e',
    name: {
      TR: 'Mono ve Diagliseritler',
      EN: 'Mono- and Diacetyltartaric Acid Esters',
    },
    safety: 'safe',
    categoryTr: 'Emülgatör',
    categoryEn: 'Emulsifier',
    descriptionTr:
      'E472e, emülgatör olarak kullanılır. Genellikle güvenli kabul edilir.',
    descriptionEn: 'E472e is used as an emulsifier. Generally considered safe.',
    usageTr: 'Ekmek ve unlu mamullerde kullanılır.',
    usageEn: 'Used in bread and baked goods.',
  },
];

// Transform additives to match database schema
const transformedAdditives = additives.map((additive) => ({
  code: additive.code,
  name_tr: additive.name.TR,
  name_en: additive.name.EN,
  safety: additive.safety,
  category_tr: additive.categoryTr,
  category_en: additive.categoryEn,
  description_tr: additive.descriptionTr,
  description_en: additive.descriptionEn,
  usage_tr: additive.usageTr,
  usage_en: additive.usageEn,
}));

// Insert additives into Supabase
async function insertAdditives() {
  console.log(
    `Inserting ${transformedAdditives.length} additives into Supabase...`
  );

  try {
    // Use upsert to handle potential duplicates
    const { data, error } = await supabase
      .from('additives')
      .upsert(transformedAdditives, { onConflict: 'code' });

    if (error) {
      console.error('Error inserting additives:', error);
      return;
    }

    console.log('Additives inserted successfully!');
  } catch (error) {
    console.error('Exception during insertion:', error);
  }
}

// Run the insertion
insertAdditives();
