// Script to restore additives from additives.ts to Supabase
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Supabase credentials
const supabaseUrl = 'https://qfyfarymbxpwztclgufm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFmeWZhcnltYnhwd3p0Y2xndWZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3MTQ3MzgsImV4cCI6MjA2MDI5MDczOH0.3Hu_i4h8qCrAKZBlQ5pWTKcGBTsQJ2ji2NTK764OWww';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Read the additives.ts file
const additivesFilePath = path.join(__dirname, '..', 'app', 'constants', 'additives.ts');
const fileContent = fs.readFileSync(additivesFilePath, 'utf8');

// Extract the additives array using regex
const additivesArrayMatch = fileContent.match(/export const eCodesAll: AdditiveFull\[\] = \[([\s\S]*?)\];/);

if (!additivesArrayMatch) {
  console.error('Could not find additives array in additives.ts');
  process.exit(1);
}

// Get the array content
const additivesArrayContent = additivesArrayMatch[1];

// Create a temporary file with the array content wrapped in a valid JS structure
const tempFilePath = path.join(__dirname, 'temp-additives.js');
fs.writeFileSync(
  tempFilePath,
  `module.exports = [${additivesArrayContent}];`
);

// Load the additives array
const additives = require('./temp-additives.js');
console.log(`Found ${additives.length} additives in additives.ts`);

// Check for duplicate codes
const codeSet = new Set();
const uniqueAdditives = [];
const duplicates = [];

additives.forEach(additive => {
  if (codeSet.has(additive.code)) {
    duplicates.push(additive.code);
  } else {
    codeSet.add(additive.code);
    uniqueAdditives.push(additive);
  }
});

if (duplicates.length > 0) {
  console.log(`Found ${duplicates.length} duplicate codes: ${duplicates.join(', ')}`);
  console.log(`Proceeding with ${uniqueAdditives.length} unique additives`);
}

// Transform additives to match database schema
const transformedAdditives = uniqueAdditives.map(additive => ({
  code: additive.code,
  name_tr: additive.name.TR,
  name_en: additive.name.EN,
  safety: additive.safety,
  category_tr: additive.categoryTr,
  category_en: additive.categoryEn,
  description_tr: additive.descriptionTr,
  description_en: additive.descriptionEn,
  usage_tr: additive.usageTr,
  usage_en: additive.usageEn,
  slugs: additive.slugs || null,
  halal: additive.halal || null
}));

// Insert additives into Supabase
async function insertAdditives() {
  console.log(`Inserting ${transformedAdditives.length} additives into Supabase...`);
  
  try {
    // Use upsert to handle potential duplicates
    const { data, error } = await supabase
      .from('additives')
      .upsert(transformedAdditives, { onConflict: 'code' });
    
    if (error) {
      console.error('Error inserting additives:', error);
      return;
    }
    
    console.log('Additives inserted successfully!');
    
    // Clean up the temporary file
    fs.unlinkSync(tempFilePath);
    console.log('Temporary file cleaned up');
  } catch (error) {
    console.error('Exception during insertion:', error);
  }
}

// Run the insertion
insertAdditives();
