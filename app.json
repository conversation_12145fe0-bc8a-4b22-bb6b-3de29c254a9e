{"expo": {"name": "Additives", "slug": "additives", "version": "2.3.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "newArchEnabled": true, "ios": {"supportsTablet": false, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "CFBundleDisplayName": "Gıda Katkı Maddeleri", "CFBundleAllowMixedLocalizations": true}, "bundleIdentifier": "com.ismailharmanda.additives"}, "locales": {"tr": "./locales/tr.json", "en": "./locales/en.json"}, "android": {"permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"], "adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}, "package": "com.ismailharmanda.additives"}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-camera", {"cameraPermission": "$(PRODUCT_NAME) uses the camera to scan food barcodes and detect additives from ingredient photos."}], ["expo-image-picker", {"photosPermission": "$(PRODUCT_NAME) needs access to your photo library to analyze ingredient text from saved images."}], ["expo-splash-screen", {"backgroundColor": "#ffffff", "image": "./assets/images/icon.png", "dark": {"image": "./assets/images/icon.png", "backgroundColor": "#ffffff"}}]], "experiments": {"typedRoutes": true, "reactCompiler": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "ddb6b009-8ab1-4058-b356-54adbad6652f"}, "environment": "development"}}}