import { useState, useCallback, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocalization } from '../context/LocalizationContext';
import { processImage } from '../utils/ImageProcessor';
import { saveImageToStorage, StoredImageInfo } from '../utils/ImageStorage';
import {
  isAIAnalysisAvailable,
  analyzeImageForECodes,
  analyzeImageForNutrition,
} from '../services/AIImageAnalyzer';

export interface MultiImageStep {
  step: number;
  title: string;
  description: string;
  tip: string;
  type: 'ingredient' | 'nutrition' | 'label';
  purpose: 'ingredients' | 'nutrition' | 'general';
  icon: string;
  completed: boolean;
  imageUri?: string;
  recognizedText?: string;
  quality?: 'good' | 'poor';
  savedImageInfo?: StoredImageInfo; // Store the actual saved image info
}

export interface MultiImageScanResult {
  images: StoredImageInfo[];
  combinedText: string;
  nutritionData?: any;
  stepResults: {
    step: number;
    text: string;
    imageUri: string;
    purpose: 'ingredients' | 'nutrition' | 'general';
    nutritionData?: any;
  }[];
}

export function useMultiImageScan() {
  const { t, language } = useLocalization();
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [scanId] = useState(
    () => Date.now().toString() + Math.random().toString(36).substring(2)
  );

  // Function to generate localized steps
  const generateLocalizedSteps = useCallback(
    (): MultiImageStep[] => [
      {
        step: 1,
        title: t('scan.step1Title'),
        description: t('scan.step1Description'),
        tip: t('scan.step1Tip'),
        type: 'ingredient',
        purpose: 'ingredients',
        icon: 'list-outline',
        completed: false,
      },
      {
        step: 2,
        title: t('scan.step2Title'),
        description: t('scan.step2Description'),
        tip: t('scan.step2Tip'),
        type: 'nutrition',
        purpose: 'nutrition',
        icon: 'nutrition-outline',
        completed: false,
      },
      {
        step: 3,
        title: t('scan.step3Title'),
        description: t('scan.step3Description'),
        tip: t('scan.step3Tip'),
        type: 'label',
        purpose: 'general',
        icon: 'document-text-outline',
        completed: false,
      },
    ],
    [t]
  );

  // Initialize steps with localized content
  const [steps, setSteps] = useState<MultiImageStep[]>(generateLocalizedSteps);

  // Update steps when language changes
  useEffect(() => {
    setSteps((prevSteps) => {
      const newSteps = generateLocalizedSteps();
      // Preserve the completion state and other dynamic properties
      return newSteps.map((newStep, index) => ({
        ...newStep,
        completed: prevSteps[index]?.completed || false,
        imageUri: prevSteps[index]?.imageUri,
        recognizedText: prevSteps[index]?.recognizedText,
        quality: prevSteps[index]?.quality,
        savedImageInfo: prevSteps[index]?.savedImageInfo,
      }));
    });
  }, [language, generateLocalizedSteps]);

  const getCurrentStep = useCallback(() => {
    return steps.find((step) => step.step === currentStep);
  }, [steps, currentStep]);

  const getCompletedSteps = useCallback(() => {
    return steps.filter((step) => step.completed);
  }, [steps]);

  const getAllStepsCompleted = useCallback(() => {
    return steps.every((step) => step.completed);
  }, [steps]);

  const processStepImage = useCallback(
    async (
      imageUri: string,
      stepNumber: number
    ): Promise<{
      text: string;
      quality: 'good' | 'poor';
      nutritionData?: any;
    }> => {
      try {
        setIsProcessing(true);

        const step = steps.find((s) => s.step === stepNumber);
        if (!step) {
          return { text: '', quality: 'poor' };
        }

        // Check if AI is available
        const isAIAvailable = await isAIAnalysisAvailable();

        let recognizedText = '';
        let nutritionData = null;

        if (isAIAvailable) {
          // Get language preference
          let language: 'en' | 'tr' = 'en';
          try {
            const savedLang = await AsyncStorage.getItem('userLanguage');
            if (savedLang === 'tr' || savedLang === 'en') {
              language = savedLang;
            }
          } catch (langError) {
            console.warn('Could not get language preference, using English');
          }

          // Use different AI analysis based on step purpose
          if (step.purpose === 'nutrition') {
            // For nutrition step, extract nutrition information
            console.log('Analyzing nutrition image with AI...');
            nutritionData = await analyzeImageForNutrition(imageUri, language);

            // Also get E-codes from nutrition image (some products list additives there)
            const eCodes = await analyzeImageForECodes(imageUri, language);
            recognizedText = eCodes.length > 0 ? eCodes.join(' ') : '';

            // Add nutrition data as text for processing
            if (nutritionData) {
              const nutritionText = Object.entries(nutritionData)
                .filter(([, value]) => value !== null)
                .map(([key, value]) => `${key}: ${value}`)
                .join(', ');
              recognizedText += (recognizedText ? ' ' : '') + nutritionText;
            }
          } else {
            // For ingredients and general steps, look for E-codes
            console.log(`Analyzing ${step.purpose} image with AI...`);
            const eCodes = await analyzeImageForECodes(imageUri, language);
            recognizedText = eCodes.length > 0 ? eCodes.join(' ') : '';
          }
        }

        // Fallback to regular text recognition if AI fails or unavailable
        if (!recognizedText) {
          console.log('Falling back to regular text recognition...');
          recognizedText = await processImage(imageUri, {
            enhanceText: true,
            useCache: true,
            useAI: false, // Don't use AI again
          });
        }

        // Simple quality assessment based on text length and content
        const quality = recognizedText.length > 10 ? 'good' : 'poor';

        return { text: recognizedText, quality, nutritionData };
      } catch (error) {
        console.error('Error processing step image:', error);
        return { text: '', quality: 'poor' };
      } finally {
        setIsProcessing(false);
      }
    },
    [steps]
  );

  const captureStepImage = useCallback(
    async (imageUri: string, stepNumber: number): Promise<boolean> => {
      try {
        const step = steps.find((s) => s.step === stepNumber);
        if (!step) return false;

        // Save image to storage without processing text yet
        const savedImage = await saveImageToStorage(
          imageUri,
          step.type,
          scanId,
          stepNumber,
          step.purpose
        );

        if (!savedImage) {
          console.error('Failed to save image to storage');
          return false;
        }

        // Update step with captured data (no text processing yet)
        setSteps((prevSteps) =>
          prevSteps.map((s) =>
            s.step === stepNumber
              ? {
                  ...s,
                  completed: true,
                  imageUri: savedImage.uri,
                  savedImageInfo: savedImage, // Store the actual saved image info
                  // Don't process text yet - wait for user to start analysis
                  recognizedText: undefined,
                  quality: undefined, // Quality will be set after processing
                }
              : s
          )
        );

        return true;
      } catch (error) {
        console.error('Error capturing step image:', error);
        return false;
      }
    },
    [steps, scanId]
  );

  const retakeStepImage = useCallback((stepNumber: number) => {
    setSteps((prevSteps) =>
      prevSteps.map((s) =>
        s.step === stepNumber
          ? {
              ...s,
              completed: false,
              imageUri: undefined,
              recognizedText: undefined,
              quality: undefined,
            }
          : s
      )
    );
  }, []);

  const nextStep = useCallback(() => {
    if (currentStep < 3) {
      setCurrentStep((prev) => prev + 1);
    }
  }, [currentStep]);

  const previousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);

  const goToStep = useCallback((stepNumber: number) => {
    if (stepNumber >= 1 && stepNumber <= 3) {
      setCurrentStep(stepNumber);
    }
  }, []);

  const generateScanResult =
    useCallback(async (): Promise<MultiImageScanResult | null> => {
      try {
        setIsAnalyzing(true);

        const completedSteps = getCompletedSteps();
        if (completedSteps.length === 0) return null;

        console.log('Processing all captured images...');

        // Process all images now (when user clicks "Start Analysis")
        const stepResults = [];

        for (const step of completedSteps) {
          if (step.imageUri) {
            console.log(
              `Processing image for step ${step.step}: ${step.title}`
            );

            // Process the image now
            const { text, quality, nutritionData } = await processStepImage(
              step.imageUri,
              step.step
            );

            // Update the step with processed data
            setSteps((prevSteps) =>
              prevSteps.map((s) =>
                s.step === step.step
                  ? { ...s, recognizedText: text, quality }
                  : s
              )
            );

            stepResults.push({
              step: step.step,
              text: text,
              imageUri: step.imageUri,
              purpose: step.purpose,
              nutritionData: nutritionData,
            });
          }
        }

        // Prioritize ingredients text, then nutrition, then general
        const sortedResults = stepResults.sort((a, b) => {
          const priorityOrder = { ingredients: 0, nutrition: 1, general: 2 };
          return priorityOrder[a.purpose] - priorityOrder[b.purpose];
        });

        const combinedText = sortedResults
          .map((result) => result.text)
          .filter((text) => text.length > 0)
          .join(' ');

        console.log('Combined text from all images:', combinedText);

        // Create stored image info array using the actual saved image info
        const images: StoredImageInfo[] = completedSteps
          .map((step) => step.savedImageInfo)
          .filter((img): img is StoredImageInfo => img !== undefined);

        // Extract nutrition data from nutrition step
        const nutritionStep = stepResults.find(
          (result) => result.purpose === 'nutrition'
        );
        const nutritionData = nutritionStep?.nutritionData || null;

        return {
          images,
          combinedText,
          nutritionData,
          stepResults: sortedResults,
        };
      } catch (error) {
        console.error('Error generating scan result:', error);
        return null;
      } finally {
        setIsAnalyzing(false);
      }
    }, [getCompletedSteps, scanId, processStepImage]);

  const resetScan = useCallback(() => {
    setCurrentStep(1);
    setIsProcessing(false);
    setIsAnalyzing(false);
    setSteps((prevSteps) =>
      prevSteps.map((step) => ({
        ...step,
        completed: false,
        imageUri: undefined,
        recognizedText: undefined,
        quality: undefined,
      }))
    );
  }, []);

  return {
    // State
    currentStep,
    steps,
    isProcessing,
    isAnalyzing,
    scanId,

    // Computed values
    getCurrentStep,
    getCompletedSteps,
    getAllStepsCompleted,

    // Actions
    captureStepImage,
    retakeStepImage,
    nextStep,
    previousStep,
    goToStep,
    generateScanResult,
    resetScan,

    // Progress
    totalSteps: 3,
    completedCount: getCompletedSteps().length,
  };
}
