import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const DEBUG_SETTINGS_KEY = 'debug_settings';

export type ImageAnalysisMode = 'direct' | 'textExtraction';

interface DebugSettings {
  imageAnalysisMode: ImageAnalysisMode;
  debugModeEnabled: boolean;
}

const defaultSettings: DebugSettings = {
  imageAnalysisMode: 'direct', // Default to current behavior
  debugModeEnabled: false,
};

export function useDebugSettings() {
  const [settings, setSettings] = useState<DebugSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);

  // Load settings from AsyncStorage
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedSettings = await AsyncStorage.getItem(DEBUG_SETTINGS_KEY);
        if (savedSettings) {
          const parsed = JSON.parse(savedSettings) as DebugSettings;
          setSettings({ ...defaultSettings, ...parsed });
        }
      } catch (error) {
        console.error('Error loading debug settings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // Save settings to AsyncStorage
  const saveSettings = useCallback(async (newSettings: Partial<DebugSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      await AsyncStorage.setItem(DEBUG_SETTINGS_KEY, JSON.stringify(updatedSettings));
      setSettings(updatedSettings);
    } catch (error) {
      console.error('Error saving debug settings:', error);
    }
  }, [settings]);

  // Toggle debug mode
  const toggleDebugMode = useCallback(() => {
    const newValue = !settings.debugModeEnabled;
    saveSettings({ debugModeEnabled: newValue });
  }, [settings.debugModeEnabled, saveSettings]);

  // Set image analysis mode
  const setImageAnalysisMode = useCallback((mode: ImageAnalysisMode) => {
    saveSettings({ imageAnalysisMode: mode });
  }, [saveSettings]);

  // Get current mode display name
  const getCurrentModeDisplayName = useCallback((t: (key: string) => string) => {
    switch (settings.imageAnalysisMode) {
      case 'direct':
        return t('scan.directImageAnalysis');
      case 'textExtraction':
        return t('scan.textExtractionAnalysis');
      default:
        return t('scan.directImageAnalysis');
    }
  }, [settings.imageAnalysisMode]);

  return {
    settings,
    isLoading,
    toggleDebugMode,
    setImageAnalysisMode,
    getCurrentModeDisplayName,
    saveSettings,
  };
}
