import { useState, useCallback, useRef, useEffect } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { useIsFocused } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocalization } from '../context/LocalizationContext';
import { processImage } from '../utils/ImageProcessor';
import { isAIAnalysisAvailable } from '../services/AIImageAnalyzer';
import { saveImageToStorage, StoredImageInfo } from '../utils/ImageStorage';
import { useScanGuidance } from './useScanGuidance';
import { useDebugSettings } from './useDebugSettings';
import {
  FoodScoreCalculator,
  FoodScore,
  ProductNutrition,
} from '../utils/FoodScoreCalculator';
import { useAdditives } from '../context/AdditivesContext';
import { AIFoodAnalysis } from '../services/AIFoodAnalyzer';
import { trackScanCompletion } from '../services/userEngagementService';

type HistoryEntry = {
  id: string;
  timestamp: number;
  codes: string[];
  productName?: string | null; // Optional product name
  images?: StoredImageInfo[]; // Stored scan images
  foodScore?: FoodScore | null;
  aiAnalysis?: AIFoodAnalysis | null;
  nutritionData?: any;
};

const COOLDOWN_DURATION = 5000;
const HISTORY_STORAGE_KEY = '@scanHistory';
const MAX_HISTORY_ITEMS = 50; // Limit history size

export function useScanLogic() {
  const { t } = useLocalization();
  const guidance = useScanGuidance();
  const debugSettings = useDebugSettings();
  const { additives } = useAdditives();

  // Always use text mode since barcode is removed
  const scanMode = 'text';
  const [foundECodes, setFoundECodes] = useState<string[]>([]);
  const [expandedCodes, setExpandedCodes] = useState<Set<string>>(new Set());
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [product, setProduct] = useState<any>(null);
  const [showGalleryPermissionModal, setShowGalleryPermissionModal] =
    useState(false);
  const [galleryPermissionDeniedForever, setGalleryPermissionDeniedForever] =
    useState(false);
  const [isAIAvailable, setIsAIAvailable] = useState(false);
  const [foodScore, setFoodScore] = useState<FoodScore | null>(null);
  const [isCalculatingScore, setIsCalculatingScore] = useState(false);

  const [cameraPermission, requestCameraPermission] = useCameraPermissions();
  const [galleryPermission, requestGalleryPermission] =
    ImagePicker.useMediaLibraryPermissions();

  const cameraRef = useRef<CameraView>(null);
  const isFocused = useIsFocused();

  // We're now using the optimized ECodeMatcher instead of Fuse.js

  // Check AI availability on mount
  useEffect(() => {
    const checkAIAvailability = async () => {
      try {
        const available = await isAIAnalysisAvailable();
        setIsAIAvailable(available);
      } catch (error) {
        console.error('Error checking AI availability:', error);
        setIsAIAvailable(false);
      }
    };
    checkAIAvailability();
  }, []);

  // AppState değişiminde kamera aktifliğini güncelle
  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      (nextAppState: AppStateStatus) => {
        if (nextAppState === 'active') {
          setIsCameraActive(cameraPermission?.granted === true);
        } else {
          setIsCameraActive(false);
        }
      }
    );
    return () => subscription.remove();
  }, [cameraPermission?.granted]);

  // Ekran odaklanması ve izin durumuna göre kamera aktifliğini senkronize et
  useEffect(() => {
    setIsCameraActive(isFocused && cameraPermission?.granted === true);
  }, [isFocused, cameraPermission?.granted]);

  // Monitor gallery permission status changes
  useEffect(() => {
    if (galleryPermission?.granted) {
      // If permission is granted, make sure the modal is closed
      setShowGalleryPermissionModal(false);
    }
  }, [galleryPermission?.granted]);

  // Geri bildirim mesajını belirli bir süre göster
  const showFeedbackMessage = useCallback((message: string) => {
    setFeedbackMessage(message);
    setShowFeedback(true);
    setTimeout(() => setShowFeedback(false), 3000);
  }, []);

  // E-code detaylarının açılıp kapanması
  const toggleExpand = useCallback((code: string) => {
    setExpandedCodes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(code)) newSet.delete(code);
      else newSet.add(code);
      return newSet;
    });
  }, []);

  // Save scan results to history
  const saveScanToHistory = useCallback(
    async (
      codes: string[],
      productName?: string | null,
      imageUri?: string,
      scanType?: 'barcode' | 'text',
      multiImages?: StoredImageInfo[],
      foodScore?: FoodScore | null,
      nutritionData?: any
    ) => {
      if (
        codes.length === 0 &&
        !productName &&
        (!multiImages || multiImages.length === 0)
      ) {
        return; // Don't save empty scans unless multi-images were provided
      }
      try {
        const scanId =
          Date.now().toString() + Math.random().toString(36).substring(2);
        let savedImages: StoredImageInfo[] = [];

        // Use multi-images if provided (from multi-image scanning)
        if (multiImages && multiImages.length > 0) {
          savedImages = multiImages;
        } else if (imageUri && scanType) {
          // Save single image if provided
          const imageType = scanType === 'barcode' ? 'barcode' : 'ingredient';
          const savedImage = await saveImageToStorage(
            imageUri,
            imageType,
            scanId
          );
          if (savedImage) {
            savedImages.push(savedImage);
          }
        }

        const newEntry: HistoryEntry = {
          id: scanId,
          timestamp: Date.now(),
          codes: codes,
          productName: productName,
          images: savedImages.length > 0 ? savedImages : undefined,
          foodScore: foodScore,
          nutritionData: nutritionData,
        };

        const existingHistoryJson = await AsyncStorage.getItem(
          HISTORY_STORAGE_KEY
        );
        let history: HistoryEntry[] = existingHistoryJson
          ? JSON.parse(existingHistoryJson)
          : [];

        // Add new entry to the beginning
        history.unshift(newEntry);

        // Limit history size
        if (history.length > MAX_HISTORY_ITEMS) {
          history = history.slice(0, MAX_HISTORY_ITEMS);
        }

        await AsyncStorage.setItem(
          HISTORY_STORAGE_KEY,
          JSON.stringify(history)
        );
        console.log('Scan saved to history:', newEntry);
      } catch (error) {
        console.error('Error saving scan to history:', error);
      }
    },
    []
  );

  // Calculate food score based on found E-codes, product nutrition, and ingredients
  const calculateFoodScore = useCallback(
    async (
      foundECodes: string[],
      productNutrition?: ProductNutrition,
      isOrganic?: boolean,
      ingredientText?: string
    ): Promise<FoodScore | null> => {
      // Always calculate food score, even if no E-codes or nutrition data
      try {
        const score = await FoodScoreCalculator.calculateFoodScore(
          foundECodes,
          additives,
          productNutrition,
          isOrganic || false,
          ingredientText
        );
        setFoodScore(score);
        console.log('Calculated food score:', score);
        return score;
      } catch (error) {
        console.error('Error calculating food score:', error);
        setFoodScore(null);
        return null;
      }
    },
    [additives]
  );

  // Metindeki additive kodlarını veya isimlerini çıkaran fonksiyon - optimized version
  const processText = useCallback(
    async (
      text: string,
      productName?: string | null,
      imageUri?: string,
      multiImages?: StoredImageInfo[],
      nutritionData?: any
    ) => {
      // Always log the text being processed
      console.log('Processing text for E-codes:', text);

      // Parse E-codes from AI analysis result (space-separated string)
      // The text parameter contains E-codes found by AI analysis
      const validECodes = text
        .split(' ')
        .map((code) => code.trim().toUpperCase())
        .filter((code) => code.match(/^E\d+$/)) // Only valid E-code format
        .filter((code) => code.length > 0);

      console.log('Parsed E-codes from AI analysis:', validECodes);

      if (validECodes.length > 0) {
        // Start calculating score - this will prevent modal from opening
        setIsCalculatingScore(true);

        // Calculate food score for the found E-codes and include ingredient text for analysis
        // Use nutrition data if available from multi-image scanning
        const productNutrition = nutritionData
          ? {
              energy_100g: nutritionData.energy_100g || 0,
              fat_100g: nutritionData.fat_100g || 0,
              saturated_fat_100g: nutritionData.saturated_fat_100g || 0,
              carbohydrates_100g: nutritionData.carbohydrates_100g || 0,
              sugars_100g: nutritionData.sugars_100g || 0,
              proteins_100g: nutritionData.proteins_100g || 0,
              salt_100g: nutritionData.salt_100g || 0,
              fiber_100g: nutritionData.fiber_100g || 0,
            }
          : undefined;

        const calculatedScore = await calculateFoodScore(
          validECodes,
          productNutrition,
          undefined,
          text
        );

        // Save to history after calculating food score
        await saveScanToHistory(
          validECodes,
          productName,
          imageUri,
          'text', // Always text mode now
          multiImages,
          calculatedScore, // Pass calculated food score
          nutritionData
        );

        // Track scan completion for rating system
        await trackScanCompletion();

        // Now set all states together after all calculations are complete
        setFoundECodes((prev) => [...new Set([...prev, ...validECodes])]);
        setFoodScore(calculatedScore);
        setIsCalculatingScore(false); // Allow modal to open now
      } else {
        // Even if no E-codes found, still calculate food score for the product
        // This allows analysis based on nutrition data or ingredient text analysis

        // Only calculate score if we have nutrition data or multi-images
        if (nutritionData || (multiImages && multiImages.length > 0)) {
          setIsCalculatingScore(true);

          const productNutrition = nutritionData
            ? {
                energy_100g: nutritionData.energy_100g || 0,
                fat_100g: nutritionData.fat_100g || 0,
                saturated_fat_100g: nutritionData.saturated_fat_100g || 0,
                carbohydrates_100g: nutritionData.carbohydrates_100g || 0,
                sugars_100g: nutritionData.sugars_100g || 0,
                proteins_100g: nutritionData.proteins_100g || 0,
                salt_100g: nutritionData.salt_100g || 0,
                fiber_100g: nutritionData.fiber_100g || 0,
              }
            : undefined;

          const calculatedScore = await calculateFoodScore(
            [],
            productNutrition,
            undefined,
            text
          );

          // If multi-images were provided, save to history even without E-codes
          if (multiImages && multiImages.length > 0) {
            await saveScanToHistory(
              [], // No E-codes found
              productName,
              imageUri,
              'text', // Always text mode now
              multiImages,
              calculatedScore, // Pass calculated food score
              nutritionData
            );
          }

          // Create a product object with nutrition data if available, even without E-codes
          if (nutritionData || productName) {
            setProduct({
              brands: productName || '',
              product_name_en: productName || '',
              nutriments: nutritionData || {
                energy_100g: '-',
                proteins_100g: '-',
                carbohydrates_100g: '-',
                fat_100g: '-',
              },
            });
          }

          // Set all states together after calculations are complete
          setFoodScore(calculatedScore);
          setIsCalculatingScore(false);
        }

        // Check if the text might contain product information but no E-codes
        const containsProductInfo =
          /\d{1,3}\s*(ml|gr|g|kg)\b|\b(tel|telefon|no|kayıt|kayit|işletme|isletme)\b/i.test(
            text
          );

        let message;
        if (containsProductInfo) {
          // If it looks like product information, give a more specific message
          message = t('scan.productInfoButNoECodes');
        } else {
          message = t('scan.noECodeFoundInImage');
        }

        showFeedbackMessage(message);
      }
    },
    [showFeedbackMessage, t, saveScanToHistory, calculateFoodScore]
  );
  // Removed barcode scanning functionality

  // Kameradan fotoğraf çekip metni işleyen fonksiyon - optimized version
  const takePicture = useCallback(async () => {
    if (!cameraRef.current || isProcessing) return;
    setIsProcessing(true);
    console.log('Taking picture from camera...');
    try {
      const photo = await cameraRef.current.takePictureAsync({
        skipProcessing: true,
        quality: 1, // Full quality
      });
      if (photo?.uri) {
        console.log(
          'Picture taken successfully, processing image from camera...'
        );
        // Use our optimized image processor with AI option
        const recognizedText = await processImage(photo.uri, {
          enhanceText: true,
          useCache: true,
          useAI: isAIAvailable, // Always use AI if available
          analysisMode: debugSettings.settings.imageAnalysisMode, // Use debug mode setting
        });
        // Pass null for product name when processing image text, include image URI
        await processText(recognizedText, null, photo.uri);
      }
    } catch (error) {
      console.error('Error processing image from camera:', error);
      showFeedbackMessage(t('scan.errorProcessingImage'));
    } finally {
      setIsProcessing(false);
    }
  }, [
    isProcessing,
    cameraRef,
    processText,
    t,
    showFeedbackMessage,
    isAIAvailable,
  ]);

  // Galeriden resim seçip metni işleyen fonksiyon - optimized version
  const pickImage = useCallback(async () => {
    if (isProcessing || !galleryPermission) return;

    // Check if we're already in the process of requesting permission
    if (showGalleryPermissionModal) {
      console.log(
        'Gallery permission modal is already showing, skipping pickImage call'
      );
      return;
    }

    if (galleryPermission.granted) {
      setIsProcessing(true);
      console.log('Picking image from gallery...');
      try {
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ['images', 'livePhotos'],
          quality: 1,
          allowsEditing: true,
        });
        if (!result.canceled && result.assets[0]) {
          console.log('Image selected from gallery, processing...');
          // Use our optimized image processor with AI option
          const recognizedText = await processImage(result.assets[0].uri, {
            enhanceText: true,
            useCache: true,
            useAI: isAIAvailable, // Always use AI if available
            analysisMode: debugSettings.settings.imageAnalysisMode, // Use debug mode setting
          });
          // Pass null for product name when processing image text, include image URI
          await processText(recognizedText, null, result.assets[0].uri);
        } else {
          console.log('Image selection canceled by user');
        }
      } catch (error) {
        console.error('Error picking image from gallery:', error);
        showFeedbackMessage(t('scan.errorProcessingImage'));
      } finally {
        setIsProcessing(false);
      }
    } else if (
      galleryPermission.status === 'denied' &&
      !galleryPermission.canAskAgain
    ) {
      console.log('Gallery permission denied permanently');
      setGalleryPermissionDeniedForever(true);
      setShowGalleryPermissionModal(true);
    } else if (galleryPermission.status === 'undetermined') {
      console.log('Gallery permission not determined, asking user');
      setGalleryPermissionDeniedForever(false);
      setShowGalleryPermissionModal(true);
    }
  }, [
    isProcessing,
    galleryPermission,
    processText,
    t,
    showFeedbackMessage,
    showGalleryPermissionModal,
  ]);

  // Galeri izni için talepte bulun ve sonrasında resmi işle
  const handleGalleryPermissionRequest = useCallback(async () => {
    console.log('Requesting gallery permission...');
    try {
      // First, close the modal to prevent it from showing again
      setShowGalleryPermissionModal(false);

      // Then request permission
      const result = await requestGalleryPermission();

      if (result.granted) {
        console.log('Gallery permission granted, proceeding to pick image');
        // Add a small delay to ensure the permission status is updated
        setTimeout(() => {
          // Use a direct call to ImagePicker instead of pickImage to avoid permission checks
          (async () => {
            setIsProcessing(true);
            try {
              const pickerResult = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ['images', 'livePhotos'],
                quality: 1,
                allowsEditing: true,
              });

              if (!pickerResult.canceled && pickerResult.assets[0]) {
                console.log(
                  'Image selected from gallery after permission grant, processing...'
                );
                const recognizedText = await processImage(
                  pickerResult.assets[0].uri,
                  {
                    enhanceText: true,
                    useCache: true,
                    useAI: isAIAvailable, // Always use AI if available
                    analysisMode: debugSettings.settings.imageAnalysisMode, // Use debug mode setting
                  }
                );
                await processText(
                  recognizedText,
                  null,
                  pickerResult.assets[0].uri
                );
              } else {
                console.log(
                  'Image selection canceled by user after permission grant'
                );
              }
            } catch (error) {
              console.error(
                'Error picking image after permission grant:',
                error
              );
              showFeedbackMessage(t('scan.errorProcessingImage'));
            } finally {
              setIsProcessing(false);
            }
          })();
        }, 500); // 500ms delay to ensure permission status is updated
      } else {
        console.log('Gallery permission denied by user');
        showFeedbackMessage(t('scan.galleryPermissionDenied'));
      }
    } catch (error) {
      console.error('Error in gallery permission request:', error);
      setShowGalleryPermissionModal(false);
      showFeedbackMessage(t('scan.galleryPermissionDenied'));
    }
  }, [
    requestGalleryPermission,
    processImage,
    processText,
    showFeedbackMessage,
    t,
  ]);

  // Modal kapatma ve state resetleme
  const closeModal = useCallback(() => {
    console.log('Closing modal and resetting state in useScanLogic');
    setFoundECodes([]);
    setExpandedCodes(new Set());
    setProduct(null);
    setFoodScore(null);
    setIsCalculatingScore(false);
  }, []);

  // Handle scan mode changes for guidance
  const handleScanModeChange = useCallback(
    (mode: ScanMode) => {
      setScanMode(mode);
      guidance.handleScanModeChange(mode);
    },
    [guidance]
  );

  return {
    scanMode,
    foundECodes,
    expandedCodes,
    isProcessing,
    isCameraActive,
    showFeedback,
    feedbackMessage,
    product,
    foodScore,
    isCalculatingScore,
    showGalleryPermissionModal,
    galleryPermissionDeniedForever,
    cameraPermission,
    galleryPermission,
    cameraRef,
    isFocused,
    requestCameraPermission,
    showFeedbackMessage,
    toggleExpand,
    processText,
    takePicture,
    pickImage,
    handleGalleryPermissionRequest,
    closeModal,
    setShowGalleryPermissionModal,
    isAIAvailable,
    textDescription: t('scan.textDescription'),
    // Guidance-related exports
    guidance,
    // Debug settings
    debugSettings,
  };
}
