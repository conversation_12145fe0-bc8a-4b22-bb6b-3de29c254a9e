import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const GUIDANCE_STORAGE_KEY = 'scan_guidance_preferences';
const TUTORIAL_SHOWN_KEY = 'scan_tutorial_shown';

interface GuidancePreferences {
  showGuidanceOverlay: boolean;
  tutorialCompleted: boolean;
  guidanceEnabled: boolean;
  firstTimeScan: boolean;
  onboardingCompleted: boolean;
}

const defaultPreferences: GuidancePreferences = {
  showGuidanceOverlay: true,
  tutorialCompleted: false,
  guidanceEnabled: true,
  firstTimeScan: true,
  onboardingCompleted: false,
};

export function useScanGuidance() {
  const [preferences, setPreferences] =
    useState<GuidancePreferences>(defaultPreferences);
  const [showGuidanceOverlay, setShowGuidanceOverlay] = useState(false);
  const [showTutorial, setShowTutorial] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load preferences from storage
  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      const stored = await AsyncStorage.getItem(GUIDANCE_STORAGE_KEY);
      if (stored) {
        const parsedPreferences = JSON.parse(stored);
        const loadedPreferences = {
          ...defaultPreferences,
          ...parsedPreferences,
        };
        setPreferences(loadedPreferences);

        // Show onboarding if it hasn't been completed
        if (!loadedPreferences.onboardingCompleted) {
          setShowOnboarding(true);
        }
      } else {
        // First time user - show onboarding
        setShowOnboarding(true);
      }
    } catch (error) {
      console.error('Error loading guidance preferences:', error);
      // On error, show onboarding for safety
      setShowOnboarding(true);
    } finally {
      setIsLoading(false);
    }
  };

  const savePreferences = async (
    newPreferences: Partial<GuidancePreferences>
  ) => {
    try {
      const updatedPreferences = { ...preferences, ...newPreferences };
      await AsyncStorage.setItem(
        GUIDANCE_STORAGE_KEY,
        JSON.stringify(updatedPreferences)
      );
      setPreferences(updatedPreferences);
    } catch (error) {
      console.error('Error saving guidance preferences:', error);
    }
  };

  // Show guidance when entering text scan mode for the first time
  const handleScanModeChange = useCallback(
    (scanMode: 'barcode' | 'text') => {
      if (
        scanMode === 'text' &&
        preferences.guidanceEnabled &&
        preferences.firstTimeScan
      ) {
        setShowGuidanceOverlay(true);
      }
    },
    [preferences.guidanceEnabled, preferences.firstTimeScan]
  );

  // Show tutorial
  const showScanTutorial = useCallback(() => {
    console.log('showScanTutorial called');
    setShowTutorial(true);
    // Hide guidance overlay when tutorial is shown
    setShowGuidanceOverlay(false);
  }, []);

  // Hide guidance overlay
  const hideGuidanceOverlay = useCallback(() => {
    setShowGuidanceOverlay(false);
    // Mark first time scan as completed
    if (preferences.firstTimeScan) {
      savePreferences({ firstTimeScan: false });
    }
  }, [preferences.firstTimeScan, savePreferences]);

  // Hide tutorial
  const hideTutorial = useCallback(() => {
    setShowTutorial(false);
    savePreferences({ tutorialCompleted: true });
  }, [savePreferences]);

  // Complete onboarding
  const completeOnboarding = useCallback(() => {
    setShowOnboarding(false);
    savePreferences({ onboardingCompleted: true });
  }, [savePreferences]);

  // Toggle guidance enabled/disabled
  const toggleGuidanceEnabled = useCallback(() => {
    const newValue = !preferences.guidanceEnabled;
    savePreferences({ guidanceEnabled: newValue });

    // Hide overlay if guidance is disabled
    if (!newValue) {
      setShowGuidanceOverlay(false);
    }
  }, [preferences.guidanceEnabled, savePreferences]);

  // Reset all guidance preferences (for testing or user request)
  const resetGuidancePreferences = useCallback(async () => {
    try {
      await AsyncStorage.removeItem(GUIDANCE_STORAGE_KEY);
      await AsyncStorage.removeItem(TUTORIAL_SHOWN_KEY);
      setPreferences(defaultPreferences);
      setShowGuidanceOverlay(false);
      setShowTutorial(false);
      setShowOnboarding(false);
    } catch (error) {
      console.error('Error resetting guidance preferences:', error);
    }
  }, []);

  // Force show onboarding (for testing)
  const showOnboardingDemo = useCallback(() => {
    setShowOnboarding(true);
  }, []);

  // Force show guidance overlay (for testing)
  const forceShowGuidance = useCallback(() => {
    console.log('forceShowGuidance called');
    setShowGuidanceOverlay(true);
  }, []);

  // Force show tutorial (for testing)
  const forceShowTutorial = useCallback(() => {
    console.log('forceShowTutorial called');
    setShowTutorial(true);
  }, []);

  // Check if should show guidance for current scan mode
  const shouldShowGuidance = useCallback(
    (scanMode: 'barcode' | 'text') => {
      return (
        scanMode === 'text' &&
        preferences.guidanceEnabled &&
        preferences.showGuidanceOverlay &&
        !showTutorial
      );
    },
    [preferences.guidanceEnabled, preferences.showGuidanceOverlay, showTutorial]
  );

  // Manually show guidance overlay
  const showGuidance = useCallback(() => {
    console.log(
      'showGuidance called, guidanceEnabled:',
      preferences.guidanceEnabled
    );
    if (preferences.guidanceEnabled) {
      console.log('Setting showGuidanceOverlay to true');
      setShowGuidanceOverlay(true);
    }
  }, [preferences.guidanceEnabled]);

  // Get guidance status for UI
  const getGuidanceStatus = useCallback(() => {
    return {
      isEnabled: preferences.guidanceEnabled,
      isFirstTime: preferences.firstTimeScan,
      tutorialCompleted: preferences.tutorialCompleted,
      overlayVisible: showGuidanceOverlay,
      tutorialVisible: showTutorial,
    };
  }, [
    preferences.guidanceEnabled,
    preferences.firstTimeScan,
    preferences.tutorialCompleted,
    showGuidanceOverlay,
    showTutorial,
  ]);

  return {
    // State
    isLoading,
    showGuidanceOverlay,
    showTutorial,
    showOnboarding,
    preferences,

    // Actions
    handleScanModeChange,
    showScanTutorial,
    hideGuidanceOverlay,
    hideTutorial,
    completeOnboarding,
    toggleGuidanceEnabled,
    resetGuidancePreferences,
    showGuidance,
    showOnboardingDemo,
    forceShowGuidance,
    forceShowTutorial,

    // Utilities
    shouldShowGuidance,
    getGuidanceStatus,
  };
}
