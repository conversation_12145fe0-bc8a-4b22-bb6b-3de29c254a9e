import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown, FadeInRight } from 'react-native-reanimated';
import { useLocalization } from './context/LocalizationContext';
import { HealthConversation, HealthMessage } from './types/healthConsultation';
import {
  getHealthConversation,
  getHealthMessages,
  deleteHealthConversation,
  updateHealthConversation,
} from './services/HealthConsultationSupabase';

interface ConversationStats {
  totalMessages: number;
  userMessages: number;
  assistantMessages: number;
  duration: number; // in minutes
  firstMessageTime: number;
  lastMessageTime: number;
  averageResponseTime: number; // in seconds
  topicsDiscussed: string[];
}

export default function HealthConversationDetailScreen() {
  const { conversationId } = useLocalSearchParams<{ conversationId: string }>();
  const router = useRouter();
  const { t } = useLocalization();

  const [conversation, setConversation] = useState<HealthConversation | null>(
    null
  );
  const [conversationMessages, setConversationMessages] = useState<
    HealthMessage[]
  >([]);
  const [stats, setStats] = useState<ConversationStats | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);

  useEffect(() => {
    const loadConversationData = async () => {
      if (!conversationId) {
        Alert.alert(
          t('healthConsultation.error.title'),
          t('healthConsultation.error.conversationNotFound'),
          [{ text: t('common.ok'), onPress: () => router.back() }]
        );
        return;
      }

      try {
        setIsLoadingData(true);

        // Load conversation directly from database
        const conversationData = await getHealthConversation(conversationId);
        if (conversationData) {
          setConversation(conversationData);
        } else {
          throw new Error('Conversation not found');
        }

        // Load messages for this conversation
        const messages = await getHealthMessages(conversationId);
        setConversationMessages(messages);
        calculateStats(messages);
      } catch (error) {
        console.error('Failed to load conversation details:', error);
        Alert.alert(
          t('healthConsultation.error.title'),
          t('healthConsultation.error.failedToLoadConversation'),
          [{ text: t('common.ok'), onPress: () => router.back() }]
        );
      } finally {
        setIsLoadingData(false);
      }
    };

    loadConversationData();
  }, [conversationId, t, router]);

  const calculateStats = (msgs: HealthMessage[]) => {
    if (msgs.length === 0) {
      setStats(null);
      return;
    }

    const userMessages = msgs.filter((m) => m.role === 'user');
    const assistantMessages = msgs.filter((m) => m.role === 'assistant');
    const systemMessages = msgs.filter((m) => m.role === 'system');

    const sortedMessages = [...msgs].sort((a, b) => a.timestamp - b.timestamp);
    const firstMessage = sortedMessages[0];
    const lastMessage = sortedMessages[sortedMessages.length - 1];

    const duration = Math.round(
      (lastMessage.timestamp - firstMessage.timestamp) / (1000 * 60)
    ); // minutes

    // Calculate average response time (time between user message and next assistant message)
    let totalResponseTime = 0;
    let responseCount = 0;

    for (let i = 0; i < sortedMessages.length - 1; i++) {
      const current = sortedMessages[i];
      const next = sortedMessages[i + 1];

      if (current.role === 'user' && next.role === 'assistant') {
        totalResponseTime += (next.timestamp - current.timestamp) / 1000; // seconds
        responseCount++;
      }
    }

    const averageResponseTime =
      responseCount > 0 ? Math.round(totalResponseTime / responseCount) : 0;

    // Extract topics from conversation title and messages
    const topicsDiscussed: string[] = [];
    if (conversation?.topic) {
      topicsDiscussed.push(conversation.topic);
    }

    const calculatedStats: ConversationStats = {
      totalMessages: msgs.length,
      userMessages: userMessages.length,
      assistantMessages: assistantMessages.length,
      duration,
      firstMessageTime: firstMessage.timestamp,
      lastMessageTime: lastMessage.timestamp,
      averageResponseTime,
      topicsDiscussed,
    };

    setStats(calculatedStats);
  };

  const handleDeleteConversation = async () => {
    if (!conversation) return;

    Alert.alert(
      t('healthConsultation.history.deleteTitle'),
      t('healthConsultation.history.deleteMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteHealthConversation(conversation.id);
              router.back();
            } catch (error) {
              console.error('Error deleting conversation:', error);
              Alert.alert(
                t('common.error'),
                t('healthConsultation.history.deleteError')
              );
            }
          },
        },
      ]
    );
  };

  const handleArchiveConversation = async () => {
    if (!conversation) return;

    try {
      // Update conversation archive status
      const updatedConversation = await updateHealthConversation(
        conversation.id,
        {
          isArchived: !conversation.isArchived,
        }
      );

      if (updatedConversation) {
        setConversation(updatedConversation);
      }
    } catch (error) {
      console.error('Error archiving/unarchiving conversation:', error);
      Alert.alert(
        t('common.error'),
        t('healthConsultation.history.archiveError')
      );
    }
  };

  const handleOpenChat = () => {
    if (!conversationId) return;
    router.push(`/health-chat?conversationId=${conversationId}`);
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} ${t('healthConsultation.detail.minutes')}`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}${t(
      'healthConsultation.detail.hours'
    )} ${remainingMinutes}${t('healthConsultation.detail.minutes')}`;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoadingData) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: t('healthConsultation.detail.title'),
            headerShown: true,
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>
            {t('healthConsultation.loadingConversation')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!conversation) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: t('healthConsultation.error.title'),
            headerShown: true,
          }}
        />
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#FF5722" />
          <Text style={styles.errorText}>
            {t('healthConsultation.error.conversationNotFound')}
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => router.back()}
          >
            <Text style={styles.retryButtonText}>{t('common.goBack')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: t('healthConsultation.detail.title'),
          headerShown: true,
          headerRight: () => (
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleOpenChat}
            >
              <Ionicons name="chatbubble-outline" size={24} color="#2196F3" />
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Conversation Header */}
        <Animated.View
          entering={FadeInDown.delay(100)}
          style={styles.headerSection}
        >
          <View style={styles.titleContainer}>
            <Text style={styles.conversationTitle} numberOfLines={2}>
              {conversation.title}
            </Text>
            {conversation.isArchived && (
              <View style={styles.archivedBadge}>
                <Ionicons name="archive" size={16} color="#FF9800" />
                <Text style={styles.archivedText}>
                  {t('healthConsultation.history.archived')}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.dateContainer}>
            <Text style={styles.dateLabel}>
              {t('healthConsultation.detail.created')}
            </Text>
            <Text style={styles.dateText}>
              {formatDate(conversation.createdAt)}
            </Text>
          </View>

          {conversation.updatedAt !== conversation.createdAt && (
            <View style={styles.dateContainer}>
              <Text style={styles.dateLabel}>
                {t('healthConsultation.detail.lastUpdated')}
              </Text>
              <Text style={styles.dateText}>
                {formatDate(conversation.updatedAt)}
              </Text>
            </View>
          )}
        </Animated.View>

        {/* Statistics Section */}
        {stats && (
          <Animated.View
            entering={FadeInDown.delay(200)}
            style={styles.statsSection}
          >
            <Text style={styles.sectionTitle}>
              {t('healthConsultation.detail.statistics')}
            </Text>

            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Ionicons name="chatbubbles" size={24} color="#2196F3" />
                <Text style={styles.statNumber}>{stats.totalMessages}</Text>
                <Text style={styles.statLabel}>
                  {t('healthConsultation.detail.totalMessages')}
                </Text>
              </View>

              <View style={styles.statCard}>
                <Ionicons name="person" size={24} color="#4CAF50" />
                <Text style={styles.statNumber}>{stats.userMessages}</Text>
                <Text style={styles.statLabel}>
                  {t('healthConsultation.detail.userMessages')}
                </Text>
              </View>

              <View style={styles.statCard}>
                <Ionicons name="medical" size={24} color="#FF9800" />
                <Text style={styles.statNumber}>{stats.assistantMessages}</Text>
                <Text style={styles.statLabel}>
                  {t('healthConsultation.detail.aiResponses')}
                </Text>
              </View>

              <View style={styles.statCard}>
                <Ionicons name="time" size={24} color="#9C27B0" />
                <Text style={styles.statNumber}>
                  {formatDuration(stats.duration)}
                </Text>
                <Text style={styles.statLabel}>
                  {t('healthConsultation.detail.duration')}
                </Text>
              </View>
            </View>

            {stats.averageResponseTime > 0 && (
              <View style={styles.responseTimeCard}>
                <Ionicons name="flash" size={20} color="#FF5722" />
                <Text style={styles.responseTimeText}>
                  {t('healthConsultation.detail.averageResponseTime')}:{' '}
                  {stats.averageResponseTime}s
                </Text>
              </View>
            )}
          </Animated.View>
        )}

        {/* Last Message Preview */}
        {conversation.lastMessage && (
          <Animated.View
            entering={FadeInDown.delay(300)}
            style={styles.previewSection}
          >
            <Text style={styles.sectionTitle}>
              {t('healthConsultation.detail.lastMessage')}
            </Text>
            <View style={styles.messagePreview}>
              <Text style={styles.previewText} numberOfLines={3}>
                {conversation.lastMessage}
              </Text>
            </View>
          </Animated.View>
        )}

        {/* Action Buttons */}
        <Animated.View
          entering={FadeInRight.delay(400)}
          style={styles.actionsSection}
        >
          <TouchableOpacity
            style={[styles.actionButton, styles.primaryButton]}
            onPress={handleOpenChat}
          >
            <Ionicons name="chatbubble" size={20} color="white" />
            <Text style={styles.primaryButtonText}>
              {t('healthConsultation.detail.openChat')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={handleArchiveConversation}
          >
            <Ionicons
              name={conversation.isArchived ? 'archive-outline' : 'archive'}
              size={20}
              color="#FF9800"
            />
            <Text style={styles.secondaryButtonText}>
              {conversation.isArchived
                ? t('healthConsultation.history.active')
                : t('healthConsultation.history.archived')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.dangerButton]}
            onPress={handleDeleteConversation}
          >
            <Ionicons name="trash" size={20} color="#F44336" />
            <Text style={styles.dangerButtonText}>{t('common.delete')}</Text>
          </TouchableOpacity>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  headerButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  headerSection: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  titleContainer: {
    marginBottom: 16,
  },
  conversationTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    lineHeight: 32,
  },
  archivedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  archivedText: {
    marginLeft: 6,
    fontSize: 12,
    color: '#FF9800',
    fontWeight: '600',
  },
  dateContainer: {
    marginBottom: 8,
  },
  dateLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  dateText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  statsSection: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 12,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  responseTimeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    padding: 12,
    borderRadius: 8,
    justifyContent: 'center',
  },
  responseTimeText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#FF5722',
    fontWeight: '500',
  },
  previewSection: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 12,
  },
  messagePreview: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  previewText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  actionsSection: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 12,
  },
  primaryButton: {
    backgroundColor: '#2196F3',
  },
  primaryButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  secondaryButton: {
    backgroundColor: '#FFF3E0',
    borderWidth: 1,
    borderColor: '#FF9800',
  },
  secondaryButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#FF9800',
  },
  dangerButton: {
    backgroundColor: '#FFEBEE',
    borderWidth: 1,
    borderColor: '#F44336',
  },
  dangerButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#F44336',
  },
});
