import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Image,
  Linking,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { PurchasesPackage } from 'react-native-purchases';
import { useSubscription } from './context/SubscriptionContext';
import { useLocalization } from './context/LocalizationContext';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';

export default function PaywallScreen() {
  const router = useRouter();
  const { onPurchaseSuccess } = useLocalSearchParams<{
    onPurchaseSuccess?: string;
  }>();

  const { t } = useLocalization();
  const { offerings, purchasePackage, restoreUserPurchases } =
    useSubscription();

  const [selectedPackage, setSelectedPackage] =
    useState<PurchasesPackage | null>(null);
  const [purchasing, setPurchasing] = useState(false);

  // Auto-select yearly package by default (better value)
  useEffect(() => {
    if (offerings?.availablePackages) {
      const yearlyPackage = offerings.availablePackages.find(
        (pkg) =>
          pkg.identifier === '$rc_annual' ||
          pkg.identifier === '$rc_yearly' ||
          pkg.identifier.includes('yearly') ||
          pkg.identifier.includes('annual')
      );
      const monthlyPackage = offerings.availablePackages.find(
        (pkg) =>
          pkg.identifier === '$rc_monthly' || pkg.identifier.includes('monthly')
      );

      // Prefer yearly, fallback to monthly, then first available
      setSelectedPackage(
        yearlyPackage || monthlyPackage || offerings.availablePackages[0]
      );
    }
  }, [offerings]);

  const handleClose = () => {
    router.back();
  };

  const handlePurchase = async () => {
    if (!selectedPackage) return;

    try {
      setPurchasing(true);
      const success = await purchasePackage(selectedPackage);

      if (success) {
        // Call success callback if provided
        if (onPurchaseSuccess) {
          // Handle success callback
        }
        router.back();
      }
    } catch (error) {
      Alert.alert(
        t('subscription.purchaseError'),
        t('subscription.purchaseErrorMessage')
      );
    } finally {
      setPurchasing(false);
    }
  };

  const handleRestore = async () => {
    try {
      setPurchasing(true);
      const success = await restoreUserPurchases();

      if (success) {
        if (onPurchaseSuccess) {
          // Handle success callback
        }
        router.back();
      } else {
        Alert.alert(
          t('subscription.restoreError'),
          t('subscription.noRestorablePurchases')
        );
      }
    } catch (error) {
      Alert.alert(
        t('subscription.restoreError'),
        t('subscription.restoreErrorMessage')
      );
    } finally {
      setPurchasing(false);
    }
  };

  const formatPrice = (pkg: PurchasesPackage): string => {
    // Handle different RevenueCat SDK versions and missing products
    if ((pkg as any).storeProduct?.priceString) {
      return (pkg as any).storeProduct.priceString;
    }
    if ((pkg as any).product?.priceString) {
      return (pkg as any).product.priceString;
    }

    // Mock prices for development/testing when App Store products aren't configured
    // Use USD as fallback since we can't determine user's locale without RevenueCat
    if (
      pkg.identifier === '$rc_annual' ||
      pkg.identifier.includes('annual') ||
      pkg.identifier.includes('yearly')
    ) {
      return '$2.99';
    }
    if (
      pkg.identifier === '$rc_monthly' ||
      pkg.identifier.includes('monthly')
    ) {
      return '$0.99';
    }

    // Default fallback
    return '$0.99';
  };

  const calculateSavings = (): string | null => {
    if (!offerings?.availablePackages) return null;

    const monthlyPackage = offerings.availablePackages.find(
      (pkg) =>
        pkg.identifier === '$rc_monthly' || pkg.identifier.includes('monthly')
    );
    const yearlyPackage = offerings.availablePackages.find(
      (pkg) =>
        pkg.identifier === '$rc_annual' ||
        pkg.identifier === '$rc_yearly' ||
        pkg.identifier.includes('yearly') ||
        pkg.identifier.includes('annual')
    );

    if (!monthlyPackage || !yearlyPackage) return null;

    try {
      // Get prices from product (RevenueCat provides localized prices)
      let monthlyPrice = (monthlyPackage as any).product?.price;
      let yearlyPrice = (yearlyPackage as any).product?.price;

      // Use mock prices if real prices aren't available
      if (!monthlyPrice) {
        monthlyPrice = 0.99; // $0.99 fallback
      }
      if (!yearlyPrice) {
        yearlyPrice = 2.99; // $2.99 fallback
      }

      const yearlyMonthlyEquivalent = monthlyPrice * 12;
      const savings =
        ((yearlyMonthlyEquivalent - yearlyPrice) / yearlyMonthlyEquivalent) *
        100;

      return Math.round(savings).toString();
    } catch (error) {
      console.warn('Error calculating savings:', error);
      return '75'; // Default savings percentage
    }
  };

  const savings = calculateSavings();

  return (
    <>
      <StatusBar barStyle="dark-content" />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
            disabled={purchasing}
          >
            <Ionicons name="close" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>

        <View style={styles.scrollContainer}>
          <ScrollView
            contentContainerStyle={styles.content}
            showsVerticalScrollIndicator={false}
          >
            {/* Hero Section */}
            <View style={styles.heroSection}>
              <View style={styles.iconContainer}>
                <Image
                  source={require('../assets/images/doctorWithoutBg.png')}
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                  resizeMode="contain"
                />
              </View>
              <Text style={styles.title}>
                {t('subscription.freeTrialTitle')}
              </Text>
            </View>

            {/* Timeline */}
            <View style={styles.timelineSection}>
              <View style={styles.timelineItem}>
                <View style={[styles.timelineIcon, styles.timelineIconActive]}>
                  <Ionicons name="lock-open" size={16} color="#FFFFFF" />
                </View>
                <View style={styles.timelineContent}>
                  <Text style={styles.timelineTitle}>
                    {t('subscription.timelineNow')}
                  </Text>
                  <Text style={styles.timelineDescription}>
                    {t('subscription.timelineNowDesc')}
                  </Text>
                </View>
                <View style={styles.timelineConnector} />
              </View>

              <View style={styles.timelineItem}>
                <View
                  style={[styles.timelineIcon, styles.timelineIconUpcoming]}
                >
                  <Ionicons name="mail" size={16} color="#FFFFFF" />
                </View>
                <View style={styles.timelineContent}>
                  <Text style={styles.timelineTitle}>
                    {t('subscription.timelineDay5')}
                  </Text>
                  <Text style={styles.timelineDescription}>
                    {t('subscription.timelineDay5Desc')}
                  </Text>
                </View>
                <View style={styles.timelineConnector} />
              </View>

              <View style={styles.timelineItem}>
                <View style={[styles.timelineIcon, styles.timelineIconFuture]}>
                  <Ionicons name="star" size={16} color="#FFFFFF" />
                </View>
                <View style={styles.timelineContent}>
                  <Text style={styles.timelineTitle}>
                    {t('subscription.timelineDay7')}
                  </Text>
                  <Text style={styles.timelineDescription}>
                    {t('subscription.timelineDay7Desc')}
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>
        </View>

        {/* Fixed Footer */}
        <View style={styles.footer}>
          {/* Subscription Options */}
          {offerings?.availablePackages && (
            <View style={styles.packagesSection}>
              <View style={styles.packagesRow}>
                {offerings.availablePackages.map((pkg) => {
                  const isYearly =
                    pkg.identifier === '$rc_annual' ||
                    pkg.identifier === '$rc_yearly' ||
                    pkg.identifier.includes('yearly') ||
                    pkg.identifier.includes('annual');
                  const isSelected =
                    selectedPackage?.identifier === pkg.identifier;

                  // Calculate correct price display
                  const getDisplayPrice = () => {
                    if (isYearly) {
                      // For yearly: use pricePerMonthString if available, otherwise calculate
                      const pricePerMonthString = (pkg as any).product
                        ?.pricePerMonthString;
                      if (pricePerMonthString) {
                        return pricePerMonthString;
                      }

                      // Fallback: calculate monthly equivalent
                      const yearlyPrice = (pkg as any).product?.price || 2.99;
                      const monthlyEquivalent = (yearlyPrice / 12).toFixed(2);
                      return `$${monthlyEquivalent}`;
                    } else {
                      // For monthly: use direct price
                      return formatPrice(pkg);
                    }
                  };

                  return (
                    <View key={pkg.identifier} style={styles.packageContainer}>
                      {/* Savings Badge on top border for yearly */}
                      {isYearly && savings && (
                        <View style={styles.topSavingsBadge}>
                          <Text style={styles.topSavingsText}>
                            %{savings} {t('subscription.off')}
                          </Text>
                        </View>
                      )}

                      <TouchableOpacity
                        style={[
                          styles.packageOption,
                          isSelected && styles.packageOptionSelected,
                        ]}
                        onPress={() => setSelectedPackage(pkg)}
                        disabled={purchasing}
                      >
                        <View style={styles.packageHeader}>
                          <Text style={styles.packageTitle}>
                            {isYearly
                              ? t('subscription.yearly')
                              : t('subscription.monthly')}
                          </Text>
                          <View style={styles.radioButton}>
                            {isSelected && (
                              <View style={styles.radioButtonSelected} />
                            )}
                          </View>
                        </View>
                        <Text style={styles.packagePrice}>
                          {formatPrice(pkg)}/
                          {isYearly
                            ? t('subscription.perYear')
                            : t('subscription.perMonth')}
                        </Text>
                        <Text style={styles.packageSubtitle}>
                          {isYearly && (
                            <>
                              {getDisplayPrice()}/{t('subscription.mo')} •{' '}
                            </>
                          )}
                          {t('subscription.afterFreeTrial')}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  );
                })}
              </View>
            </View>
          )}

          {/* Purchase Button */}
          <TouchableOpacity
            style={[
              styles.purchaseButton,
              (!selectedPackage || purchasing) && styles.purchaseButtonDisabled,
            ]}
            onPress={handlePurchase}
            disabled={!selectedPackage || purchasing}
          >
            {purchasing ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <Text style={styles.purchaseButtonText}>
                {t('subscription.getStartedFree')}
              </Text>
            )}
          </TouchableOpacity>

          {/* Links */}
          <View style={styles.linksContainer}>
            <TouchableOpacity onPress={handleRestore}>
              <Text style={styles.linkText}>
                {t('subscription.restorePurchases')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                // Open Apple's standard EULA
                const url =
                  'https://www.apple.com/legal/internet-services/itunes/dev/stdeula';
                Linking.openURL(url);
              }}
            >
              <Text style={styles.linkText}>{t('subscription.terms')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                // Open privacy policy URL
                const url = 'https://additivesweb.vercel.app/privacy';
                Linking.openURL(url);
              }}
            >
              <Text style={styles.linkText}>{t('subscription.privacy')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  closeButton: {
    padding: 8,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 24,
    paddingBottom: 20,
  },
  heroSection: {
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 100,
    height: 70,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    overflow: 'hidden',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#2C2C2E',
    textAlign: 'center',
    lineHeight: 30,
  },

  // Timeline Styles
  timelineSection: {
    marginBottom: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    position: 'relative',
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    zIndex: 1,
  },
  timelineIconActive: {
    backgroundColor: '#4CAF50',
  },
  timelineIconUpcoming: {
    backgroundColor: '#FF9500',
  },
  timelineIconFuture: {
    backgroundColor: '#007AFF',
  },
  timelineContent: {
    flex: 1,
    paddingTop: 2,
  },
  timelineTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2E',
    marginBottom: 4,
  },
  timelineDescription: {
    fontSize: 14,
    color: '#6D6D70',
    lineHeight: 20,
  },
  timelineConnector: {
    position: 'absolute',
    left: 15,
    top: 32,
    width: 2,
    height: 24,
    backgroundColor: '#E5E5EA',
  },

  // Footer Styles
  footer: {
    backgroundColor: '#FFFFFF',
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 34, // Safe area bottom
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  packagesSection: {
    marginBottom: 20,
  },
  packagesRow: {
    flexDirection: 'row',
    gap: 12,
  },
  packageContainer: {
    flex: 1,
    position: 'relative',
  },
  topSavingsBadge: {
    position: 'absolute',
    top: -8,
    left: '50%',
    transform: [{ translateX: -25 }],
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1,
  },
  topSavingsText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  packageOption: {
    borderWidth: 2,
    borderColor: '#E5E5EA',
    borderRadius: 16,
    padding: 16,
    backgroundColor: '#FFFFFF',
    marginTop: 8, // Space for top badge
  },
  packageOptionSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#F0F8FF',
  },
  packageHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    position: 'relative',
  },
  packageTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C2C2E',
    flex: 1,
    textAlign: 'center',
  },
  radioButton: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#C7C7CC',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#007AFF',
  },
  packagePrice: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2C2C2E',
    textAlign: 'center',
    marginBottom: 4,
  },
  packageSubtitle: {
    fontSize: 11,
    color: '#8E8E93',
    lineHeight: 15,
    textAlign: 'center',
  },
  purchaseButton: {
    backgroundColor: '#007AFF',
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: 'center',
    marginBottom: 16,
  },
  purchaseButtonDisabled: {
    backgroundColor: '#C7C7CC',
  },
  purchaseButtonText: {
    fontSize: 17,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  linksContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  linkText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
});
