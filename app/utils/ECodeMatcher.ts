// Remove static import: import { eCodesAll, AdditiveFull } from '../constants/additives';
import { AdditiveFull } from '../lib/supabase'; // Import type from where it's defined now

// Trie node for efficient prefix matching
class TrieNode {
  children: Map<string, TrieNode>;
  isEndOfWord: boolean;
  code: string | null;

  constructor() {
    this.children = new Map();
    this.isEndOfWord = false;
    this.code = null;
  }
}

// Class for efficient E-code matching
export class ECodeMatcher {
  private trieRoot: TrieNode;
  private eCodesMap: Map<string, AdditiveFull>;
  private nameToCodeMap: Map<string, string>; // Maps lower-case full name to canonical E-code
  private tokenToNamesMap: Map<string, Set<string>>;
  private initialized: boolean = false;
  private allAdditives: AdditiveFull[] = []; // Store the additives internally

  // Constructor now accepts the additives data
  constructor(additivesData: AdditiveFull[]) {
    this.trieRoot = new TrieNode();
    this.eCodesMap = new Map();
    this.nameToCodeMap = new Map();
    this.tokenToNamesMap = new Map();
    this.allAdditives = additivesData; // Store the provided data
    this.initialize(); // Initialize with the provided data
  }

  // Initialize the data structures using the provided data
  private initialize(): void {
    if (
      this.initialized ||
      !this.allAdditives ||
      this.allAdditives.length === 0
    )
      return;

    // Build the trie for E-code matching
    for (const eCode of this.allAdditives) {
      // Use the stored data
      // Insert the code into the trie
      this.insertIntoTrie(eCode.code.toLowerCase(), eCode.code);

      // Also insert common variations
      this.insertIntoTrie(
        eCode.code.replace('E', '').toLowerCase(),
        eCode.code
      );
      this.insertIntoTrie(
        eCode.code.replace('E', 'e').toLowerCase(),
        eCode.code
      );
      this.insertIntoTrie(
        eCode.code.replace('E', 'e-').toLowerCase(),
        eCode.code
      );

      // Map the code to the full additive data
      this.eCodesMap.set(eCode.code, eCode);

      // Map names to codes for quick lookup
      this.nameToCodeMap.set(eCode.name.TR.toLowerCase(), eCode.code);
      const nameTRLower = eCode.name.TR.toLowerCase();
      const nameENLower = eCode.name.EN.toLowerCase();

      this.nameToCodeMap.set(nameTRLower, eCode.code);
      this.nameToCodeMap.set(nameENLower, eCode.code);

      // Populate tokenToNamesMap
      this.addNameToTokenMap(nameTRLower);
      this.addNameToTokenMap(nameENLower);
    }

    this.initialized = true;
  }

  // Helper to tokenize name and update tokenToNamesMap
  private addNameToTokenMap(name: string): void {
    // Simple tokenization: split by space, keep words >= 3 chars
    const tokens = name
      .split(/[\s-]+/) // Split by space or hyphen
      .filter((token) => token.length >= 3);

    for (const token of tokens) {
      if (!this.tokenToNamesMap.has(token)) {
        this.tokenToNamesMap.set(token, new Set());
      }
      this.tokenToNamesMap.get(token)!.add(name);
    }
  }

  // Insert a code into the trie (for E-numbers)
  private insertIntoTrie(str: string, code: string): void {
    let node = this.trieRoot;

    for (const char of str) {
      if (!node.children.has(char)) {
        node.children.set(char, new TrieNode());
      }
      node = node.children.get(char)!;
    }

    node.isEndOfWord = true;
    node.code = code;
  }

  // Search for E-codes in text using the trie (exact matching)
  private searchECodesInText(text: string): Set<string> {
    const foundCodes = new Set<string>();
    const lowerText = text.toLowerCase();

    // Use a sliding window approach to find all possible E-codes
    for (let i = 0; i < lowerText.length; i++) {
      let node = this.trieRoot;
      let j = i;

      while (j < lowerText.length && node.children.has(lowerText[j])) {
        node = node.children.get(lowerText[j])!;
        j++;

        if (node.isEndOfWord && node.code) {
          foundCodes.add(node.code);
        }
      }
    }

    return foundCodes;
  }

  // Calculate Levenshtein distance between two strings
  private levenshteinDistance(s1: string, s2: string): number {
    const len1 = s1.length;
    const len2 = s2.length;
    const matrix: number[][] = Array(len1 + 1)
      .fill(0)
      .map(() => Array(len2 + 1).fill(0));

    for (let i = 0; i <= len1; i++) matrix[i][0] = i;
    for (let j = 0; j <= len2; j++) matrix[0][j] = j;

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1, // Deletion
          matrix[i][j - 1] + 1, // Insertion
          matrix[i - 1][j - 1] + cost // Substitution
        );
      }
    }

    return matrix[len1][len2];
  }

  // Optimized search for additive names using token pre-filtering and fuzzy matching
  private searchAdditiveNames(text: string): Set<string> {
    const foundCodes = new Set<string>();
    const lowerText = text.toLowerCase();
    const textLength = lowerText.length;

    // 1. Tokenize input text and find candidate names
    const candidateNames = new Set<string>();
    const textTokens = lowerText.match(/\b[\w-]{3,}\b/g) || []; // Get words >= 3 chars

    for (const token of textTokens) {
      if (this.tokenToNamesMap.has(token)) {
        this.tokenToNamesMap
          .get(token)!
          .forEach((name) => candidateNames.add(name));
      }
    }

    // 2. Perform targeted Levenshtein distance check on candidate names
    const processedCodes = new Set<string>(); // Avoid redundant checks for the same code

    for (const name of candidateNames) {
      const code = this.nameToCodeMap.get(name);
      if (!code || processedCodes.has(code)) {
        continue; // Skip if code not found or already processed
      }

      const nameLength = name.length;
      // Increased flexibility: Allow 5 edits for names <= 10 chars, 6 edits for longer names
      const maxDistance = nameLength <= 8 ? 3 : 4; // Similarity threshold
      const lengthMargin = Math.max(2, Math.floor(nameLength * 0.2)); // Wider margin for longer names

      // Rough search window - can be refined further if needed
      const searchWindowStart = 0;
      const searchWindowEnd = textLength;

      // Iterate through potential starting positions in the text
      // This is still a loop, but only for candidate names
      for (
        let i = searchWindowStart;
        i <= searchWindowEnd - (nameLength - lengthMargin);
        i++
      ) {
        const subLength = Math.min(nameLength + lengthMargin, textLength - i);
        if (subLength < nameLength - lengthMargin) continue;

        const substring = lowerText.substring(i, i + subLength);

        // Check distance if lengths are reasonably close
        if (Math.abs(substring.length - nameLength) <= lengthMargin) {
          const distance = this.levenshteinDistance(name, substring);

          if (distance <= maxDistance) {
            foundCodes.add(code);
            processedCodes.add(code); // Mark code as processed
            break; // Found a match for this name, move to the next candidate name
          }
        }
      }
    }

    return foundCodes;
  }

  // Main function to find E-codes in text
  public findECodesInText(text: string): string[] {
    if (!text) return [];

    const foundCodes = new Set<string>();

    // 1. Find explicit E-codes (E###, E ###, E-###)
    const explicitRegex = /\bE\s*-?\s*(\d{3,4})\b/gi;
    let match;
    while ((match = explicitRegex.exec(text)) !== null) {
      const number = match[1];
      const canonicalCode = `E${number}`;
      if (this.eCodesMap.has(canonicalCode)) {
        foundCodes.add(canonicalCode);
      }
    }

    // 2. Find E-codes from additive names (approximate matching)
    const eCodesFromNames = this.searchAdditiveNames(text);
    for (const code of eCodesFromNames) {
      // Add codes from names only if they weren't found by number patterns
      if (!foundCodes.has(code)) {
        foundCodes.add(code);
      }
    }

    return Array.from(foundCodes);
  }

  // Get additive details by code
  public getAdditiveByCode(code: string): AdditiveFull | undefined {
    return this.eCodesMap.get(code);
  }

  // Removed getAllAdditives as data is managed externally now
}

// Remove singleton export - instance will be created elsewhere
// export const eCodeMatcher = new ECodeMatcher();
