import { eCodesAll } from '../constants/additives';
import { AdditiveFull } from '../lib/supabase';

/**
 * Service to provide additives data from the local constants file
 * Used by scan and history screens for matching E-codes without Supabase queries
 */
export class LocalAdditivesService {
  private static instance: LocalAdditivesService;
  private additives: AdditiveFull[];

  private constructor() {
    this.additives = eCodesAll;
  }

  public static getInstance(): LocalAdditivesService {
    if (!LocalAdditivesService.instance) {
      LocalAdditivesService.instance = new LocalAdditivesService();
    }
    return LocalAdditivesService.instance;
  }

  /**
   * Get all additives from the local constants file
   */
  public getAllAdditives(): AdditiveFull[] {
    return this.additives;
  }

  /**
   * Get an additive by its code
   */
  public getAdditiveByCode(code: string): AdditiveFull | undefined {
    return this.additives.find(additive => additive.code === code);
  }

  /**
   * Search additives by code or name
   */
  public searchAdditives(query: string): AdditiveFull[] {
    const lowerQuery = query.toLowerCase();
    return this.additives.filter(additive => 
      additive.code.toLowerCase().includes(lowerQuery) ||
      additive.name.TR.toLowerCase().includes(lowerQuery) ||
      additive.name.EN.toLowerCase().includes(lowerQuery) ||
      additive.categoryTr.toLowerCase().includes(lowerQuery) ||
      additive.categoryEn.toLowerCase().includes(lowerQuery)
    );
  }
}

// Export a singleton instance
export const localAdditivesService = LocalAdditivesService.getInstance();
