import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';

// Directory for storing scan images
const SCAN_IMAGES_DIR = `${FileSystem.documentDirectory}scanImages/`;

export interface StoredImageInfo {
  uri: string;
  type: 'barcode' | 'ingredient' | 'nutrition' | 'label';
  timestamp: number;
  filename: string;
  step?: number; // For multi-image scanning (1, 2, 3)
  purpose?: 'ingredients' | 'nutrition' | 'general'; // Purpose of the image
}

/**
 * Initialize the scan images directory
 */
export async function initializeImageStorage(): Promise<void> {
  try {
    const dirInfo = await FileSystem.getInfoAsync(SCAN_IMAGES_DIR);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(SCAN_IMAGES_DIR, {
        intermediates: true,
      });
      console.log('Created scan images directory');
    }
  } catch (error) {
    console.error('Error initializing image storage:', error);
  }
}

/**
 * Save an image to persistent storage
 * @param imageUri URI of the image to save
 * @param type Type of scan (barcode, ingredient, nutrition, or label)
 * @param scanId Unique identifier for the scan session
 * @param step Optional step number for multi-image scanning (1, 2, 3)
 * @param purpose Optional purpose of the image (ingredients, nutrition, general)
 * @returns Stored image info or null if failed
 */
export async function saveImageToStorage(
  imageUri: string,
  type: 'barcode' | 'ingredient' | 'nutrition' | 'label',
  scanId: string,
  step?: number,
  purpose?: 'ingredients' | 'nutrition' | 'general'
): Promise<StoredImageInfo | null> {
  try {
    // Ensure directory exists
    await initializeImageStorage();

    // Generate filename with step information if provided
    const timestamp = Date.now();
    const stepSuffix = step ? `_step${step}` : '';
    const filename = `${scanId}_${type}${stepSuffix}_${timestamp}.jpg`;
    const destinationUri = `${SCAN_IMAGES_DIR}${filename}`;

    // Compress and resize image to save storage space
    const manipulatedImage = await ImageManipulator.manipulateAsync(
      imageUri,
      [
        { resize: { width: 800 } }, // Resize to max width of 800px
      ],
      {
        compress: 0.8,
        format: ImageManipulator.SaveFormat.JPEG,
      }
    );

    // Copy the compressed image to our storage directory
    await FileSystem.copyAsync({
      from: manipulatedImage.uri,
      to: destinationUri,
    });

    // Clean up the temporary manipulated image
    await FileSystem.deleteAsync(manipulatedImage.uri, { idempotent: true });

    console.log(`Image saved to storage: ${filename}`);

    return {
      uri: destinationUri,
      type,
      timestamp,
      filename,
      step,
      purpose,
    };
  } catch (error) {
    console.error('Error saving image to storage:', error);
    return null;
  }
}

/**
 * Load an image from storage
 * @param filename Filename of the image to load
 * @returns Image URI or null if not found
 */
export async function loadImageFromStorage(
  filename: string
): Promise<string | null> {
  try {
    const imageUri = `${SCAN_IMAGES_DIR}${filename}`;
    const fileInfo = await FileSystem.getInfoAsync(imageUri);

    if (fileInfo.exists) {
      return imageUri;
    } else {
      console.warn(`Image not found: ${filename}`);
      return null;
    }
  } catch (error) {
    console.error('Error loading image from storage:', error);
    return null;
  }
}

/**
 * Delete an image from storage
 * @param filename Filename of the image to delete
 */
export async function deleteImageFromStorage(filename: string): Promise<void> {
  try {
    const imageUri = `${SCAN_IMAGES_DIR}${filename}`;
    await FileSystem.deleteAsync(imageUri, { idempotent: true });
    console.log(`Image deleted from storage: ${filename}`);
  } catch (error) {
    console.error('Error deleting image from storage:', error);
  }
}

/**
 * Clean up old images (older than 30 days)
 */
export async function cleanupOldImages(): Promise<void> {
  try {
    const dirInfo = await FileSystem.getInfoAsync(SCAN_IMAGES_DIR);
    if (!dirInfo.exists) return;

    const files = await FileSystem.readDirectoryAsync(SCAN_IMAGES_DIR);
    const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000;

    for (const filename of files) {
      try {
        // Extract timestamp from filename
        const timestampMatch = filename.match(/_(\d+)\.jpg$/);
        if (timestampMatch) {
          const timestamp = parseInt(timestampMatch[1]);
          if (timestamp < thirtyDaysAgo) {
            await deleteImageFromStorage(filename);
          }
        }
      } catch (error) {
        console.warn(`Error processing file ${filename}:`, error);
      }
    }
  } catch (error) {
    console.error('Error cleaning up old images:', error);
  }
}

/**
 * Get the total size of stored images
 * @returns Size in bytes
 */
export async function getStorageSize(): Promise<number> {
  try {
    const dirInfo = await FileSystem.getInfoAsync(SCAN_IMAGES_DIR);
    if (!dirInfo.exists) return 0;

    const files = await FileSystem.readDirectoryAsync(SCAN_IMAGES_DIR);
    let totalSize = 0;

    for (const filename of files) {
      try {
        const fileUri = `${SCAN_IMAGES_DIR}${filename}`;
        const fileInfo = await FileSystem.getInfoAsync(fileUri);
        if (fileInfo.exists && fileInfo.size) {
          totalSize += fileInfo.size;
        }
      } catch (error) {
        console.warn(`Error getting size for file ${filename}:`, error);
      }
    }

    return totalSize;
  } catch (error) {
    console.error('Error getting storage size:', error);
    return 0;
  }
}

/**
 * Delete images associated with a specific history item
 * @param images Array of StoredImageInfo to delete
 */
export async function deleteHistoryItemImages(
  images: StoredImageInfo[]
): Promise<void> {
  try {
    for (const imageInfo of images) {
      await deleteImageFromStorage(imageInfo.filename);
    }
    console.log(`Deleted ${images.length} images for history item`);
  } catch (error) {
    console.error('Error deleting history item images:', error);
  }
}

/**
 * Delete all stored images
 */
export async function clearAllImages(): Promise<void> {
  try {
    const dirInfo = await FileSystem.getInfoAsync(SCAN_IMAGES_DIR);
    if (dirInfo.exists) {
      await FileSystem.deleteAsync(SCAN_IMAGES_DIR, { idempotent: true });
      await initializeImageStorage(); // Recreate the directory
      console.log('All stored images cleared');
    }
  } catch (error) {
    console.error('Error clearing all images:', error);
  }
}
