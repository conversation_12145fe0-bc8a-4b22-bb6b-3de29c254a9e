import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import TextRecognition from '@react-native-ml-kit/text-recognition';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  analyzeImageForECodes,
  analyzeTextForECodes,
} from '../services/AIImageAnalyzer';
import { ImageAnalysisMode } from '../hooks/useDebugSettings';

// Cache for processed images
const imageCache = new Map<string, { text: string; timestamp: number }>();
const CACHE_EXPIRY = 1000 * 60 * 60; // 1 hour

// Helper function to delay execution
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Extract text from image using MLKit only
 * @param imageUri URI of the image to process
 * @param enhanceText Whether to apply text enhancement
 * @param useCache Whether to use cache
 * @returns Recognized text
 */
async function extractTextWithMLKit(
  imageUri: string,
  enhanceText: boolean,
  useCache: boolean
): Promise<string> {
  // Check cache first if enabled
  if (useCache) {
    const cachedResult = imageCache.get(imageUri);
    if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_EXPIRY) {
      return cachedResult.text;
    }
  }

  try {
    // Optimize the image for text recognition
    const processedImage = await optimizeImageForTextRecognition(
      imageUri,
      enhanceText
    );

    // Use ML Kit for text recognition
    const result = await TextRecognition.recognize(processedImage.uri);

    // Log the raw recognized text
    console.log('Raw recognized text from image (MLKit only):', result.text);

    // Cache the result if caching is enabled
    if (useCache) {
      imageCache.set(imageUri, {
        text: result.text,
        timestamp: Date.now(),
      });
    }

    // Clean up temporary file
    if (processedImage.uri !== imageUri) {
      await FileSystem.deleteAsync(processedImage.uri, { idempotent: true });
    }

    return result.text;
  } catch (error) {
    console.error('Error extracting text with MLKit:', error);
    throw error;
  }
}

export interface ImageProcessingOptions {
  enhanceText?: boolean;
  useCache?: boolean;
  useAI?: boolean; // New option for AI-based processing
  analysisMode?: ImageAnalysisMode; // Debug mode: 'direct' or 'textExtraction'
}

/**
 * Process an image and extract text from it
 * @param imageUri URI of the image to process
 * @param options Processing options
 * @returns Recognized text
 */
export async function processImage(
  imageUri: string,
  options: ImageProcessingOptions = {}
): Promise<string> {
  const {
    enhanceText = true,
    useCache = true,
    useAI = false,
    analysisMode = 'direct',
  } = options;

  // If AI processing is requested, use AI-based analysis
  if (useAI) {
    // Get language from AsyncStorage or default to English
    let language: 'en' | 'tr' = 'en';
    try {
      const savedLang = await AsyncStorage.getItem('userLanguage');
      if (savedLang === 'tr' || savedLang === 'en') {
        language = savedLang;
      }
    } catch (langError) {
      console.warn('Could not get language preference, using English');
    }

    // Retry logic: 5 attempts with 5-second intervals
    const maxRetries = 30;
    const retryDelay = 3000; // 5 seconds

    // Handle different analysis modes
    if (analysisMode === 'textExtraction') {
      // Debug mode: Extract text first, then analyze with AI
      console.log('Using text extraction + AI analysis mode');

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`Text extraction + AI attempt ${attempt}/${maxRetries}`);

          // First extract text using MLKit
          const extractedText = await extractTextWithMLKit(
            imageUri,
            enhanceText,
            useCache
          );
          console.log('Extracted text for AI analysis:', extractedText);

          // Then analyze the extracted text with AI
          const eCodes = await analyzeTextForECodes(extractedText, language);
          return eCodes.join(' '); // Return E-codes as space-separated string
        } catch (error) {
          console.error(
            `Text extraction + AI attempt ${attempt} failed:`,
            error
          );

          if (attempt === maxRetries) {
            console.error(
              'All text extraction + AI attempts failed, falling back to ML Kit'
            );
            // Fall back to ML Kit if all attempts fail
            break;
          }

          // Wait before next retry
          console.log(`Waiting ${retryDelay / 1000} seconds before retry...`);
          await delay(retryDelay);
        }
      }
    } else {
      // Default mode: Direct image analysis
      console.log('Using direct image analysis mode');

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`Direct AI processing attempt ${attempt}/${maxRetries}`);
          const eCodes = await analyzeImageForECodes(imageUri, language);
          return eCodes.join(' '); // Return E-codes as space-separated string
        } catch (error) {
          console.error(
            `Direct AI processing attempt ${attempt} failed:`,
            error
          );

          if (attempt === maxRetries) {
            console.error(
              'All direct AI processing attempts failed, falling back to ML Kit'
            );
            // Fall back to ML Kit if all attempts fail
            break;
          }

          // Wait before next retry
          console.log(`Waiting ${retryDelay / 1000} seconds before retry...`);
          await delay(retryDelay);
        }
      }
    }
  }

  // Check cache first if enabled
  if (useCache) {
    const cachedResult = imageCache.get(imageUri);
    if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_EXPIRY) {
      return cachedResult.text;
    }
  }

  try {
    // Optimize the image for text recognition
    const processedImage = await optimizeImageForTextRecognition(
      imageUri,
      enhanceText
    );

    // Use ML Kit for text recognition
    let result;
    try {
      // Use ML Kit for text recognition
      result = await TextRecognition.recognize(processedImage.uri);

      // Log the raw recognized text
      console.log('Raw recognized text from image:', result.text);
    } catch (error) {
      console.error('Error in text recognition:', error);
      throw error;
    }

    // Cache the result if caching is enabled
    if (useCache) {
      imageCache.set(imageUri, {
        text: result.text,
        timestamp: Date.now(),
      });
    }

    // Clean up temporary file
    if (processedImage.uri !== imageUri) {
      await FileSystem.deleteAsync(processedImage.uri, { idempotent: true });
    }

    return result.text;
  } catch (error) {
    console.error('Error processing image:', error);
    throw error;
  }
}

/**
 * Optimize an image for text recognition
 * @param imageUri URI of the image to optimize
 * @param enhanceText Whether to apply text enhancement
 * @returns Processed image URI
 */
async function optimizeImageForTextRecognition(
  imageUri: string,
  _enhanceText: boolean // Unused parameter, but kept for API compatibility
): Promise<{ uri: string }> {
  try {
    // Apply image manipulations to improve text recognition
    const operations: ImageManipulator.Action[] = [];

    // Resize large images to improve processing speed
    operations.push({ resize: { width: 1200 } });

    // Process the image
    const processedImage = await ImageManipulator.manipulateAsync(
      imageUri,
      operations,
      { format: ImageManipulator.SaveFormat.JPEG, compress: 1 }
    );

    return processedImage;
  } catch (error) {
    console.error('Error optimizing image:', error);
    // Return original image if processing fails
    return { uri: imageUri };
  }
}
