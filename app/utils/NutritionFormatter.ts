// Helper functions for formatting nutrition values

/**
 * Format energy values to show both kJ and kcal
 * @param nutriments - Nutrition data object
 * @returns Formatted energy string like "1700 kJ / 403 kcal"
 */
export const formatEnergyValue = (nutriments: any): string => {
  if (!nutriments) return '-';
  
  const energyKj = nutriments.energy_kj_100g;
  const energyKcal = nutriments.energy_kcal_100g;
  const energyGeneric = nutriments.energy_100g;
  
  // If we have both kJ and kcal, show both
  if (energyKj && energyKcal) {
    return `${energyKj} kJ / ${energyKcal} kcal`;
  }
  
  // If we only have kJ, show kJ and convert to kcal
  if (energyKj && !energyKcal) {
    const kcalConverted = Math.round(energyKj / 4.184);
    return `${energyKj} kJ / ${kcalConverted} kcal`;
  }
  
  // If we only have kcal, show kcal and convert to kJ
  if (energyKcal && !energyKj) {
    const kjConverted = Math.round(energyKcal * 4.184);
    return `${kjConverted} kJ / ${energyKcal} kcal`;
  }
  
  // Fallback to generic energy value (assume it's kcal)
  if (energyGeneric) {
    const kjConverted = Math.round(energyGeneric * 4.184);
    return `${kjConverted} kJ / ${energyGeneric} kcal`;
  }
  
  return '-';
};

/**
 * Convert kJ to kcal
 * @param kj - Energy value in kilojoules
 * @returns Energy value in kilocalories
 */
export const kjToKcal = (kj: number): number => {
  return Math.round(kj / 4.184);
};

/**
 * Convert kcal to kJ
 * @param kcal - Energy value in kilocalories
 * @returns Energy value in kilojoules
 */
export const kcalToKj = (kcal: number): number => {
  return Math.round(kcal * 4.184);
};

/**
 * Get energy value in kcal (for calculations)
 * @param nutriments - Nutrition data object
 * @returns Energy value in kcal or null if not available
 */
export const getEnergyInKcal = (nutriments: any): number | null => {
  if (!nutriments) return null;
  
  // Prefer kcal value if available
  if (nutriments.energy_kcal_100g) {
    return nutriments.energy_kcal_100g;
  }
  
  // Convert from kJ if available
  if (nutriments.energy_kj_100g) {
    return kjToKcal(nutriments.energy_kj_100g);
  }
  
  // Fallback to generic energy (assume kcal)
  if (nutriments.energy_100g) {
    return nutriments.energy_100g;
  }
  
  return null;
};

/**
 * Get energy value in kJ (for display)
 * @param nutriments - Nutrition data object
 * @returns Energy value in kJ or null if not available
 */
export const getEnergyInKj = (nutriments: any): number | null => {
  if (!nutriments) return null;
  
  // Prefer kJ value if available
  if (nutriments.energy_kj_100g) {
    return nutriments.energy_kj_100g;
  }
  
  // Convert from kcal if available
  if (nutriments.energy_kcal_100g) {
    return kcalToKj(nutriments.energy_kcal_100g);
  }
  
  // Convert from generic energy (assume kcal)
  if (nutriments.energy_100g) {
    return kcalToKj(nutriments.energy_100g);
  }
  
  return null;
};
