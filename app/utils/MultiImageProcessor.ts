import { processImage } from './ImageProcessor';
import { isAIAnalysisAvailable } from '../services/AIImageAnalyzer';
import { MultiImageScanResult } from '../hooks/useMultiImageScan';

export interface ProcessedImageData {
  imageUri: string;
  recognizedText: string;
  purpose: 'ingredients' | 'nutrition' | 'general';
  step: number;
  quality: 'good' | 'poor';
  confidence: number;
}

export interface CombinedAnalysisResult {
  combinedText: string;
  prioritizedText: string;
  ingredientsText: string;
  nutritionText: string;
  generalText: string;
  processedImages: ProcessedImageData[];
  analysisMetadata: {
    totalImages: number;
    goodQualityImages: number;
    hasIngredients: boolean;
    hasNutrition: boolean;
    hasGeneral: boolean;
    confidence: number;
  };
}

/**
 * Process multiple images and combine their text recognition results
 * with intelligent prioritization and cross-referencing
 */
export async function processMultipleImages(
  scanResult: MultiImageScanResult
): Promise<CombinedAnalysisResult> {
  try {
    const processedImages: ProcessedImageData[] = [];
    
    // Process each image and extract detailed information
    for (const stepResult of scanResult.stepResults) {
      const quality = assessTextQuality(stepResult.text);
      const confidence = calculateConfidence(stepResult.text, stepResult.purpose);
      
      processedImages.push({
        imageUri: stepResult.imageUri,
        recognizedText: stepResult.text,
        purpose: stepResult.purpose,
        step: stepResult.step,
        quality,
        confidence,
      });
    }

    // Separate text by purpose
    const ingredientsText = processedImages
      .filter(img => img.purpose === 'ingredients')
      .map(img => img.recognizedText)
      .join(' ');

    const nutritionText = processedImages
      .filter(img => img.purpose === 'nutrition')
      .map(img => img.recognizedText)
      .join(' ');

    const generalText = processedImages
      .filter(img => img.purpose === 'general')
      .map(img => img.recognizedText)
      .join(' ');

    // Create prioritized text (ingredients first, then nutrition, then general)
    const prioritizedText = [ingredientsText, nutritionText, generalText]
      .filter(text => text.trim().length > 0)
      .join(' ');

    // Cross-reference and validate findings
    const validatedText = crossReferenceFindings(processedImages);

    // Calculate overall analysis metadata
    const analysisMetadata = {
      totalImages: processedImages.length,
      goodQualityImages: processedImages.filter(img => img.quality === 'good').length,
      hasIngredients: ingredientsText.trim().length > 0,
      hasNutrition: nutritionText.trim().length > 0,
      hasGeneral: generalText.trim().length > 0,
      confidence: calculateOverallConfidence(processedImages),
    };

    return {
      combinedText: scanResult.combinedText,
      prioritizedText: validatedText || prioritizedText,
      ingredientsText,
      nutritionText,
      generalText,
      processedImages,
      analysisMetadata,
    };
  } catch (error) {
    console.error('Error processing multiple images:', error);
    throw error;
  }
}

/**
 * Assess the quality of recognized text
 */
function assessTextQuality(text: string): 'good' | 'poor' {
  if (!text || text.trim().length === 0) return 'poor';
  
  // Check for minimum length
  if (text.length < 10) return 'poor';
  
  // Check for meaningful content (letters and numbers)
  const meaningfulChars = text.match(/[a-zA-Z0-9]/g);
  if (!meaningfulChars || meaningfulChars.length < 5) return 'poor';
  
  // Check for too many special characters (indicates poor OCR)
  const specialChars = text.match(/[^a-zA-Z0-9\s.,;:()\-]/g);
  const specialCharRatio = specialChars ? specialChars.length / text.length : 0;
  if (specialCharRatio > 0.3) return 'poor';
  
  return 'good';
}

/**
 * Calculate confidence score for text based on its purpose
 */
function calculateConfidence(text: string, purpose: 'ingredients' | 'nutrition' | 'general'): number {
  if (!text || text.trim().length === 0) return 0;
  
  let confidence = 0.5; // Base confidence
  
  switch (purpose) {
    case 'ingredients':
      // Look for ingredient-related keywords
      const ingredientKeywords = [
        'ingredients', 'içindekiler', 'e-', 'e ', 'preservative', 'koruyucu',
        'additive', 'katkı', 'flour', 'un', 'sugar', 'şeker', 'salt', 'tuz',
        'oil', 'yağ', 'water', 'su'
      ];
      const foundIngredientKeywords = ingredientKeywords.filter(keyword => 
        text.toLowerCase().includes(keyword.toLowerCase())
      ).length;
      confidence += foundIngredientKeywords * 0.1;
      break;
      
    case 'nutrition':
      // Look for nutrition-related keywords
      const nutritionKeywords = [
        'calories', 'kalori', 'energy', 'enerji', 'fat', 'yağ', 'protein',
        'carbohydrate', 'karbonhidrat', 'sugar', 'şeker', 'sodium', 'sodyum',
        'per 100g', '100g', 'serving', 'porsiyon', 'kcal', 'kj'
      ];
      const foundNutritionKeywords = nutritionKeywords.filter(keyword => 
        text.toLowerCase().includes(keyword.toLowerCase())
      ).length;
      confidence += foundNutritionKeywords * 0.1;
      break;
      
    case 'general':
      // Look for general product information
      const generalKeywords = [
        'brand', 'marka', 'product', 'ürün', 'net', 'weight', 'ağırlık',
        'expiry', 'son', 'best before', 'tüketim', 'barcode', 'barkod'
      ];
      const foundGeneralKeywords = generalKeywords.filter(keyword => 
        text.toLowerCase().includes(keyword.toLowerCase())
      ).length;
      confidence += foundGeneralKeywords * 0.1;
      break;
  }
  
  // Adjust confidence based on text quality
  const quality = assessTextQuality(text);
  if (quality === 'poor') {
    confidence *= 0.5;
  }
  
  return Math.min(confidence, 1.0);
}

/**
 * Calculate overall confidence across all processed images
 */
function calculateOverallConfidence(processedImages: ProcessedImageData[]): number {
  if (processedImages.length === 0) return 0;
  
  const totalConfidence = processedImages.reduce((sum, img) => sum + img.confidence, 0);
  const averageConfidence = totalConfidence / processedImages.length;
  
  // Bonus for having multiple good quality images
  const goodQualityCount = processedImages.filter(img => img.quality === 'good').length;
  const qualityBonus = Math.min(goodQualityCount * 0.1, 0.3);
  
  // Bonus for having diverse image types
  const uniquePurposes = new Set(processedImages.map(img => img.purpose)).size;
  const diversityBonus = Math.min(uniquePurposes * 0.05, 0.15);
  
  return Math.min(averageConfidence + qualityBonus + diversityBonus, 1.0);
}

/**
 * Cross-reference findings between images to reduce false positives
 */
function crossReferenceFindings(processedImages: ProcessedImageData[]): string {
  // For now, return the prioritized text as-is
  // In the future, this could implement more sophisticated cross-referencing
  // such as validating E-codes found in ingredients against general label text
  
  const prioritizedImages = processedImages.sort((a, b) => {
    const purposeOrder = { ingredients: 0, nutrition: 1, general: 2 };
    return purposeOrder[a.purpose] - purposeOrder[b.purpose];
  });
  
  return prioritizedImages
    .filter(img => img.quality === 'good' && img.confidence > 0.3)
    .map(img => img.recognizedText)
    .join(' ');
}

/**
 * Extract E-codes specifically from ingredients text with higher accuracy
 */
export function extractECodesFromMultiImage(
  analysisResult: CombinedAnalysisResult
): string[] {
  const eCodes: Set<string> = new Set();
  
  // Prioritize ingredients text for E-code extraction
  if (analysisResult.ingredientsText) {
    const ingredientECodes = extractECodesFromText(analysisResult.ingredientsText);
    ingredientECodes.forEach(code => eCodes.add(code));
  }
  
  // Also check general text but with lower priority
  if (analysisResult.generalText) {
    const generalECodes = extractECodesFromText(analysisResult.generalText);
    generalECodes.forEach(code => eCodes.add(code));
  }
  
  return Array.from(eCodes);
}

/**
 * Extract E-codes from text using regex patterns
 */
function extractECodesFromText(text: string): string[] {
  const eCodes: Set<string> = new Set();
  
  // Common E-code patterns
  const patterns = [
    /E\s*(\d{3,4}[a-z]?)/gi,  // E123, E 123, E123a
    /E-(\d{3,4}[a-z]?)/gi,    // E-123
    /\bE(\d{3,4}[a-z]?)\b/gi, // E123 (word boundary)
  ];
  
  patterns.forEach(pattern => {
    const matches = text.matchAll(pattern);
    for (const match of matches) {
      const code = `E${match[1]}`;
      eCodes.add(code.toUpperCase());
    }
  });
  
  return Array.from(eCodes);
}

/**
 * Extract nutrition information from nutrition text
 */
export function extractNutritionFromMultiImage(
  analysisResult: CombinedAnalysisResult
): Record<string, number> {
  const nutrition: Record<string, number> = {};
  
  if (!analysisResult.nutritionText) return nutrition;
  
  const text = analysisResult.nutritionText.toLowerCase();
  
  // Extract common nutrition values
  const nutritionPatterns = {
    energy: /(?:energy|enerji|calories|kalori)[\s:]*(\d+(?:\.\d+)?)\s*(?:kcal|kj|cal)/i,
    fat: /(?:fat|yağ)[\s:]*(\d+(?:\.\d+)?)\s*g/i,
    sugar: /(?:sugar|şeker)[\s:]*(\d+(?:\.\d+)?)\s*g/i,
    salt: /(?:salt|tuz|sodium|sodyum)[\s:]*(\d+(?:\.\d+)?)\s*(?:g|mg)/i,
    protein: /(?:protein)[\s:]*(\d+(?:\.\d+)?)\s*g/i,
    carbohydrate: /(?:carbohydrate|karbonhidrat)[\s:]*(\d+(?:\.\d+)?)\s*g/i,
  };
  
  Object.entries(nutritionPatterns).forEach(([key, pattern]) => {
    const match = text.match(pattern);
    if (match && match[1]) {
      nutrition[key] = parseFloat(match[1]);
    }
  });
  
  return nutrition;
}
