import { AdditiveFull } from '../lib/supabase';
import { localAdditivesService } from './LocalAdditivesService';

// Trie node for efficient prefix matching
class TrieNode {
  children: Map<string, TrieNode>;
  isEndOfWord: boolean;
  code: string | null;

  constructor() {
    this.children = new Map();
    this.isEndOfWord = false;
    this.code = null;
  }
}

/**
 * ECodeMatcher that uses local additives data for scan and history screens
 */
export class LocalECodeMatcher {
  private static instance: LocalECodeMatcher;
  private trieRoot: TrieNode;
  private eCodesMap: Map<string, AdditiveFull>;
  private nameToCodeMap: Map<string, string>; // Maps lower-case full name to canonical E-code
  private slugToCodeMap: Map<string, string>; // Maps lower-case slug to canonical E-code
  private tokenToNamesMap: Map<string, Set<string>>;
  private initialized: boolean = false;
  private allAdditives: AdditiveFull[] = []; // Store the additives internally

  private constructor() {
    this.trieRoot = new TrieNode();
    this.eCodesMap = new Map();
    this.nameToCodeMap = new Map();
    this.slugToCodeMap = new Map();
    this.tokenToNamesMap = new Map();
    this.allAdditives = localAdditivesService.getAllAdditives();
    this.initialize();
  }

  public static getInstance(): LocalECodeMatcher {
    if (!LocalECodeMatcher.instance) {
      LocalECodeMatcher.instance = new LocalECodeMatcher();
    }
    return LocalECodeMatcher.instance;
  }

  // Initialize the data structures using the provided data
  private initialize(): void {
    if (
      this.initialized ||
      !this.allAdditives ||
      this.allAdditives.length === 0
    )
      return;

    // Build the trie for E-code matching
    for (const eCode of this.allAdditives) {
      // Insert the code into the trie
      this.insertIntoTrie(eCode.code.toLowerCase(), eCode.code);

      // Also insert common variations
      this.insertIntoTrie(
        eCode.code.replace('E', '').toLowerCase(),
        eCode.code
      );
      this.insertIntoTrie(
        eCode.code.replace('E', 'e').toLowerCase(),
        eCode.code
      );
      this.insertIntoTrie(
        eCode.code.replace('E', 'e-').toLowerCase(),
        eCode.code
      );

      // Map the code to the full additive data
      this.eCodesMap.set(eCode.code, eCode);

      // Map names to codes for quick lookup
      this.nameToCodeMap.set(eCode.name.TR.toLowerCase(), eCode.code);
      const nameTRLower = eCode.name.TR.toLowerCase();
      const nameENLower = eCode.name.EN.toLowerCase();

      this.nameToCodeMap.set(nameTRLower, eCode.code);
      this.nameToCodeMap.set(nameENLower, eCode.code);

      // Map slugs to codes for exact matching
      if (eCode.slugs && eCode.slugs.length > 0) {
        for (const slug of eCode.slugs) {
          this.slugToCodeMap.set(slug.toLowerCase(), eCode.code);
        }
      }

      // Populate tokenToNamesMap
      this.addNameToTokenMap(nameTRLower);
      this.addNameToTokenMap(nameENLower);
    }

    console.log(
      `Initialized with ${this.slugToCodeMap.size} alternative spellings (slugs)`
    );
    this.initialized = true;
  }

  // Helper to tokenize name and update tokenToNamesMap
  private addNameToTokenMap(name: string): void {
    // Simple tokenization: split by space, keep words >= 3 chars
    const tokens = name
      .split(/[\s-]+/) // Split by space or hyphen
      .filter((token) => token.length >= 3);

    for (const token of tokens) {
      if (!this.tokenToNamesMap.has(token)) {
        this.tokenToNamesMap.set(token, new Set());
      }
      this.tokenToNamesMap.get(token)!.add(name);
    }
  }

  // Insert a code into the trie (for E-numbers)
  private insertIntoTrie(str: string, code: string): void {
    let node = this.trieRoot;

    for (const char of str) {
      if (!node.children.has(char)) {
        node.children.set(char, new TrieNode());
      }
      node = node.children.get(char)!;
    }

    node.isEndOfWord = true;
    node.code = code;
  }

  // Search for E-codes in text using the trie (exact matching)
  private searchECodesInText(text: string): Set<string> {
    const foundCodes = new Set<string>();
    const lowerText = text.toLowerCase();

    // Use a sliding window approach to find all possible E-codes
    for (let i = 0; i < lowerText.length; i++) {
      let node = this.trieRoot;
      let j = i;

      while (j < lowerText.length && node.children.has(lowerText[j])) {
        node = node.children.get(lowerText[j])!;
        j++;

        if (node.isEndOfWord && node.code) {
          foundCodes.add(node.code);
        }
      }
    }

    return foundCodes;
  }

  // Search for exact matches with alternative spellings (slugs)
  private searchSlugsInText(text: string): Set<string> {
    const foundCodes = new Set<string>();
    const lowerText = text.toLowerCase();
    const slugMatches: Array<{ slug: string; code: string }> = [];

    // Check each slug for an exact match in the text
    for (const [slug, code] of this.slugToCodeMap.entries()) {
      if (lowerText.includes(slug)) {
        foundCodes.add(code);
        slugMatches.push({ slug, code });
      }
    }

    if (slugMatches.length > 0) {
      console.log('Exact matches found with alternative spellings:');
      slugMatches.forEach((match) => {
        console.log(`- Found "${match.slug}" → ${match.code}`);
      });
    }

    return foundCodes;
  }

  // Calculate Levenshtein distance between two strings
  private levenshteinDistance(s1: string, s2: string): number {
    const len1 = s1.length;
    const len2 = s2.length;
    const matrix: number[][] = Array(len1 + 1)
      .fill(0)
      .map(() => Array(len2 + 1).fill(0));

    for (let i = 0; i <= len1; i++) matrix[i][0] = i;
    for (let j = 0; j <= len2; j++) matrix[0][j] = j;

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1, // Deletion
          matrix[i][j - 1] + 1, // Insertion
          matrix[i - 1][j - 1] + cost // Substitution
        );
      }
    }

    return matrix[len1][len2];
  }

  // Optimized search for additive names using token pre-filtering and fuzzy matching
  private searchAdditiveNames(text: string): Set<string> {
    const foundCodes = new Set<string>();
    const lowerText = text.toLowerCase();
    const textLength = lowerText.length;

    console.log('Searching for additive names in text...');

    // 1. Tokenize input text and find candidate names
    const candidateNames = new Set<string>();
    const textTokens = lowerText.match(/\b[\w-]{3,}\b/g) || []; // Get words >= 3 chars

    console.log('Text tokens found:', textTokens);

    for (const token of textTokens) {
      if (this.tokenToNamesMap.has(token)) {
        this.tokenToNamesMap
          .get(token)!
          .forEach((name) => candidateNames.add(name));
      }
    }

    console.log(
      'Candidate additive names to check:',
      Array.from(candidateNames)
    );

    // 2. Perform targeted Levenshtein distance check on candidate names
    const processedCodes = new Set<string>(); // Avoid redundant checks for the same code
    const matchDetails: Array<{
      name: string;
      code: string;
      substring: string;
      distance: number;
    }> = [];

    for (const name of candidateNames) {
      const code = this.nameToCodeMap.get(name);
      if (!code || processedCodes.has(code)) {
        continue; // Skip if code not found or already processed
      }

      const nameLength = name.length;
      // Increased flexibility: Allow 5 edits for names <= 10 chars, 6 edits for longer names
      const generateMaxDistance = (nameLength: number) => {
        if (nameLength <= 8) return 3;
        if (nameLength <= 12) return 4;
        if (nameLength <= 15) return 5;
        if (nameLength <= 20) return 6;
        if (nameLength <= 24) return 8;
        if (nameLength <= 28) return 11;
        if (nameLength <= 32) return 13;
        return 5;
      };
      const maxDistance = generateMaxDistance(nameLength); // Similarity threshold
      const lengthMargin = Math.max(2, Math.floor(nameLength * 0.2)); // Wider margin for longer names

      // Rough search window - can be refined further if needed
      const searchWindowStart = 0;
      const searchWindowEnd = textLength;

      // Iterate through potential starting positions in the text
      // This is still a loop, but only for candidate names
      for (
        let i = searchWindowStart;
        i <= searchWindowEnd - (nameLength - lengthMargin);
        i++
      ) {
        const subLength = Math.min(nameLength + lengthMargin, textLength - i);
        if (subLength < nameLength - lengthMargin) continue;

        const substring = lowerText.substring(i, i + subLength);

        // Check distance if lengths are reasonably close
        if (Math.abs(substring.length - nameLength) <= lengthMargin) {
          console.log('Checking distance for:', name, substring);
          const distance = this.levenshteinDistance(name, substring);

          if (distance <= maxDistance) {
            foundCodes.add(code);
            processedCodes.add(code); // Mark code as processed
            matchDetails.push({
              name,
              code,
              substring,
              distance,
            });
            break; // Found a match for this name, move to the next candidate name
          }
        }
      }
    }

    if (matchDetails.length > 0) {
      console.log('Fuzzy matches found:');
      matchDetails.forEach((match) => {
        console.log(
          `- Match: "${match.substring}" ~= "${match.name}" (distance: ${match.distance}, code: ${match.code})`
        );
      });
    } else {
      console.log('No fuzzy matches found for additive names');
    }

    return foundCodes;
  }

  // Main function to find E-codes in text
  public findECodesInText(text: string): string[] {
    if (!text) return [];

    console.log(
      'Starting E-code search in text:',
      text.substring(0, 100) + (text.length > 100 ? '...' : '')
    );

    const foundCodes = new Set<string>();

    // 1. Find explicit E-codes (E###, E ###, E-###)
    const explicitRegex = /\bE\s*-?\s*(\d{3,4})\b/gi;
    let match;
    const explicitMatches: string[] = [];

    while ((match = explicitRegex.exec(text)) !== null) {
      const number = match[1];
      const canonicalCode = `E${number}`;
      if (this.eCodesMap.has(canonicalCode)) {
        foundCodes.add(canonicalCode);
        explicitMatches.push(canonicalCode);
      }
    }

    console.log('E-codes found by explicit pattern matching:', explicitMatches);

    // 2. Find E-codes from alternative spellings (exact matching with slugs)
    const eCodesFromSlugs = this.searchSlugsInText(text);
    const slugMatches: string[] = [];

    for (const code of eCodesFromSlugs) {
      if (!foundCodes.has(code)) {
        foundCodes.add(code);
        slugMatches.push(code);
      }
    }

    console.log('E-codes found by alternative spelling matching:', slugMatches);

    // 3. Find E-codes from additive names (approximate matching)
    const eCodesFromNames = this.searchAdditiveNames(text);
    const nameMatches: string[] = [];

    for (const code of eCodesFromNames) {
      // Add codes from names only if they weren't found by previous methods
      if (!foundCodes.has(code)) {
        foundCodes.add(code);
        nameMatches.push(code);
      }
    }

    console.log(
      'Additional E-codes found by fuzzy name matching:',
      nameMatches
    );
    console.log('Total E-codes found:', Array.from(foundCodes));

    return Array.from(foundCodes);
  }

  // Get additive details by code
  public getAdditiveByCode(code: string): AdditiveFull | undefined {
    return this.eCodesMap.get(code);
  }
}

// Export a singleton instance
export const localECodeMatcher = LocalECodeMatcher.getInstance();
