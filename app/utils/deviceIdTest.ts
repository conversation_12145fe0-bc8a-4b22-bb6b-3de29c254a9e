/**
 * Test utility for device ID persistence
 * This file helps test the persistent device ID system
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

const DEVICE_ID_KEY = '@health_consultation_device_id';
const REVENUECAT_USER_ID_KEY = '@health_consultation_revenuecat_user_id';

export const testDeviceIdPersistence = async () => {
  console.log('🧪 Testing Device ID Persistence...');
  
  try {
    // Clear existing data for clean test
    await AsyncStorage.removeItem(DEVICE_ID_KEY);
    await AsyncStorage.removeItem(REVENUECAT_USER_ID_KEY);
    console.log('🧹 Cleared existing device ID data');
    
    // Test 1: First time - should generate new ID
    console.log('\n📱 Test 1: First time device ID generation');
    const deviceId1 = await AsyncStorage.getItem(DEVICE_ID_KEY);
    console.log('Device ID (should be null):', deviceId1);
    
    // Test 2: After app restart - should use same ID
    console.log('\n🔄 Test 2: Simulating app restart');
    const deviceId2 = await AsyncStorage.getItem(DEVICE_ID_KEY);
    console.log('Device ID after restart:', deviceId2);
    
    // Test 3: Set RevenueCat user ID
    console.log('\n💰 Test 3: Setting RevenueCat user ID');
    const testUserId = 'test_user_123';
    await AsyncStorage.setItem(REVENUECAT_USER_ID_KEY, testUserId);
    
    const newDeviceId = `rc_${testUserId}`;
    await AsyncStorage.setItem(DEVICE_ID_KEY, newDeviceId);
    
    const deviceId3 = await AsyncStorage.getItem(DEVICE_ID_KEY);
    console.log('Device ID with RevenueCat:', deviceId3);
    
    console.log('✅ Device ID persistence test completed');
    
    return {
      firstTime: deviceId1,
      afterRestart: deviceId2,
      withRevenueCat: deviceId3,
    };
  } catch (error) {
    console.error('❌ Device ID test failed:', error);
    return null;
  }
};

export const getCurrentDeviceId = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem(DEVICE_ID_KEY);
  } catch (error) {
    console.error('Failed to get current device ID:', error);
    return null;
  }
};

export const clearDeviceIdData = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(DEVICE_ID_KEY);
    await AsyncStorage.removeItem(REVENUECAT_USER_ID_KEY);
    console.log('🧹 Cleared all device ID data');
  } catch (error) {
    console.error('Failed to clear device ID data:', error);
  }
};
