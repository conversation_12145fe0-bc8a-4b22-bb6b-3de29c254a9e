import { AdditiveFull } from '../lib/supabase';
import { IngredientAnalyzer, IngredientAnalysis } from './IngredientAnalyzer';
import {
  analyzeFood,
  AIFoodAnalysis,
  isAIFoodAnalysisAvailable,
} from '../services/AIFoodAnalyzer';
import { getEnergyInKcal } from './NutritionFormatter';

export interface FoodScore {
  overall: number; // 0-100 scale
  nutrition: number; // 0-100 scale
  breakdown: {
    additives: {
      score: number;
      weight: number;
      details: string[];
    };
    nutrition: {
      score: number;
      weight: number;
      details: string[];
    };
    processing: {
      score: number;
      weight: number;
      details: string[];
    };
    ingredients: {
      score: number;
      weight: number;
      details: string[];
    };
  };
  ingredientAnalysis?: IngredientAnalysis; // Optional ingredient analysis
  aiAnalysis?: AIFoodAnalysis; // Optional AI-powered analysis
  recommendation: string;
  colorCode: 'green' | 'yellow' | 'orange' | 'red';
  grade: 'A' | 'B' | 'C' | 'D' | 'E' | 'F';
}

export interface ProductNutrition {
  energy_100g?: number; // kcal
  proteins_100g?: number; // g
  carbohydrates_100g?: number; // g
  sugars_100g?: number; // g
  fat_100g?: number; // g
  saturated_fat_100g?: number; // g
  fiber_100g?: number; // g
  sodium_100g?: number; // mg
  salt_100g?: number; // g
}

export class FoodScoreCalculator {
  private static readonly WEIGHTS = {
    additives: 0.3, // 30% - E-codes and official additives
    nutrition: 0.3, // 30% - Nutritional quality
    processing: 0.2, // 20% - Processing level
    ingredients: 0.2, // 20% - Harmful ingredients analysis
  };

  private static readonly SAFETY_SCORES = {
    safe: 100,
    limited: 75,
    moderate: 50,
    high: 25,
  };

  /**
   * Calculate comprehensive food score based on additives, nutrition, and ingredients
   */
  public static async calculateFoodScore(
    foundECodes: string[],
    additives: AdditiveFull[],
    nutrition?: ProductNutrition,
    isOrganic: boolean = false,
    ingredientText?: string
  ): Promise<FoodScore> {
    // Get additive details
    const additiveDetails = foundECodes
      .map((code) => additives.find((a) => a.code === code))
      .filter(Boolean) as AdditiveFull[];

    // Analyze ingredients for harmful components
    const ingredientAnalysis = ingredientText
      ? IngredientAnalyzer.analyzeIngredients(ingredientText)
      : null;

    // Try to get AI-powered analysis if available
    let aiAnalysis: AIFoodAnalysis | null = null;
    try {
      const isAIAvailable = await isAIFoodAnalysisAvailable();
      if (
        isAIAvailable &&
        (nutrition || foundECodes.length > 0 || ingredientText)
      ) {
        // Get language preference
        let language: 'en' | 'tr' = 'en';
        try {
          const AsyncStorage = await import(
            '@react-native-async-storage/async-storage'
          );
          const savedLang = await AsyncStorage.default.getItem('userLanguage');
          if (savedLang === 'tr' || savedLang === 'en') {
            language = savedLang;
          }
        } catch (langError) {
          console.warn('Could not get language preference, using English');
        }

        console.log('Getting AI food analysis...');
        aiAnalysis = await analyzeFood(
          nutrition,
          foundECodes,
          ingredientText || '',
          language
        );
        console.log('AI analysis result:', aiAnalysis);
      }
    } catch (error) {
      console.error('Error getting AI food analysis:', error);
    }

    // Calculate individual scores
    const additiveScore = this.calculateAdditiveScore(additiveDetails);
    const nutritionScore = this.calculateNutritionScore(nutrition);
    const processingScore = this.calculateProcessingScore(
      foundECodes.length,
      isOrganic
    );
    const ingredientScore = this.calculateIngredientScore(ingredientAnalysis);

    // Use AI analysis if available, otherwise use traditional calculation
    let overall: number;
    let nutritionDisplay: number;
    let recommendation: string;
    let grade: 'A' | 'B' | 'C' | 'D' | 'E' | 'F';
    let colorCode: 'green' | 'yellow' | 'orange' | 'red';

    if (aiAnalysis) {
      // Use AI-powered nutrition scoring but calculate overall with weights
      nutritionDisplay = aiAnalysis.nutritionScore;
      recommendation = aiAnalysis.healthRecommendation;
      grade = aiAnalysis.nutritionGrade;

      // Calculate overall score using AI nutrition score with proper weights
      overall = Math.round(
        additiveScore.score * this.WEIGHTS.additives +
          aiAnalysis.nutritionScore * this.WEIGHTS.nutrition +
          processingScore.score * this.WEIGHTS.processing +
          ingredientScore.score * this.WEIGHTS.ingredients
      );

      colorCode = this.getColorCode(overall);

      // Update nutrition score details with AI insights
      nutritionScore.details =
        aiAnalysis.keyNutritionPoints || nutritionScore.details;
    } else {
      // Use traditional calculation
      overall = Math.round(
        additiveScore.score * this.WEIGHTS.additives +
          nutritionScore.score * this.WEIGHTS.nutrition +
          processingScore.score * this.WEIGHTS.processing +
          ingredientScore.score * this.WEIGHTS.ingredients
      );
      nutritionDisplay = nutritionScore.score;
      colorCode = this.getColorCode(overall);
      grade = this.getGrade(overall);
      recommendation = this.getRecommendation(overall, additiveDetails);
    }

    return {
      overall,
      nutrition: nutritionDisplay,
      breakdown: {
        additives: {
          score: additiveScore.score,
          weight: this.WEIGHTS.additives,
          details: additiveScore.details,
        },
        nutrition: {
          score: nutritionScore.score,
          weight: this.WEIGHTS.nutrition,
          details: nutritionScore.details,
        },
        processing: {
          score: processingScore.score,
          weight: this.WEIGHTS.processing,
          details: processingScore.details,
        },
        ingredients: {
          score: ingredientScore.score,
          weight: this.WEIGHTS.ingredients,
          details: ingredientScore.details,
        },
      },
      ingredientAnalysis,
      aiAnalysis,
      recommendation,
      colorCode,
      grade,
    };
  }

  /**
   * Calculate ingredient safety score based on harmful ingredients analysis
   */
  private static calculateIngredientScore(
    analysis: IngredientAnalysis | null
  ): {
    score: number;
    details: string[];
  } {
    if (!analysis) {
      return {
        score: 70, // Neutral score when no ingredient analysis
        details: ['Ingredient analysis not available'],
      };
    }

    const details: string[] = [];

    // Use the health score from ingredient analysis
    let score = analysis.healthScore;

    // Add details from the analysis
    if (analysis.concerns.length > 0) {
      details.push(...analysis.concerns);
    }

    if (analysis.positives.length > 0) {
      details.push(...analysis.positives.map((p) => `Contains ${p}`));
    }

    // Add processing level info
    details.push(`Processing level: ${analysis.processingLevel}`);

    // Add harmful ingredients count
    if (analysis.harmfulIngredients.length > 0) {
      const highRisk = analysis.harmfulIngredients.filter(
        (i) => i.riskLevel === 'high'
      ).length;
      const moderateRisk = analysis.harmfulIngredients.filter(
        (i) => i.riskLevel === 'moderate'
      ).length;

      if (highRisk > 0) {
        details.push(`${highRisk} high-risk ingredient(s) detected`);
      }
      if (moderateRisk > 0) {
        details.push(`${moderateRisk} moderate-risk ingredient(s) detected`);
      }
    } else {
      details.push('No harmful ingredients detected');
    }

    return { score, details };
  }

  /**
   * Calculate additive safety score
   */
  private static calculateAdditiveScore(additives: AdditiveFull[]): {
    score: number;
    details: string[];
  } {
    if (additives.length === 0) {
      return {
        score: 100,
        details: ['No additives detected - excellent!'],
      };
    }

    let totalScore = 0;
    const details: string[] = [];
    const safetyLevels = { safe: 0, limited: 0, moderate: 0, high: 0 };

    additives.forEach((additive) => {
      const safety = additive.safety_level as keyof typeof this.SAFETY_SCORES;
      const score = this.SAFETY_SCORES[safety] || 50;
      totalScore += score;
      safetyLevels[safety]++;
    });

    const averageScore = Math.round(totalScore / additives.length);

    // Generate details
    if (safetyLevels.high > 0) {
      details.push(`${safetyLevels.high} high-risk additive(s) detected`);
    }
    if (safetyLevels.moderate > 0) {
      details.push(
        `${safetyLevels.moderate} moderate-risk additive(s) detected`
      );
    }
    if (safetyLevels.limited > 0) {
      details.push(`${safetyLevels.limited} limited-risk additive(s) detected`);
    }
    if (safetyLevels.safe > 0) {
      details.push(`${safetyLevels.safe} safe additive(s) detected`);
    }

    return { score: averageScore, details };
  }

  /**
   * Calculate nutrition score based on Nutri-Score algorithm
   */
  private static calculateNutritionScore(nutrition?: ProductNutrition): {
    score: number;
    details: string[];
  } {
    if (!nutrition) {
      return {
        score: 70, // Neutral score when no nutrition data
        details: ['Nutrition information not available'],
      };
    }

    let score = 100; // Start with perfect score
    const details: string[] = [];

    // Negative points for unhealthy components
    const energy = getEnergyInKcal(nutrition) || 0;
    const sugars = nutrition.sugars_100g || 0;
    const saturatedFat = nutrition.saturated_fat_100g || 0;
    const sodium = nutrition.sodium_100g || 0;

    // Energy penalty (kcal per 100g)
    if (energy > 350) {
      score -= 15;
      details.push('High calorie content');
    } else if (energy > 250) {
      score -= 8;
      details.push('Moderate calorie content');
    }

    // Sugar penalty (g per 100g)
    if (sugars > 15) {
      score -= 20;
      details.push('High sugar content');
    } else if (sugars > 8) {
      score -= 10;
      details.push('Moderate sugar content');
    }

    // Saturated fat penalty (g per 100g)
    if (saturatedFat > 5) {
      score -= 15;
      details.push('High saturated fat content');
    } else if (saturatedFat > 2) {
      score -= 8;
      details.push('Moderate saturated fat content');
    }

    // Sodium penalty (mg per 100g)
    if (sodium > 600) {
      score -= 20;
      details.push('High sodium content');
    } else if (sodium > 300) {
      score -= 10;
      details.push('Moderate sodium content');
    }

    // Positive points for healthy components
    const proteins = nutrition.proteins_100g || 0;
    const fiber = nutrition.fiber_100g || 0;

    // Protein bonus
    if (proteins > 8) {
      score += 5;
      details.push('Good protein content');
    }

    // Fiber bonus
    if (fiber > 3) {
      score += 5;
      details.push('Good fiber content');
    }

    // Ensure score stays within bounds
    score = Math.max(0, Math.min(100, score));

    if (details.length === 0) {
      details.push('Balanced nutritional profile');
    }

    return { score: Math.round(score), details };
  }

  /**
   * Calculate processing score
   */
  private static calculateProcessingScore(
    additiveCount: number,
    isOrganic: boolean
  ): {
    score: number;
    details: string[];
  } {
    let score = 100;
    const details: string[] = [];

    // Penalty based on number of additives (processing indicator)
    if (additiveCount === 0) {
      details.push('Minimally processed');
    } else if (additiveCount <= 2) {
      score -= 10;
      details.push('Lightly processed');
    } else if (additiveCount <= 5) {
      score -= 25;
      details.push('Moderately processed');
    } else {
      score -= 40;
      details.push('Highly processed');
    }

    // Organic bonus
    if (isOrganic) {
      score += 10;
      details.push('Organic certification bonus');
    }

    return { score: Math.max(0, score), details };
  }

  /**
   * Get color code based on score
   */
  private static getColorCode(
    score: number
  ): 'green' | 'yellow' | 'orange' | 'red' {
    if (score >= 80) return 'green';
    if (score >= 60) return 'yellow';
    if (score >= 40) return 'orange';
    return 'red';
  }

  /**
   * Get letter grade based on score
   */
  private static getGrade(score: number): 'A' | 'B' | 'C' | 'D' | 'E' | 'F' {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    if (score >= 50) return 'E';
    return 'F';
  }

  /**
   * Get recommendation text based on score and additives
   */
  private static getRecommendation(
    score: number,
    additives: AdditiveFull[]
  ): string {
    const highRiskAdditives = additives.filter(
      (a) => a.safety_level === 'high'
    );

    if (highRiskAdditives.length > 0) {
      return 'Proceed with caution - contains high-risk additives';
    }

    if (score >= 80) {
      return 'Excellent choice - healthy and safe';
    } else if (score >= 60) {
      return 'Good choice - consume in moderation';
    } else if (score >= 40) {
      return 'Fair choice - consider healthier alternatives';
    } else {
      return 'Poor choice - avoid or consume rarely';
    }
  }
}
