/**
 * Ingredient Analyzer - Analyzes ingredient text for harmful components beyond E-codes
 * This analyzer looks for problematic ingredients like high sugar content, unhealthy fats,
 * artificial sweeteners, preservatives, and other concerning additives
 */

export interface IngredientAnalysis {
  harmfulIngredients: HarmfulIngredient[];
  healthScore: number; // 0-100 scale
  concerns: string[];
  positives: string[];
  processingLevel: 'minimal' | 'moderate' | 'high' | 'ultra-processed';
}

export interface HarmfulIngredient {
  name: string;
  category: 'sugar' | 'fat' | 'preservative' | 'artificial' | 'salt' | 'other';
  riskLevel: 'low' | 'moderate' | 'high';
  description: string;
}

export class IngredientAnalyzer {
  // Harmful ingredients database
  private static readonly HARMFUL_INGREDIENTS = {
    // High-risk artificial sweeteners
    artificial_sweeteners: {
      high: [
        'aspartam', 'aspartame', 'acesulfame k', 'acesulfame potassium',
        'sucralose', 'saccharin', 'neotame', 'advantame'
      ],
      moderate: [
        'sorbitol', 'mannitol', 'xylitol', 'erythritol', 'stevia'
      ]
    },

    // Preservatives and chemicals
    preservatives: {
      high: [
        'sodium benzoate', 'potassium benzoate', 'sodium nitrite', 'sodium nitrate',
        'bha', 'bht', 'tbhq', 'propyl gallate', 'calcium propionate'
      ],
      moderate: [
        'citric acid', 'ascorbic acid', 'tocopherols', 'sodium citrate'
      ]
    },

    // Unhealthy fats
    fats: {
      high: [
        'trans fat', 'partially hydrogenated', 'hydrogenated oil',
        'palm oil', 'palm kernel oil', 'coconut oil'
      ],
      moderate: [
        'sunflower oil', 'soybean oil', 'corn oil', 'canola oil'
      ]
    },

    // High sugar ingredients
    sugars: {
      high: [
        'high fructose corn syrup', 'corn syrup', 'glucose syrup',
        'dextrose', 'maltodextrin', 'sucrose', 'fructose'
      ],
      moderate: [
        'cane sugar', 'brown sugar', 'honey', 'maple syrup', 'agave'
      ]
    },

    // Artificial colors and flavors
    artificial: {
      high: [
        'artificial flavor', 'artificial flavoring', 'artificial color',
        'red 40', 'yellow 5', 'yellow 6', 'blue 1', 'blue 2'
      ],
      moderate: [
        'natural flavor', 'natural flavoring', 'caramel color'
      ]
    },

    // High sodium ingredients
    salt: {
      high: [
        'monosodium glutamate', 'msg', 'sodium chloride', 'sea salt',
        'sodium phosphate', 'sodium bicarbonate'
      ],
      moderate: [
        'salt', 'sodium'
      ]
    }
  };

  // Turkish translations for ingredients
  private static readonly TURKISH_INGREDIENTS = {
    // Yapay tatlandırıcılar
    'aspartam': 'aspartame',
    'sukraloz': 'sucralose',
    'sakarin': 'saccharin',
    'sorbitol': 'sorbitol',
    'ksilitol': 'xylitol',
    'stevia': 'stevia',

    // Koruyucular
    'sodyum benzoat': 'sodium benzoate',
    'potasyum benzoat': 'potassium benzoate',
    'sodyum nitrit': 'sodium nitrite',
    'sodyum nitrat': 'sodium nitrate',
    'sitrik asit': 'citric acid',
    'askorbik asit': 'ascorbic acid',

    // Yağlar
    'trans yağ': 'trans fat',
    'hidrojenize yağ': 'hydrogenated oil',
    'palm yağı': 'palm oil',
    'hindistan cevizi yağı': 'coconut oil',
    'ayçiçek yağı': 'sunflower oil',
    'soya yağı': 'soybean oil',
    'mısır yağı': 'corn oil',

    // Şekerler
    'mısır şurubu': 'corn syrup',
    'glukoz şurubu': 'glucose syrup',
    'dekstroz': 'dextrose',
    'maltodekstrin': 'maltodextrin',
    'sakkaroz': 'sucrose',
    'fruktoz': 'fructose',
    'kamış şekeri': 'cane sugar',
    'esmer şeker': 'brown sugar',
    'bal': 'honey',

    // Yapay maddeler
    'yapay aroma': 'artificial flavor',
    'yapay renk': 'artificial color',
    'doğal aroma': 'natural flavor',
    'karamel rengi': 'caramel color',

    // Tuz
    'monosodyum glutamat': 'monosodium glutamate',
    'sodyum klorür': 'sodium chloride',
    'deniz tuzu': 'sea salt',
    'tuz': 'salt'
  };

  /**
   * Analyze ingredient text for harmful components
   */
  public static analyzeIngredients(ingredientText: string): IngredientAnalysis {
    if (!ingredientText) {
      return {
        harmfulIngredients: [],
        healthScore: 70, // Neutral score when no ingredients
        concerns: ['No ingredient information available'],
        positives: [],
        processingLevel: 'minimal'
      };
    }

    const text = ingredientText.toLowerCase();
    const harmfulIngredients: HarmfulIngredient[] = [];
    const concerns: string[] = [];
    const positives: string[] = [];

    // Normalize Turkish characters
    const normalizedText = this.normalizeTurkishText(text);

    // Check for harmful ingredients
    this.checkHarmfulIngredients(normalizedText, harmfulIngredients, concerns);

    // Check for positive ingredients
    this.checkPositiveIngredients(normalizedText, positives);

    // Calculate processing level
    const processingLevel = this.calculateProcessingLevel(harmfulIngredients, normalizedText);

    // Calculate health score
    const healthScore = this.calculateHealthScore(harmfulIngredients, positives, processingLevel);

    return {
      harmfulIngredients,
      healthScore,
      concerns,
      positives,
      processingLevel
    };
  }

  /**
   * Normalize Turkish characters for better matching
   */
  private static normalizeTurkishText(text: string): string {
    return text
      .replace(/ç/g, 'c')
      .replace(/ğ/g, 'g')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ş/g, 's')
      .replace(/ü/g, 'u');
  }

  /**
   * Check for harmful ingredients in the text
   */
  private static checkHarmfulIngredients(
    text: string,
    harmfulIngredients: HarmfulIngredient[],
    concerns: string[]
  ): void {
    // Check each category of harmful ingredients
    Object.entries(this.HARMFUL_INGREDIENTS).forEach(([category, levels]) => {
      Object.entries(levels).forEach(([riskLevel, ingredients]) => {
        ingredients.forEach(ingredient => {
          if (text.includes(ingredient.toLowerCase())) {
            harmfulIngredients.push({
              name: ingredient,
              category: category as any,
              riskLevel: riskLevel as any,
              description: this.getIngredientDescription(ingredient, category, riskLevel)
            });
          }
        });
      });
    });

    // Check Turkish ingredient names
    Object.entries(this.TURKISH_INGREDIENTS).forEach(([turkish, english]) => {
      if (text.includes(turkish)) {
        // Find the category and risk level for this ingredient
        const found = this.findIngredientInfo(english);
        if (found) {
          harmfulIngredients.push({
            name: turkish,
            category: found.category,
            riskLevel: found.riskLevel,
            description: this.getIngredientDescription(english, found.category, found.riskLevel)
          });
        }
      }
    });

    // Add specific concerns based on found ingredients
    if (harmfulIngredients.some(i => i.category === 'artificial_sweeteners')) {
      concerns.push('Contains artificial sweeteners');
    }
    if (harmfulIngredients.some(i => i.category === 'preservatives' && i.riskLevel === 'high')) {
      concerns.push('Contains potentially harmful preservatives');
    }
    if (harmfulIngredients.some(i => i.category === 'fats' && i.riskLevel === 'high')) {
      concerns.push('Contains unhealthy fats');
    }
    if (harmfulIngredients.some(i => i.category === 'sugars' && i.riskLevel === 'high')) {
      concerns.push('Contains high amounts of processed sugars');
    }
  }

  /**
   * Check for positive ingredients
   */
  private static checkPositiveIngredients(text: string, positives: string[]): void {
    const positiveIngredients = [
      'whole grain', 'organic', 'natural', 'vitamin', 'mineral',
      'fiber', 'protein', 'omega-3', 'antioxidant', 'probiotic',
      'tam tahıl', 'organik', 'doğal', 'vitamin', 'mineral',
      'lif', 'protein', 'antioksidan', 'probiyotik'
    ];

    positiveIngredients.forEach(ingredient => {
      if (text.includes(ingredient)) {
        positives.push(ingredient);
      }
    });
  }

  /**
   * Calculate processing level based on ingredients
   */
  private static calculateProcessingLevel(
    harmfulIngredients: HarmfulIngredient[],
    text: string
  ): 'minimal' | 'moderate' | 'high' | 'ultra-processed' {
    const highRiskCount = harmfulIngredients.filter(i => i.riskLevel === 'high').length;
    const totalHarmfulCount = harmfulIngredients.length;

    // Check for ultra-processed indicators
    const ultraProcessedIndicators = [
      'artificial flavor', 'artificial color', 'high fructose corn syrup',
      'partially hydrogenated', 'modified starch', 'emulsifier'
    ];

    const hasUltraProcessedIndicators = ultraProcessedIndicators.some(indicator =>
      text.includes(indicator)
    );

    if (hasUltraProcessedIndicators || highRiskCount >= 3) {
      return 'ultra-processed';
    } else if (highRiskCount >= 2 || totalHarmfulCount >= 5) {
      return 'high';
    } else if (totalHarmfulCount >= 2) {
      return 'moderate';
    } else {
      return 'minimal';
    }
  }

  /**
   * Calculate health score based on analysis
   */
  private static calculateHealthScore(
    harmfulIngredients: HarmfulIngredient[],
    positives: string[],
    processingLevel: string
  ): number {
    let score = 100;

    // Deduct points for harmful ingredients
    harmfulIngredients.forEach(ingredient => {
      switch (ingredient.riskLevel) {
        case 'high':
          score -= 15;
          break;
        case 'moderate':
          score -= 8;
          break;
        case 'low':
          score -= 3;
          break;
      }
    });

    // Deduct points for processing level
    switch (processingLevel) {
      case 'ultra-processed':
        score -= 20;
        break;
      case 'high':
        score -= 15;
        break;
      case 'moderate':
        score -= 8;
        break;
      case 'minimal':
        score += 5;
        break;
    }

    // Add points for positive ingredients
    score += Math.min(positives.length * 3, 15);

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Find ingredient category and risk level
   */
  private static findIngredientInfo(ingredient: string): { category: string; riskLevel: string } | null {
    for (const [category, levels] of Object.entries(this.HARMFUL_INGREDIENTS)) {
      for (const [riskLevel, ingredients] of Object.entries(levels)) {
        if (ingredients.includes(ingredient)) {
          return { category, riskLevel };
        }
      }
    }
    return null;
  }

  /**
   * Get description for an ingredient
   */
  private static getIngredientDescription(ingredient: string, category: string, riskLevel: string): string {
    const descriptions: Record<string, string> = {
      'aspartame': 'Artificial sweetener linked to headaches and digestive issues',
      'high fructose corn syrup': 'Highly processed sweetener linked to obesity and diabetes',
      'trans fat': 'Unhealthy fat that increases heart disease risk',
      'sodium nitrite': 'Preservative that may form harmful compounds when heated',
      'artificial flavor': 'Synthetic flavoring with unknown long-term effects',
      'palm oil': 'Saturated fat that may raise cholesterol levels'
    };

    return descriptions[ingredient] || `${category} ingredient with ${riskLevel} risk level`;
  }
}
