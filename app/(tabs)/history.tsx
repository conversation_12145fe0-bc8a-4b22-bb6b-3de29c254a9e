import React, { useState, useCallback, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  SectionList, // Use SectionList
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect, useNavigation } from 'expo-router'; // Import useNavigation
import { useLocalization } from '../context/LocalizationContext';
import SwipeableHistoryItem from '../components/SwipeableHistoryItem'; // Import the swipeable component
import { Ionicons } from '@expo/vector-icons'; // For empty state icon
import ECodeModal from '../components/ECodeModal'; // Import ECodeModal component
import MultiImageViewer from '../components/MultiImageViewer'; // Import MultiImageViewer component
import {
  StoredImageInfo,
  initializeImageStorage,
  deleteHistoryItemImages,
} from '../utils/ImageStorage';
import { FoodScore } from '../utils/FoodScoreCalculator';
import { AIFoodAnalysis } from '../services/AIFoodAnalyzer';

const HISTORY_STORAGE_KEY = '@scanHistory';

type HistoryEntry = {
  id: string;
  timestamp: number;
  codes: string[];
  productName?: string | null;
  images?: StoredImageInfo[];
  foodScore?: FoodScore | null;
  aiAnalysis?: AIFoodAnalysis | null;
  nutritionData?: any;
};

// Define the structure for SectionList sections
type HistorySection = {
  title: string;
  data: HistoryEntry[];
};

// Helper function to check if a date is today
const isToday = (someDate: Date): boolean => {
  const today = new Date();
  return (
    someDate.getDate() === today.getDate() &&
    someDate.getMonth() === today.getMonth() &&
    someDate.getFullYear() === today.getFullYear()
  );
};

// Helper function to check if a date is yesterday
const isYesterday = (someDate: Date): boolean => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return (
    someDate.getDate() === yesterday.getDate() &&
    someDate.getMonth() === yesterday.getMonth() &&
    someDate.getFullYear() === yesterday.getFullYear()
  );
};

// Function to group history entries by date
const groupHistoryByDate = (
  history: HistoryEntry[],
  t: (key: string) => string // Pass translation function
): HistorySection[] => {
  const groups: { [key: string]: HistoryEntry[] } = {
    today: [],
    yesterday: [],
    older: [],
  };

  history.forEach((item) => {
    const itemDate = new Date(item.timestamp);
    if (isToday(itemDate)) {
      groups.today.push(item);
    } else if (isYesterday(itemDate)) {
      groups.yesterday.push(item);
    } else {
      groups.older.push(item);
    }
  });

  const sections: HistorySection[] = [];
  if (groups.today.length > 0) {
    sections.push({ title: t('history.today'), data: groups.today });
  }
  if (groups.yesterday.length > 0) {
    sections.push({ title: t('history.yesterday'), data: groups.yesterday });
  }
  if (groups.older.length > 0) {
    sections.push({ title: t('history.older'), data: groups.older });
  }

  return sections;
};

export default function HistoryScreen() {
  const { t, language } = useLocalization(); // Get language as well
  const navigation = useNavigation(); // Get navigation instance for header options
  const [history, setHistory] = useState<HistoryEntry[]>([]);

  // Set dynamic tab title and header options based on language
  useEffect(() => {
    navigation.setOptions({
      title: t('tabs.history'), // Use translation key for tab title
      headerTitle: t('common.scanHistory'), // Set header title explicitly
      headerTitleStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
      },
      headerStyle: {
        backgroundColor: '#fff',
        elevation: 2,
        shadowOpacity: 0.1,
        shadowRadius: 3,
        shadowOffset: { width: 0, height: 2 },
      },
    });
  }, [navigation, t, language]); // Update when language changes
  const [isLoading, setIsLoading] = useState(true);

  // State for ECodeModal
  const [foundECodes, setFoundECodes] = useState<string[]>([]);
  const [expandedCodes, setExpandedCodes] = useState<Set<string>>(new Set());
  const [product, setProduct] = useState<any>(null);
  const [foodScore, setFoodScore] = useState<FoodScore | null>(null);

  // State for MultiImageViewer
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [selectedImages, setSelectedImages] = useState<StoredImageInfo[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const loadHistory = useCallback(async () => {
    try {
      // Initialize image storage
      await initializeImageStorage();

      const historyJson = await AsyncStorage.getItem(HISTORY_STORAGE_KEY);
      const loadedHistory = historyJson ? JSON.parse(historyJson) : [];
      setHistory(loadedHistory);
    } catch (error) {
      console.error('Error loading history:', error);
      Alert.alert(t('history.loadErrorTitle'), t('history.loadErrorMessage'));
    } finally {
      setIsLoading(false);
    }
  }, [t]);

  // Load history when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadHistory();
    }, [loadHistory])
  );

  const deleteHistoryItem = useCallback(
    async (itemToDelete: HistoryEntry) => {
      try {
        // Delete associated images first
        if (itemToDelete.images && itemToDelete.images.length > 0) {
          await deleteHistoryItemImages(itemToDelete.images);
        }

        // Remove item from history
        const updatedHistory = history.filter(
          (item) => item.id !== itemToDelete.id
        );
        setHistory(updatedHistory);

        // Update AsyncStorage
        await AsyncStorage.setItem(
          HISTORY_STORAGE_KEY,
          JSON.stringify(updatedHistory)
        );

        console.log('History item deleted:', itemToDelete.id);
      } catch (error) {
        console.error('Error deleting history item:', error);
        Alert.alert(
          t('history.deleteErrorTitle'),
          t('history.deleteErrorMessage')
        );
      }
    },
    [history, t]
  );

  // Remove bulk delete functionality - individual delete is now available via swipe
  React.useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => null, // Remove bulk delete button
    });
  }, [navigation]);

  // Toggle code expansion in the modal
  const toggleExpand = useCallback((code: string) => {
    setExpandedCodes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(code)) newSet.delete(code);
      else newSet.add(code);
      return newSet;
    });
  }, []);

  // Close the modal and reset state
  const closeModal = useCallback(() => {
    console.log('Closing modal and resetting state');
    setFoundECodes([]);
    setExpandedCodes(new Set());
    setProduct(null);
    setFoodScore(null);
    setModalImages([]);
  }, []);

  // Handle image press - open full screen viewer directly
  const handleImagePress = useCallback(
    (images: StoredImageInfo[], initialIndex: number) => {
      console.log(
        'Image pressed, opening viewer:',
        images.length,
        'images, index:',
        initialIndex
      );
      setSelectedImages(images);
      setSelectedImageIndex(initialIndex);
      setImageViewerVisible(true);
    },
    []
  );

  // Close image viewer
  const closeImageViewer = useCallback(() => {
    console.log('Closing image viewer');
    setImageViewerVisible(false);
    setSelectedImages([]);
    setSelectedImageIndex(0);
  }, []);

  // State for modal images (separate from viewer images)
  const [modalImages, setModalImages] = useState<StoredImageInfo[]>([]);

  // Handle item press - show the ECodeModal with history item data
  const handleItemPress = useCallback((item: HistoryEntry) => {
    console.log('History item pressed:', item);
    // Set the found E-codes from the history item
    setFoundECodes(item.codes);
    // Set the food score from history
    setFoodScore(item.foodScore || null);
    // Set images for the modal
    setModalImages(item.images || []);
    // Always create a product object if we have any data to show
    // This ensures the modal opens even without E-codes
    if (
      item.productName ||
      item.nutritionData ||
      item.foodScore ||
      (item.images && item.images.length > 0)
    ) {
      setProduct({
        brands: item.productName || '',
        product_name_en: item.productName || '',
        // Use saved nutrition data or fallback to empty values
        nutriments: item.nutritionData || {
          energy_100g: '-',
          proteins_100g: '-',
          carbohydrates_100g: '-',
          fat_100g: '-',
        },
      });
    } else {
      setProduct(null);
    }
  }, []);

  // Memoize the grouped data
  const sections = useMemo(() => groupHistoryByDate(history, t), [history, t]);

  const renderItem = ({
    item,
    index,
    section,
  }: {
    item: HistoryEntry;
    index: number;
    section: any;
  }) => {
    // Check if this is the first item in the first section
    const isFirstItem = sections.indexOf(section) === 0 && index === 0;

    return (
      <SwipeableHistoryItem
        item={item}
        onPress={handleItemPress}
        onImagePress={handleImagePress}
        onDelete={deleteHistoryItem}
        isFirstItem={isFirstItem}
      />
    );
  };

  // Render section headers
  const renderSectionHeader = ({
    section: { title },
  }: {
    section: HistorySection;
  }) => (
    <View style={styles.sectionHeaderContainer}>
      <Text style={styles.sectionHeader}>{title}</Text>
      <View style={styles.sectionHeaderLine} />
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.centered}>
          <ActivityIndicator size="large" />
        </View>
      ) : sections.length === 0 ? (
        <View style={styles.centered}>
          <View style={styles.emptyIconContainer}>
            <Ionicons name="time-outline" size={48} color="#9CA3AF" />
          </View>
          <Text style={styles.emptyText}>{t('history.empty')}</Text>
          <Text style={styles.emptySubtext}>
            {language === 'tr'
              ? `${t('scan.scanText')} ile başlayın`
              : `Start by using ${t('scan.scanText')}`}
          </Text>
        </View>
      ) : (
        <SectionList
          sections={sections}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          renderSectionHeader={renderSectionHeader}
          contentContainerStyle={styles.listContent}
          stickySectionHeadersEnabled={false} // Optional: make headers non-sticky
        />
      )}

      {/* E-Code Modal */}
      <ECodeModal
        foundECodes={foundECodes}
        expandedCodes={expandedCodes}
        product={product}
        foodScore={foodScore}
        images={modalImages}
        toggleExpand={toggleExpand}
        closeModal={closeModal}
        onImagePress={handleImagePress}
      />

      {/* Multi Image Viewer Modal */}
      <MultiImageViewer
        visible={imageViewerVisible}
        images={selectedImages}
        initialIndex={selectedImageIndex}
        onClose={closeImageViewer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    color: '#374151',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '600',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    fontWeight: '400',
  },
  listContent: {
    paddingTop: 16,
    paddingBottom: 32,
  },
  sectionHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#F5F7FA',
    marginBottom: 8,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '700',
    color: '#374151',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  sectionHeaderLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E5E7EB',
    marginLeft: 12,
  },
});
