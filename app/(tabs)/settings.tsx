import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Linking,
  Share,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Constants from 'expo-constants';
import { useLocalization } from '../context/LocalizationContext';
import { useSubscription } from '../context/SubscriptionContext';
import { useFavorites } from '../context/FavoritesContext';
import { useAvoidedAdditives } from '../context/AvoidedAdditivesContext';
import { useCustomLabels } from '../context/CustomLabelsContext';
import { useRouter } from 'expo-router';

import { resetEngagementData } from '../services/userEngagementService';

export default function SettingsScreen() {
  const { t, language, setLanguage } = useLocalization();
  const {
    subscriptionStatus,
    usageStats,
    restoreUserPurchases,
    refreshSubscriptionStatus,
  } = useSubscription();
  const { favorites } = useFavorites();
  const { avoidedAdditives } = useAvoidedAdditives();
  const { labels } = useCustomLabels();
  const router = useRouter();

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'tr' ? 'tr-TR' : 'en-US');
  };

  const handleLanguageChange = (lang: 'tr' | 'en') => {
    setLanguage(lang);
  };

  const handleRestorePurchases = async () => {
    try {
      const success = await restoreUserPurchases();
      if (success) {
        Alert.alert(
          t('settings.restorePurchases'),
          t('settings.purchasesRestored')
        );
        await refreshSubscriptionStatus();
      } else {
        Alert.alert(
          t('subscription.restoreError'),
          t('subscription.noRestorablePurchases')
        );
      }
    } catch (error) {
      Alert.alert(
        t('subscription.restoreError'),
        t('subscription.restoreErrorMessage')
      );
    }
  };

  const handleManageSubscription = () => {
    // Open App Store subscription management
    const url = 'https://apps.apple.com/account/subscriptions';
    Linking.openURL(url);
  };

  const handleContactSupport = () => {
    const email = '<EMAIL>';
    const subject = t('settings.supportSubject');
    const appVersion = Constants.expoConfig?.version || '1.0.0';
    const body = `App Version: ${appVersion}\nDevice: ${Platform.OS}\nLanguage: ${language}`;
    const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(
      subject
    )}&body=${encodeURIComponent(body)}`;
    Linking.openURL(mailtoUrl);
  };

  const handleRateApp = () => {
    // Open App Store rating page
    const appStoreUrl = 'https://apps.apple.com/app/id123456789'; // Replace with actual App Store ID
    Linking.openURL(appStoreUrl);
  };

  const handleShareApp = async () => {
    try {
      await Share.share({
        message: t('settings.shareMessage'),
        url: 'https://apps.apple.com/app/id123456789', // Replace with actual App Store URL
      });
    } catch (error) {
      console.error('Error sharing app:', error);
    }
  };

  const handleTermsOfService = () => {
    const url =
      'https://www.apple.com/legal/internet-services/itunes/dev/stdeula';
    Linking.openURL(url);
  };

  const handlePrivacyPolicy = () => {
    const url = 'https://additivesweb.vercel.app/privacy';
    Linking.openURL(url);
  };

  const renderSectionHeader = (title: string) => (
    <Text style={styles.sectionHeader}>{title}</Text>
  );

  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightElement?: React.ReactNode,
    iconColor?: string,
    iconBackground?: string
  ) => (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      <View
        style={[
          styles.iconContainer,
          { backgroundColor: iconBackground || '#F0F0F0' },
        ]}
      >
        <Ionicons name={icon as any} size={20} color={iconColor || '#666'} />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {rightElement ||
        (onPress && (
          <Ionicons name="chevron-forward" size={18} color="#C7C7CC" />
        ))}
    </TouchableOpacity>
  );

  const renderLanguageSelector = () => (
    <View style={styles.languageContainer}>
      <TouchableOpacity
        style={[
          styles.languageOption,
          language === 'tr' && styles.activeLanguage,
        ]}
        onPress={() => handleLanguageChange('tr')}
      >
        <Text
          style={[
            styles.languageText,
            language === 'tr' && styles.activeLanguageText,
          ]}
        >
          Türkçe
        </Text>
        {language === 'tr' && (
          <Ionicons name="checkmark" size={16} color="#007AFF" />
        )}
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.languageOption,
          language === 'en' && styles.activeLanguage,
        ]}
        onPress={() => handleLanguageChange('en')}
      >
        <Text
          style={[
            styles.languageText,
            language === 'en' && styles.activeLanguageText,
          ]}
        >
          English
        </Text>
        {language === 'en' && (
          <Ionicons name="checkmark" size={16} color="#007AFF" />
        )}
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Subscription Section */}
        {renderSectionHeader(t('settings.subscription'))}
        <View style={styles.section}>
          {/* Subscription Status */}
          {renderSettingItem(
            subscriptionStatus.isPremium ? 'star' : 'star-outline',
            t('settings.subscriptionStatus'),
            subscriptionStatus.isPremium
              ? `${t('settings.subscriptionActive')} - ${t('settings.premium')}`
              : t('settings.free'),
            undefined,
            <View
              style={[
                styles.statusBadge,
                {
                  backgroundColor: subscriptionStatus.isPremium
                    ? '#34C759'
                    : '#FF9500',
                },
              ]}
            >
              <Text style={styles.statusText}>
                {subscriptionStatus.isPremium
                  ? t('settings.premium')
                  : t('settings.free')}
              </Text>
            </View>,
            subscriptionStatus.isPremium ? '#FFD700' : '#666',
            subscriptionStatus.isPremium ? '#FFF8DC' : '#F0F0F0'
          )}

          {/* Subscription Details */}
          {subscriptionStatus.isPremium &&
            subscriptionStatus.expirationDate &&
            renderSettingItem(
              'calendar',
              subscriptionStatus.willRenew
                ? t('settings.subscriptionRenews')
                : t('settings.subscriptionExpires'),
              formatDate(subscriptionStatus.expirationDate),
              undefined,
              undefined,
              '#007AFF',
              '#E3F2FD'
            )}

          {/* Usage Stats */}
          {!subscriptionStatus.isPremium && (
            <>
              {renderSettingItem(
                'chatbubbles',
                t('settings.conversations'),
                `${usageStats.conversationsUsed}/${
                  usageStats.conversationsLimit
                } ${t('settings.used')}`,
                undefined,
                <Text style={styles.usageText}>
                  {usageStats.conversationsRemaining} {t('settings.remaining')}
                </Text>,
                '#FF6B6B',
                '#FFE5E5'
              )}
              {renderSettingItem(
                'text',
                t('settings.messages'),
                `${Object.values(usageStats.messagesPerConversation).reduce(
                  (a, b) => a + b,
                  0
                )} ${t('settings.used')}`,
                undefined,
                undefined,
                '#4ECDC4',
                '#E0F7FA'
              )}
            </>
          )}

          {/* Subscription Actions */}
          {!subscriptionStatus.isPremium
            ? renderSettingItem(
                'arrow-up-circle',
                t('settings.upgradeSubscription'),
                t('settings.upgradeDescription'),
                () => router.push('/paywall'),
                undefined,
                '#007AFF',
                '#E3F2FD'
              )
            : renderSettingItem(
                'settings',
                t('settings.manageSubscription'),
                t('settings.manageDescription'),
                handleManageSubscription,
                undefined,
                '#007AFF',
                '#E3F2FD'
              )}

          {renderSettingItem(
            'refresh',
            t('settings.restorePurchases'),
            t('settings.restoreDescription'),
            handleRestorePurchases,
            undefined,
            '#34C759',
            '#E8F5E8'
          )}
        </View>

        {/* Language Section */}
        {renderSectionHeader(t('settings.language'))}
        <View style={styles.section}>{renderLanguageSelector()}</View>

        {/* Personalization Section */}
        {renderSectionHeader(t('settings.personalization'))}
        <View style={styles.section}>
          {renderSettingItem(
            'heart',
            t('common.favorites'),
            `${favorites.size} ${t('common.items')}`,
            () => router.push('/favorites'),
            undefined,
            '#FF6B6B',
            '#FFE5E5'
          )}
          {renderSettingItem(
            'close-circle',
            t('settings.avoidedAdditives'),
            `${avoidedAdditives.size} ${t('common.items')}`,
            () => router.push('/avoided'),
            undefined,
            '#FF9500',
            '#FFF3E0'
          )}
          {renderSettingItem(
            'pricetag',
            t('settings.customLabels'),
            `${labels.length} ${t('common.items')}`,
            () => router.push('/labels'),
            undefined,
            '#9C27B0',
            '#F3E5F5'
          )}
        </View>

        {/* Support Section */}
        {renderSectionHeader(t('settings.support'))}
        <View style={styles.section}>
          {renderSettingItem(
            'mail',
            t('settings.contactSupport'),
            t('settings.contactDescription'),
            handleContactSupport,
            undefined,
            '#007AFF',
            '#E3F2FD'
          )}
          {renderSettingItem(
            'star',
            t('settings.rateApp'),
            t('settings.rateDescription'),
            handleRateApp,
            undefined,
            '#FFD700',
            '#FFF8DC'
          )}
          {renderSettingItem(
            'share',
            t('settings.shareApp'),
            t('settings.shareDescription'),
            handleShareApp,
            undefined,
            '#34C759',
            '#E8F5E8'
          )}
        </View>

        {/* About Section */}
        {renderSectionHeader(t('settings.about'))}
        <View style={styles.section}>
          {renderSettingItem(
            'document-text',
            t('settings.termsOfService'),
            t('settings.termsDescription'),
            handleTermsOfService,
            undefined,
            '#666',
            '#F0F0F0'
          )}
          {renderSettingItem(
            'shield-checkmark',
            t('settings.privacyPolicy'),
            t('settings.privacyDescription'),
            handlePrivacyPolicy,
            undefined,
            '#666',
            '#F0F0F0'
          )}
          {renderSettingItem(
            'information-circle',
            t('settings.version'),
            Constants.expoConfig?.version || '1.0.0',
            undefined,
            undefined,
            '#666',
            '#F0F0F0'
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollView: {
    flex: 1,
  },
  sectionHeader: {
    fontSize: 13,
    fontWeight: '600',
    color: '#8E8E93',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginTop: 32,
    marginBottom: 8,
    marginHorizontal: 20,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E5E5EA',
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2C2C2E',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 13,
    color: '#8E8E93',
    lineHeight: 16,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  usageText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#34C759',
  },
  languageContainer: {
    flexDirection: 'row',
    padding: 16,
  },
  languageOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    marginHorizontal: 4,
  },
  activeLanguage: {
    borderColor: '#007AFF',
    backgroundColor: '#F0F8FF',
  },
  languageText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#2C2C2E',
    marginRight: 8,
  },
  activeLanguageText: {
    color: '#007AFF',
  },
});
