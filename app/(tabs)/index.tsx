import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Animated,
  Image,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useLocalization } from '../context/LocalizationContext';
import { useSubscription } from '../context/SubscriptionContext';

import {
  getDefaultQuickTopics,
  HealthConversation,
} from '../types/healthConsultation';
import {
  getHealthConversations,
  getConversationMessageCount,
  deleteHealthConversation,
  updateHealthConversation,
} from '../services/HealthConsultationSupabase';
import { getUnifiedDeviceId } from '../services/revenueCatService';
import ConversationCard from '../components/health/ConversationCard';

export default function HealthConsultationScreen() {
  const { t } = useLocalization();
  const router = useRouter();
  const { checkPaywallCondition, subscriptionStatus } = useSubscription();

  // Local state
  const [conversations, setConversations] = useState<HealthConversation[]>([]);
  const [messageCounts, setMessageCounts] = useState<Record<string, number>>(
    {}
  );

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  // Get screen width for responsive design
  const screenWidth = Dimensions.get('window').width;

  // Load conversations on mount
  useEffect(() => {
    loadConversations();

    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const loadConversations = async () => {
    try {
      const deviceId = await getUnifiedDeviceId();
      const data = await getHealthConversations(deviceId);
      setConversations(data);

      // Load message counts for each conversation
      const counts: Record<string, number> = {};
      await Promise.all(
        data.map(async (conversation) => {
          const count = await getConversationMessageCount(conversation.id);
          counts[conversation.id] = count;
        })
      );
      setMessageCounts(counts);
    } catch (error) {
      console.error('Failed to load conversations:', error);
    }
  };

  const handleStartNewConsultation = async () => {
    try {
      // Check paywall condition for premium users
      if (!subscriptionStatus.isPremium) {
        const shouldShowPaywallModal = await checkPaywallCondition(
          'create_conversation'
        );
        if (shouldShowPaywallModal) {
          router.push('/paywall');
          return;
        }
      }

      // Navigate to chat without creating conversation yet
      router.push('/health-chat');
    } catch (error) {
      console.error('Failed to navigate to chat:', error);
    }
  };

  const handleQuickTopicPress = async (
    topic: ReturnType<typeof getDefaultQuickTopics>[0]
  ) => {
    try {
      // Check paywall condition for premium users
      if (!subscriptionStatus.isPremium) {
        const shouldShowPaywallModal = await checkPaywallCondition(
          'create_conversation'
        );
        if (shouldShowPaywallModal) {
          router.push('/paywall');
          return;
        }
      }

      // Navigate to chat with topic info, without creating conversation yet
      router.push(
        `/health-chat?topicId=${topic.id}&topicTitle=${encodeURIComponent(
          t(topic.titleKey)
        )}`
      );
    } catch (error) {
      console.error('Failed to navigate to chat with topic:', error);
    }
  };

  const handleConversationPress = (conversation: HealthConversation) => {
    // Directly open the conversation chat
    router.push(`/health-chat?conversationId=${conversation.id}`);
  };

  const handleDeleteConversation = useCallback(
    async (conversation: HealthConversation) => {
      Alert.alert(
        t('healthConsultation.history.deleteTitle'),
        t('healthConsultation.history.deleteMessage'),
        [
          {
            text: t('common.cancel'),
            style: 'cancel',
          },
          {
            text: t('common.delete'),
            style: 'destructive',
            onPress: async () => {
              try {
                await deleteHealthConversation(conversation.id);
                // Remove from local state
                setConversations((prev) =>
                  prev.filter((c) => c.id !== conversation.id)
                );
              } catch (error) {
                console.error('Error deleting conversation:', error);
                Alert.alert(
                  t('common.error'),
                  t('healthConsultation.history.deleteError')
                );
              }
            },
          },
        ]
      );
    },
    [t]
  );

  const handleArchiveConversation = useCallback(
    async (conversation: HealthConversation) => {
      try {
        const newArchivedStatus = !conversation.isArchived;
        await updateHealthConversation(conversation.id, {
          isArchived: newArchivedStatus,
        });

        // Update local state
        setConversations((prev) =>
          prev.map((c) =>
            c.id === conversation.id
              ? { ...c, isArchived: newArchivedStatus }
              : c
          )
        );
      } catch (error) {
        console.error('Error archiving/unarchiving conversation:', error);
        Alert.alert(
          t('common.error'),
          t('healthConsultation.history.archiveError')
        );
      }
    },
    [t]
  );

  const quickTopics = getDefaultQuickTopics(t).map((topic) => ({
    ...topic,
    title: t(topic.titleKey),
  }));

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Welcome Section */}
        <Animated.View
          style={[
            styles.welcomeSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Image
            source={require('../../assets/images/doctorWithoutBg.png')}
            style={[styles.doctorIcon, { width: screenWidth }]}
            resizeMode="contain"
          />
          <Text style={styles.welcomeMessage}>
            {t('healthConsultation.welcomeMessage')}
          </Text>
        </Animated.View>

        {/* Start New Consultation Button */}
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }}
        >
          <TouchableOpacity
            style={styles.startButton}
            onPress={handleStartNewConsultation}
            activeOpacity={0.8}
          >
            <Ionicons name="add-circle-outline" size={24} color="white" />
            <Text style={styles.startButtonText}>
              {t('healthConsultation.startNewConsultation')}
            </Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Quick Topics */}
        <Animated.View
          style={[
            styles.quickTopicsSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Text style={styles.sectionTitle}>
            {t('healthConsultation.quickTopicsTitle')}
          </Text>
          <View style={styles.topicsGrid}>
            {quickTopics.map((topic) => (
              <TouchableOpacity
                key={topic.id}
                style={[styles.topicCard, { borderLeftColor: topic.color }]}
                onPress={() => handleQuickTopicPress(topic)}
                activeOpacity={0.7}
              >
                <View
                  style={[styles.topicIcon, { backgroundColor: topic.color }]}
                >
                  <Ionicons name={topic.icon as any} size={24} color="white" />
                </View>
                <Text style={styles.topicTitle}>{topic.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Recent Conversations Section */}
        <Animated.View
          style={[
            styles.recentSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Text style={styles.sectionTitle}>
            {t('healthConsultation.recentConversations')}
          </Text>
          {conversations.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="chatbubbles-outline" size={48} color="#9CA3AF" />
              <Text style={styles.emptyStateText}>
                {t('healthConsultation.noConversations')}
              </Text>
              <Text style={styles.emptyStateSubtext}>
                {t('healthConsultation.startFirstConsultation')}
              </Text>
            </View>
          ) : (
            <View style={styles.conversationsList}>
              {conversations.slice(0, 3).map((conversation, index) => (
                <ConversationCard
                  key={conversation.id}
                  item={conversation}
                  index={index}
                  onPress={handleConversationPress}
                  onDelete={handleDeleteConversation}
                  onArchive={handleArchiveConversation}
                  t={t}
                  messageCounts={messageCounts}
                  showContextMenu={true}
                />
              ))}
              {conversations.length > 3 && (
                <TouchableOpacity
                  style={styles.viewAllButton}
                  onPress={() => router.push('/health-history')}
                >
                  <Text style={styles.viewAllText}>
                    View all {conversations.length} conversations
                  </Text>
                  <Ionicons name="chevron-forward" size={16} color="#2196F3" />
                </TouchableOpacity>
              )}
            </View>
          )}
        </Animated.View>

        {/* Medical Disclaimer */}
        <Animated.View
          style={[
            styles.disclaimerSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.disclaimerHeader}>
            <Ionicons
              name="information-circle-outline"
              size={20}
              color="#FF9800"
            />
            <Text style={styles.disclaimerTitle}>
              {t('healthConsultation.disclaimer.title')}
            </Text>
          </View>
          <Text style={styles.disclaimerText}>
            {t('healthConsultation.disclaimer.message')}
          </Text>
        </Animated.View>

        {/* Test Context Menu Button - Development Only */}
        {__DEV__ && (
          <Animated.View
            style={[
              styles.testSection,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            <TouchableOpacity
              style={styles.testButton}
              onPress={() => router.push('/test-context-menu')}
            >
              <Ionicons name="bug" size={20} color="#FF5722" />
              <Text style={styles.testButtonText}>Test Context Menu</Text>
            </TouchableOpacity>
          </Animated.View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  welcomeSection: {
    alignItems: 'center',

    top: -50,
    marginBottom: -45,
  },
  welcomeIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  doctorIcon: {
    height: undefined, // Let aspect ratio determine height
    aspectRatio: 1, // Assuming doctor.png is square, adjust if needed
    maxHeight: 400, // Prevent it from being too tall
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 8,
    top: -15,
  },
  welcomeMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    top: -15,
  },
  startButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 30,
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  startButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  quickTopicsSection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  topicsGrid: {
    gap: 12,
  },
  topicCard: {
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  topicIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  topicTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
    flex: 1,
  },
  recentSection: {
    marginBottom: 30,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
  },
  disclaimerSection: {
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 152, 0, 0.2)',
  },
  disclaimerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  disclaimerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FF9800',
    marginLeft: 6,
  },
  disclaimerText: {
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
  },
  conversationsList: {
    gap: 12,
  },

  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
  },
  viewAllText: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: '500',
    marginRight: 4,
  },
  // Test section styles
  testSection: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF3E0',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#FFE0B2',
  },
  testButtonText: {
    fontSize: 14,
    color: '#FF5722',
    fontWeight: '600',
    marginLeft: 8,
  },
});
