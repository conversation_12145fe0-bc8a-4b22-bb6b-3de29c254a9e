import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Platform,
  UIManager,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useNavigation } from 'expo-router'; // Import useNavigation
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../context/LocalizationContext';
import { supabase, transformAdditiveData, AdditiveFull } from '../lib/supabase';
import { useFilter } from '../context/FilterContext'; // Import the filter context
import { useAvoidedAdditives } from '../context/AvoidedAdditivesContext';
import { useCustomLabels } from '../context/CustomLabelsContext';
import Animated, {
  useAnimatedStyle,
  withTiming,
  Easing,
  runOnJS,
  useAnimatedReaction,
  useSharedValue,
} from 'react-native-reanimated';
import ECodeItem from '../components/ECodeItem';

const AnimatedView = Animated.createAnimatedComponent(View);

// Enable LayoutAnimation for Android
if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

export default function DatabaseScreen() {
  const { t, language } = useLocalization(); // Get language as well
  const navigation = useNavigation(); // Get navigation object

  const [additives, setAdditives] = useState<AdditiveFull[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const { activeFilters, setActiveFilters, filterVisible, filterHeight } =
    useFilter(); // Get filter state from context
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [expandedIds, setExpandedIds] = useState(new Set<string>()); // Specify type for Set
  const [itemsPerPage] = useState(20); // Number of items to load per page
  const [currentPage, setCurrentPage] = useState(1); // Current page number
  const [isLoadingMore, setIsLoadingMore] = useState(false); // Loading state for pagination
  const flatListRef = useRef<FlatList>(null); // Reference to FlatList for scrolling

  // Define animation configuration
  const FILTER_MAX_HEIGHT = 280; // Increased to accommodate all filter sections
  const ANIMATION_CONFIG = {
    duration: 250,
    easing: Easing.bezier(0.25, 0.1, 0.25, 1), // Improved easing curve
  };

  // Use animated reaction to track filter visibility changes
  useAnimatedReaction(
    () => filterVisible,
    (visible) => {
      // Animate height with optimized timing
      filterHeight.value = withTiming(
        visible ? FILTER_MAX_HEIGHT : 0,
        ANIMATION_CONFIG
      );
    },
    [filterVisible]
  );

  // Optimized animated style
  const animatedFilterStyle = useAnimatedStyle(() => {
    // Calculate opacity based on height for smooth transition
    const opacity = filterHeight.value / FILTER_MAX_HEIGHT;

    return {
      height: filterHeight.value,
      opacity: withTiming(opacity, { duration: 200 }),
      transform: [
        {
          translateY: withTiming(filterHeight.value === 0 ? -10 : 0, {
            duration: 200,
          }),
        },
      ],
    };
  });

  // Create animated values for filter chip animations
  const safeChipScale = useSharedValue(1);
  const questionableChipScale = useSharedValue(1);
  const harmfulChipScale = useSharedValue(1);
  const halalChipScale = useSharedValue(1);
  const haramChipScale = useSharedValue(1);
  const mushboohChipScale = useSharedValue(1);
  const avoidedChipScale = useSharedValue(1);

  // Optimized filter toggle with animation
  const toggleFilter = (
    filter:
      | 'safe'
      | 'questionable'
      | 'harmful'
      | 'halal'
      | 'haram'
      | 'mushbooh'
      | 'avoided'
  ) => {
    // Animate the chip when toggled
    let scaleValue;

    switch (filter) {
      case 'safe':
        scaleValue = safeChipScale;
        break;
      case 'questionable':
        scaleValue = questionableChipScale;
        break;
      case 'harmful':
        scaleValue = harmfulChipScale;
        break;
      case 'halal':
        scaleValue = halalChipScale;
        break;
      case 'haram':
        scaleValue = haramChipScale;
        break;
      case 'mushbooh':
        scaleValue = mushboohChipScale;
        break;
      case 'avoided':
        scaleValue = avoidedChipScale;
        break;
      default:
        scaleValue = safeChipScale;
    }

    // Quick press animation
    scaleValue.value = withTiming(0.95, { duration: 50 });
    setTimeout(() => {
      scaleValue.value = withTiming(1, { duration: 100 });
    }, 50);

    // Update filter state
    setActiveFilters((prev) => ({
      ...prev,
      [filter]: !prev[filter],
    }));
  };

  // Toggle custom label filter
  const toggleCustomLabelFilter = (labelId: string) => {
    setActiveFilters((prev) => {
      const customLabels = [...prev.customLabels];
      const index = customLabels.indexOf(labelId);

      if (index >= 0) {
        // Remove label if already selected
        customLabels.splice(index, 1);
      } else {
        // Add label if not selected
        customLabels.push(labelId);
      }

      return {
        ...prev,
        customLabels,
      };
    });
  };

  // Animated styles for filter chips
  const safeChipStyle = useAnimatedStyle(() => ({
    transform: [{ scale: safeChipScale.value }],
  }));

  const questionableChipStyle = useAnimatedStyle(() => ({
    transform: [{ scale: questionableChipScale.value }],
  }));

  const harmfulChipStyle = useAnimatedStyle(() => ({
    transform: [{ scale: harmfulChipScale.value }],
  }));

  const halalChipStyle = useAnimatedStyle(() => ({
    transform: [{ scale: halalChipScale.value }],
  }));

  const haramChipStyle = useAnimatedStyle(() => ({
    transform: [{ scale: haramChipScale.value }],
  }));

  const mushboohChipStyle = useAnimatedStyle(() => ({
    transform: [{ scale: mushboohChipScale.value }],
  }));

  const avoidedChipStyle = useAnimatedStyle(() => ({
    transform: [{ scale: avoidedChipScale.value }],
  }));

  // Get the avoided additives context
  const { avoidedAdditives } = useAvoidedAdditives();

  // Get the custom labels context
  const { labels, getLabelsForAdditive } = useCustomLabels();

  // Get count of each safety and halal type for filter badges
  const filterCounts = useMemo(() => {
    return {
      // Safety counts
      safe: additives.filter((i: AdditiveFull) => i.safety === 'safe').length,
      questionable: additives.filter(
        (i: AdditiveFull) => i.safety === 'questionable'
      ).length,
      harmful: additives.filter((i: AdditiveFull) => i.safety === 'harmful')
        .length,
      // Halal counts
      halal: additives.filter((i: AdditiveFull) => i.halal === 'halal').length,
      haram: additives.filter((i: AdditiveFull) => i.halal === 'haram').length,
      mushbooh: additives.filter(
        (i: AdditiveFull) => i.halal === 'mushbooh' || !i.halal
      ).length, // Count items with no halal status as mushbooh
    };
  }, [additives]); // Recalculate when additives change

  // Set dynamic tab title based on language
  useEffect(() => {
    navigation.setOptions({
      title: t('tabs.database'), // Use translation key
      // Remove headerTitle to hide it in the database tab
    });
  }, [navigation, t, language]); // Update when language changes

  // Fetch additives from Supabase with server-side pagination and filtering
  const fetchAdditives = useCallback(async () => {
    try {
      // Show appropriate loading indicators
      if (currentPage === 1) {
        if (debouncedSearchQuery.trim() !== '') {
          // If searching, show spinner in search box
          setIsSearching(true);
        } else {
          // If initial load or filter change, show full loading screen
          setIsLoading(true);
        }
      }

      // Check if any filter is active
      const anySafetyFilterActive =
        activeFilters.safe ||
        activeFilters.questionable ||
        activeFilters.harmful;

      const anyHalalFilterActive =
        activeFilters.halal || activeFilters.haram || activeFilters.mushbooh;

      // Start building the query
      let query = supabase
        .from('additives')
        .select('*', { count: 'exact' })
        .order('code', { ascending: true });

      // Apply safety filters if any are active
      if (anySafetyFilterActive) {
        const safetyFilters = [];
        if (activeFilters.safe) safetyFilters.push('safe');
        if (activeFilters.questionable) safetyFilters.push('questionable');
        if (activeFilters.harmful) safetyFilters.push('harmful');

        query = query.in('safety', safetyFilters);
      }

      // Apply halal filters if any are active
      if (anyHalalFilterActive) {
        const halalFilters = [];
        if (activeFilters.halal) halalFilters.push('halal');
        if (activeFilters.haram) halalFilters.push('haram');
        if (activeFilters.mushbooh) halalFilters.push('mushbooh');

        query = query.in('halal', halalFilters);
      }

      // Client-side filtering for avoided additives will be applied after fetching

      // Apply search query if present
      if (debouncedSearchQuery.trim() !== '') {
        const searchTerm = `%${debouncedSearchQuery.toLowerCase()}%`;
        query = query.or(
          `code.ilike.${searchTerm},name_tr.ilike.${searchTerm},name_en.ilike.${searchTerm},category_tr.ilike.${searchTerm},category_en.ilike.${searchTerm}`
        );
      }

      // Apply pagination
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;
      query = query.range(from, to);

      // Execute the query
      const { data, error, count } = await query;

      console.log('Supabase query result:', {
        data: data?.length,
        error,
        count,
      });

      if (error) {
        console.error('Error fetching additives:', error);
        return;
      }

      // Transform the data to match the AdditiveFull format
      const transformedData = data.map(transformAdditiveData);

      // Apply client-side filtering for avoided additives and custom labels
      let filteredData = transformedData;

      // Filter by avoided additives if needed
      if (activeFilters.avoided) {
        filteredData = filteredData.filter((item) =>
          avoidedAdditives.has(item.code)
        );
      }

      // Filter by custom labels if any are selected
      if (activeFilters.customLabels.length > 0) {
        filteredData = filteredData.filter((item) => {
          // Get all labels for this additive
          const additiveLabels = getLabelsForAdditive(item.code);

          // Check if any of the selected labels are applied to this additive
          return additiveLabels.some((label) =>
            activeFilters.customLabels.includes(label.id)
          );
        });
      }

      // Append new items to existing list if loading more, otherwise replace the list
      setAdditives((prevAdditives) => {
        // If it's the first page or filters/search changed, replace the list
        if (currentPage === 1) {
          return filteredData;
        }
        // Otherwise, append the new items to the existing list
        return [...prevAdditives, ...filteredData];
      });

      // Set the total count for pagination
      if (count !== null) {
        setTotalCount(count);
      }
    } catch (error) {
      console.error('Error in fetchAdditives:', error);
    } finally {
      setIsLoading(false);
      setIsSearching(false);
      setIsLoadingMore(false);
    }
  }, [
    debouncedSearchQuery,
    activeFilters,
    currentPage,
    itemsPerPage,
    avoidedAdditives,
    getLabelsForAdditive,
  ]);

  const toggleExpand = useCallback((code: string) => {
    setExpandedIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(code)) {
        newSet.delete(code);
      } else {
        newSet.add(code);
      }
      return newSet;
    });
  }, []);

  // Add type for item in renderItem
  const renderItem = useCallback(
    ({ item }: { item: AdditiveFull }) => (
      <ECodeItem
        code={item.code}
        isExpanded={expandedIds.has(item.code)}
        onToggle={() => toggleExpand(item.code)}
      />
    ),
    [expandedIds, toggleExpand]
  );

  const clearSearch = () => {
    setSearchQuery('');
    setCurrentPage(1); // Reset to first page when clearing search
  };

  // Check if any client-side filters are active
  const hasClientSideFilters = useCallback(() => {
    return activeFilters.avoided || activeFilters.customLabels.length > 0;
  }, [activeFilters.avoided, activeFilters.customLabels]);

  // Load more items when user reaches the end of the list
  const handleLoadMore = useCallback(() => {
    // If client-side filters are active, don't load more items
    if (hasClientSideFilters()) {
      return;
    }

    if (additives.length < totalCount && !isLoadingMore) {
      console.log('Loading more items...');
      setIsLoadingMore(true);
      setCurrentPage((prevPage) => prevPage + 1);
    }
  }, [additives.length, totalCount, isLoadingMore, hasClientSideFilters]);

  // Debounce search query to avoid too many requests
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 0); // 300ms delay

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery]);

  // Reset pagination when filters or search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchQuery, activeFilters]);

  // Fetch additives when dependencies change
  useEffect(() => {
    fetchAdditives();
  }, [fetchAdditives]);

  // Debug log
  useEffect(() => {
    console.log('Additives state:', {
      additivesLength: additives.length,
      totalCount,
      isLoading,
      isSearching,
      searchQuery,
      activeFilters,
    });
  }, [
    additives,
    totalCount,
    isLoading,
    isSearching,
    searchQuery,
    activeFilters,
  ]);

  // Function to refetch additives
  const refetchAdditives = useCallback(() => {
    setCurrentPage(1);
    fetchAdditives();
  }, [fetchAdditives]);

  // Handle initial loading state (only for first app load)
  if (isLoading && additives.length === 0 && !isSearching) {
    return (
      <SafeAreaView style={[styles.container, styles.centerContent]}>
        <View style={styles.loadingContainer}>
          <View style={styles.loadingIconContainer}>
            <ActivityIndicator size="large" color="#3b82f6" />
          </View>
          <Text style={styles.loadingText}>{t('common.loadingData')}</Text>
          <Text style={styles.loadingSubtext}>
            {language === 'tr'
              ? 'Katkı maddeleri veritabanı yükleniyor...'
              : 'Loading additives database...'}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Handle error state
  if (error) {
    return (
      <SafeAreaView style={[styles.container, styles.centerContent]}>
        <Ionicons name="alert-circle-outline" size={48} color="#F44336" />
        <Text style={styles.errorText}>{t('common.errorLoading')}</Text>
        <Text style={styles.errorDetail}>{error.message}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={refetchAdditives}>
          <Text style={styles.retryButtonText}>{t('common.retry')}</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <LinearGradient
      colors={['#f8fafc', '#f1f5f9', '#e2e8f0']}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <StatusBar barStyle="dark-content" backgroundColor="#fff" />

        {/* Filter Section */}
        <AnimatedView style={[styles.filterContainer, animatedFilterStyle]}>
          <View style={styles.filterTitleContainer}>
            <Ionicons name="options-outline" size={16} color="#64748b" />
            <Text style={styles.filterTitle}>{t('common.filterByStatus')}</Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalScrollContent}
          >
            <Animated.View style={safeChipStyle}>
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  activeFilters.safe ? styles.filterChipActive : null,
                  activeFilters.safe ? { backgroundColor: '#4CAF5015' } : null,
                ]}
                onPress={() => toggleFilter('safe')}
              >
                <View
                  style={[styles.statusDot, { backgroundColor: '#4CAF50' }]}
                />
                <Text style={styles.filterText}>{t('common.safe')}</Text>
                <View style={styles.countBadge}>
                  <Text style={styles.countText}>{filterCounts.safe}</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>

            <Animated.View style={questionableChipStyle}>
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  activeFilters.questionable ? styles.filterChipActive : null,
                  activeFilters.questionable
                    ? { backgroundColor: '#FF980015' }
                    : null,
                ]}
                onPress={() => toggleFilter('questionable')}
              >
                <View
                  style={[styles.statusDot, { backgroundColor: '#FF9800' }]}
                />
                <Text style={styles.filterText}>
                  {t('common.questionable')}
                </Text>
                <View style={styles.countBadge}>
                  <Text style={styles.countText}>
                    {filterCounts.questionable}
                  </Text>
                </View>
              </TouchableOpacity>
            </Animated.View>

            <Animated.View style={harmfulChipStyle}>
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  activeFilters.harmful ? styles.filterChipActive : null,
                  activeFilters.harmful
                    ? { backgroundColor: '#F4433615' }
                    : null,
                ]}
                onPress={() => toggleFilter('harmful')}
              >
                <View
                  style={[styles.statusDot, { backgroundColor: '#F44336' }]}
                />
                <Text style={styles.filterText}>{t('common.harmful')}</Text>
                <View style={styles.countBadge}>
                  <Text style={styles.countText}>{filterCounts.harmful}</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>
          </ScrollView>

          {/* Avoided Additives Filter */}
          <View style={styles.filterTitleContainer}>
            <Ionicons name="close-circle-outline" size={16} color="#64748b" />
            <Text style={styles.filterTitle}>
              {t('common.filterByAvoided')}
            </Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalScrollContent}
          >
            <Animated.View style={avoidedChipStyle}>
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  activeFilters.avoided ? styles.filterChipActive : null,
                  activeFilters.avoided
                    ? { backgroundColor: '#F4433615' }
                    : null,
                ]}
                onPress={() => toggleFilter('avoided')}
              >
                <View
                  style={[styles.statusDot, { backgroundColor: '#F44336' }]}
                />
                <Text style={styles.filterText}>{t('common.avoided')}</Text>
              </TouchableOpacity>
            </Animated.View>
          </ScrollView>

          {/* Custom Labels Filter */}
          <View style={styles.filterTitleContainer}>
            <Ionicons name="pricetags-outline" size={16} color="#64748b" />
            <Text style={styles.filterTitle}>
              {t('common.filterByCustomLabels')}
            </Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalScrollContent}
          >
            {labels.map((label) => (
              <TouchableOpacity
                key={label.id}
                style={[
                  styles.filterChip,
                  activeFilters.customLabels.includes(label.id)
                    ? styles.filterChipActive
                    : null,
                  activeFilters.customLabels.includes(label.id)
                    ? { backgroundColor: `${label.color}15` }
                    : null,
                ]}
                onPress={() => toggleCustomLabelFilter(label.id)}
              >
                <View
                  style={[styles.statusDot, { backgroundColor: label.color }]}
                />
                <Text style={styles.filterText}>{label.name}</Text>
              </TouchableOpacity>
            ))}
            {labels.length === 0 && (
              <Text style={styles.noLabelsText}>
                {t('common.noCustomLabels')}
              </Text>
            )}
          </ScrollView>

          {/* Halal Status Filters */}
          <View style={styles.filterTitleContainer}>
            <Ionicons
              name="checkmark-circle-outline"
              size={16}
              color="#64748b"
            />
            <Text style={styles.filterTitle}>{t('common.filterByHalal')}</Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalScrollContent}
          >
            <Animated.View style={halalChipStyle}>
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  activeFilters.halal ? styles.filterChipActive : null,
                  activeFilters.halal ? { backgroundColor: '#4CAF5015' } : null,
                ]}
                onPress={() => toggleFilter('halal')}
              >
                <View
                  style={[styles.statusDot, { backgroundColor: '#4CAF50' }]}
                />
                <Text style={styles.filterText}>{t('common.halal')}</Text>
                <View style={styles.countBadge}>
                  <Text style={styles.countText}>{filterCounts.halal}</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>

            <Animated.View style={haramChipStyle}>
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  activeFilters.haram ? styles.filterChipActive : null,
                  activeFilters.haram ? { backgroundColor: '#F4433615' } : null,
                ]}
                onPress={() => toggleFilter('haram')}
              >
                <View
                  style={[styles.statusDot, { backgroundColor: '#F44336' }]}
                />
                <Text style={styles.filterText}>{t('common.haram')}</Text>
                <View style={styles.countBadge}>
                  <Text style={styles.countText}>{filterCounts.haram}</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>

            <Animated.View style={mushboohChipStyle}>
              <TouchableOpacity
                style={[
                  styles.filterChip,
                  activeFilters.mushbooh ? styles.filterChipActive : null,
                  activeFilters.mushbooh
                    ? { backgroundColor: '#FF980015' }
                    : null,
                ]}
                onPress={() => toggleFilter('mushbooh')}
              >
                <View
                  style={[styles.statusDot, { backgroundColor: '#FF9800' }]}
                />
                <Text style={styles.filterText}>{t('common.mushbooh')}</Text>
                <View style={styles.countBadge}>
                  <Text style={styles.countText}>{filterCounts.mushbooh}</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>
          </ScrollView>
        </AnimatedView>

        {/* Search Box */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBox}>
            <Ionicons
              name="search"
              size={20}
              color="#666"
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder={t('common.searchPlaceholder')}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#94a3b8"
              returnKeyType="done"
              clearButtonMode="while-editing"
              selectionColor="#3b82f6"
            />
            {isSearching ? (
              <ActivityIndicator
                size="small"
                color="#007AFF"
                style={styles.searchSpinner}
              />
            ) : searchQuery.length > 0 ? (
              <TouchableOpacity
                onPress={clearSearch}
                style={styles.clearButton}
              >
                <Ionicons name="close-circle" size={20} color="#666" />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        {/* Results Count */}
        <View style={styles.resultsCountContainer}>
          <Text style={styles.resultsCount}>
            {searchQuery.trim() !== '' || hasClientSideFilters()
              ? `${additives.length} ${t('common.resultsFound')}`
              : `${additives.length} / ${totalCount} ${t('common.itemsShown')}`}
          </Text>
        </View>

        {/* E-Code List */}
        <FlatList
          ref={flatListRef}
          data={additives}
          renderItem={renderItem}
          keyExtractor={(item) => item.code}
          contentContainerStyle={styles.list}
          style={styles.flatList}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.3}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={10}
          removeClippedSubviews={true}
          showsVerticalScrollIndicator={true}
          ListFooterComponent={() => {
            // Don't show load more button if client-side filters are active
            if (hasClientSideFilters()) {
              return null;
            }

            return additives.length < totalCount && totalCount > 0 ? (
              <TouchableOpacity
                style={styles.loadMoreButton}
                onPress={handleLoadMore}
                disabled={isLoadingMore}
              >
                {isLoadingMore ? (
                  <ActivityIndicator size="small" color="#007AFF" />
                ) : (
                  <Text style={styles.loadMoreText}>
                    {t('common.loadMore')}
                  </Text>
                )}
              </TouchableOpacity>
            ) : null;
          }}
          ListEmptyComponent={() =>
            isLoading ? (
              <View style={styles.emptyContainer}>
                <ActivityIndicator size="large" color="#007AFF" />
                <Text style={styles.emptyText}>{t('common.loadingData')}</Text>
              </View>
            ) : (
              <View style={styles.emptyContainer}>
                <Ionicons name="search-outline" size={48} color="#ccc" />
                <Text style={styles.emptyText}>{t('common.noResults')}</Text>
                <TouchableOpacity
                  style={styles.resetButton}
                  onPress={() => {
                    clearSearch();
                    setActiveFilters({
                      safe: false,
                      questionable: false,
                      harmful: false,
                      halal: false,
                      haram: false,
                      mushbooh: false,
                      avoided: false,
                      customLabels: [],
                    });
                  }}
                >
                  <Text style={styles.resetButtonText}>
                    {t('common.resetFilters')}
                  </Text>
                </TouchableOpacity>
              </View>
            )
          }
        />
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  centerContent: {
    // Style for loading/error states
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  loadingContainer: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 24,
    padding: 40,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  loadingIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#eff6ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 18,
    color: '#1e293b',
    fontWeight: '600',
    textAlign: 'center',
  },
  loadingSubtext: {
    marginTop: 8,
    fontSize: 15,
    color: '#64748b',
    fontWeight: '500',
    textAlign: 'center',
  },
  errorText: {
    marginTop: 16,
    fontSize: 20,
    color: '#dc2626',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorDetail: {
    fontSize: 15,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 20,
    lineHeight: 22,
  },
  retryButton: {
    paddingVertical: 14,
    paddingHorizontal: 32,
    backgroundColor: '#3b82f6',
    borderRadius: 12,
    shadowColor: '#3b82f6',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },

  // Filter styles - Enhanced with modern design
  filterContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingTop: 4,
    overflow: 'hidden',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    maxHeight: 280,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  filterTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 10,
  },
  filterTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
    letterSpacing: 0.3,
  },
  filterOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    paddingHorizontal: 12,
    borderRadius: 24,
    borderWidth: 1.5,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    height: 24,
  },
  filterChipActive: {
    borderColor: '#3b82f6',
    backgroundColor: '#eff6ff',
    shadowColor: '#3b82f6',
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 6,
    marginRight: 6,
  },
  filterText: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '500',
  },
  countBadge: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 3,
    marginLeft: 6,
  },
  countText: {
    fontSize: 10,
    color: '#3b82f6',
    fontWeight: '600',
  },

  searchContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 16,
    paddingHorizontal: 16,
    height: 52,
    borderWidth: 1.5,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 12,
    color: '#64748b',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1e293b',
    height: '100%',
    paddingVertical: 8,
    fontWeight: '500',
  },
  clearButton: {
    padding: 6,
    borderRadius: 8,
  },
  resultsCountContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#f8fafc',
    flexDirection: 'column',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  resultsCount: {
    fontSize: 15,
    color: '#64748b',
    fontWeight: '500',
  },
  flatList: {
    flex: 1,
  },
  list: {
    padding: 20,
    paddingTop: 12,
    flexGrow: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    marginTop: 60,
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 17,
    color: '#64748b',
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 24,
  },
  resetButton: {
    marginTop: 20,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#3b82f6',
    borderRadius: 12,
    shadowColor: '#3b82f6',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  resetButtonText: {
    fontSize: 15,
    color: 'white',
    fontWeight: '600',
    textAlign: 'center',
  },
  loadMoreButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginVertical: 20,
    marginHorizontal: 20,
    backgroundColor: '#f8fafc',
    borderWidth: 1.5,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  loadMoreText: {
    fontSize: 15,
    color: '#3b82f6',
    fontWeight: '600',
  },
  searchSpinner: {
    marginRight: 12,
  },
  noLabelsText: {
    fontSize: 13,
    color: '#94a3b8',
    fontStyle: 'italic',
    padding: 12,
    textAlign: 'center',
  },
  horizontalScrollContent: {
    paddingBottom: 12,
    gap: 8,
  },
});
