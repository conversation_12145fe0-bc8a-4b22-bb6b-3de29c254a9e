import { useEffect, useState } from 'react';
import * as ImagePicker from 'expo-image-picker';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Modal,
  Platform,
  Dimensions,
  ActivityIndicator,
  Linking,
  StatusBar,
} from 'react-native';
import { CameraView } from 'expo-camera';
import { useNavigation } from 'expo-router'; // Import useNavigation
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withSequence,
  withDelay,
} from 'react-native-reanimated';
import { BlurView } from 'expo-blur';
import { useLocalization } from '../context/LocalizationContext';
import ScanGuidanceOverlay from '../components/ScanGuidanceOverlay';
import ScanTutorial from '../components/ScanTutorial';
import ScanCameraOverlay from '../components/ScanCameraOverlay';
import ScanOnboarding from '../components/ScanOnboarding';
import CameraScanningOverlay from '../components/CameraScanningOverlay';

import ScanningProgressIndicator from '../components/ScanningProgressIndicator';
import MultiImageActionButtons from '../components/MultiImageActionButtons';
import MultiImageSummary from '../components/MultiImageSummary';
import { useScanLogic } from '../hooks/useScanLogic';
import { useMultiImageScan } from '../hooks/useMultiImageScan';
import CameraPermissionScreen from '../components/CameraPermissionScreen';
import ECodeModal from '../components/ECodeModal';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

const SCREEN_WIDTH =
  Platform.OS === 'web' ? window.innerWidth : Dimensions.get('window').width;

export default function ScanScreen() {
  const {
    scanMode,
    foundECodes,
    expandedCodes,
    isProcessing,
    isCameraActive,
    showFeedback,
    feedbackMessage,
    product,
    foodScore,
    isCalculatingScore,
    cameraPermission,
    cameraRef,
    requestCameraPermission,

    toggleExpand,
    closeModal,
    showGalleryPermissionModal,
    setShowGalleryPermissionModal,
    handleGalleryPermissionRequest,
    galleryPermissionDeniedForever,
    guidance,
    processText,
  } = useScanLogic();

  // Multi-image scanning state (always enabled now)
  const [showMultiImageSummary, setShowMultiImageSummary] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  const [captureSuccess, setCaptureSuccess] = useState(false);

  const multiImageScan = useMultiImageScan();

  // Multi-image scanning handlers
  const handleMultiImageCapture = async () => {
    if (!cameraRef.current || multiImageScan.isProcessing) return;

    setIsCapturing(true);
    setCaptureSuccess(false);

    try {
      const photo = await cameraRef.current.takePictureAsync({
        skipProcessing: true,
        quality: 1,
      });

      if (photo?.uri) {
        console.log(`Capturing image for step ${multiImageScan.currentStep}`);

        const success = await multiImageScan.captureStepImage(
          photo.uri,
          multiImageScan.currentStep
        );

        if (success) {
          console.log(
            `Image captured successfully for step ${multiImageScan.currentStep}`
          );

          // Show success feedback immediately
          setCaptureSuccess(true);
          setIsCapturing(false);

          // Auto-advance to next step immediately if not the last step
          if (multiImageScan.currentStep < 3) {
            multiImageScan.nextStep();
            // Hide success feedback after a short delay
            setTimeout(() => {
              setCaptureSuccess(false);
            }, 500);
          } else {
            // Last step - hide success after short delay
            setTimeout(() => {
              setCaptureSuccess(false);
            }, 1000);
          }
        } else {
          setIsCapturing(false);
        }
      } else {
        setIsCapturing(false);
      }
    } catch (error) {
      console.error('Error capturing multi-image:', error);
      setIsCapturing(false);
    }
  };

  const handleMultiImageGallery = async () => {
    if (multiImageScan.isProcessing) return;

    setIsCapturing(true);
    setCaptureSuccess(false);

    try {
      // Use ImagePicker directly for multi-image mode
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        console.log(`Picking image for step ${multiImageScan.currentStep}`);

        const success = await multiImageScan.captureStepImage(
          result.assets[0].uri,
          multiImageScan.currentStep
        );

        if (success) {
          console.log(
            `Image picked successfully for step ${multiImageScan.currentStep}`
          );

          // Show success feedback immediately
          setCaptureSuccess(true);
          setIsCapturing(false);

          // Auto-advance to next step immediately if not the last step
          if (multiImageScan.currentStep < 3) {
            multiImageScan.nextStep();
            // Hide success feedback after a short delay
            setTimeout(() => {
              setCaptureSuccess(false);
            }, 500);
          } else {
            // Last step - hide success after short delay
            setTimeout(() => {
              setCaptureSuccess(false);
            }, 1000);
          }
        } else {
          setIsCapturing(false);
        }
      } else {
        setIsCapturing(false);
      }
    } catch (error) {
      console.error('Error picking image for multi-image scan:', error);
      setIsCapturing(false);
    }
  };

  const handleMultiImageRetake = () => {
    multiImageScan.retakeStepImage(multiImageScan.currentStep);
  };

  const handleMultiImageFinish = async () => {
    if (!multiImageScan.getAllStepsCompleted()) return;

    try {
      // First close the MultiImageSummary modal immediately
      setShowMultiImageSummary(false);

      // Clear previous results before processing new ones
      closeModal();

      // Start the analysis process
      const scanResult = await multiImageScan.generateScanResult();
      if (scanResult) {
        console.log('Scan result nutrition data:', scanResult.nutritionData);

        // Process the combined text using existing logic, but pass the images and nutrition data too
        await processText(
          scanResult.combinedText,
          null,
          undefined,
          scanResult.images,
          scanResult.nutritionData
        );

        // Reset multi-image scan
        multiImageScan.resetScan();
      }
    } catch (error) {
      console.error('Error finishing multi-image scan:', error);
      // Reset multi-image scan even on error
      multiImageScan.resetScan();
    }
  };

  const feedbackOpacity = useSharedValue(0);
  const navigation = useNavigation(); // Get navigation object
  const { t, language } = useLocalization(); // Get language

  // Set dynamic tab title based on language
  useEffect(() => {
    navigation.setOptions({
      title: t('tabs.scan'), // Use translation key
      // headerTitle is set in _layout.tsx
    });
  }, [navigation, t, language]); // Update when language changes

  useEffect(() => {
    if (showFeedback) {
      feedbackOpacity.value = withSequence(
        withTiming(1, { duration: 300 }),
        withDelay(2000, withTiming(0, { duration: 300 }))
      );
    }
  }, [showFeedback, feedbackOpacity]);

  const feedbackAnimatedStyle = useAnimatedStyle(() => ({
    opacity: feedbackOpacity.value,
  }));

  // Remove duplicate useLocalization call: const { t } = useLocalization();

  if (cameraPermission?.granted !== true) {
    return (
      <CameraPermissionScreen
        cameraPermission={cameraPermission}
        requestCameraPermission={requestCameraPermission}
      />
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="black" />

      {/* Progress Indicator */}
      {scanMode === 'text' && (
        <ScanningProgressIndicator
          steps={multiImageScan.steps}
          currentStepNumber={multiImageScan.currentStep}
          totalSteps={multiImageScan.totalSteps}
        />
      )}

      {/* Camera View Area */}
      <View style={styles.cameraAreaWrapper}>
        {isCameraActive ? (
          <View style={styles.cameraContainer}>
            <CameraView
              ref={cameraRef}
              style={styles.cameraPreview}
              facing="back"
            />
            {/* <Image
              source={require('../../assets/images/ingredients2.png')}
              style={styles.cameraPreview}
            /> */}
            {/* Sophisticated Camera Overlay */}
            {scanMode === 'text' ? (
              <CameraScanningOverlay
                currentStep={multiImageScan.getCurrentStep()!}
                totalSteps={multiImageScan.totalSteps}
                completedCount={multiImageScan.completedCount}
                isProcessing={
                  multiImageScan.isAnalyzing || multiImageScan.isProcessing
                }
                showGuidance={guidance.shouldShowGuidance(scanMode)}
                isCapturing={isCapturing}
                captureSuccess={captureSuccess}
              />
            ) : (
              <ScanCameraOverlay
                scanMode={'text'}
                isProcessing={isProcessing}
                showGuidance={guidance.shouldShowGuidance('text')}
              />
            )}
          </View>
        ) : (
          <View style={styles.cameraPlaceholder}>
            <ActivityIndicator color="#2196F3" size="large" />
            <Text style={styles.inactiveText}>{t('scan.preparingCamera')}</Text>
          </View>
        )}
      </View>

      {/* Controls and Buttons */}
      <MultiImageActionButtons
        currentStep={multiImageScan.getCurrentStep()!}
        totalSteps={multiImageScan.totalSteps}
        completedCount={multiImageScan.completedCount}
        isProcessing={multiImageScan.isProcessing}
        isCapturing={isCapturing}
        allStepsCompleted={multiImageScan.getAllStepsCompleted()}
        onTakePicture={handleMultiImageCapture}
        onPickImage={handleMultiImageGallery}
        onRetake={handleMultiImageRetake}
        onNextStep={multiImageScan.nextStep}
        onFinishScanning={() => setShowMultiImageSummary(true)}
      />

      {/* Feedback Overlay */}
      {showFeedback && (
        <Animated.View
          style={[styles.feedbackContainer, feedbackAnimatedStyle]}
        >
          <MaterialIcons
            name={feedbackMessage.includes('success') ? 'check-circle' : 'info'}
            size={24}
            color={feedbackMessage.includes('success') ? '#4CAF50' : '#2196F3'}
            style={styles.feedbackIcon}
          />
          <Text style={styles.feedbackText}>{feedbackMessage}</Text>
        </Animated.View>
      )}

      {/* AI Processing Overlay */}
      {(isProcessing || multiImageScan.isAnalyzing || isCalculatingScore) && (
        <View style={styles.aiProcessingOverlay}>
          <View style={styles.aiProcessingContent}>
            <ActivityIndicator color="#2196F3" size="large" />
            <Text style={styles.aiProcessingTitle}>
              {isCalculatingScore
                ? t('scan.calculatingScore')
                : multiImageScan.isAnalyzing
                ? t('scan.aiAnalyzing')
                : t('scan.processing')}
            </Text>
            <Text style={styles.aiProcessingSubtitle}>
              {isCalculatingScore
                ? t('scan.calculatingScoreMessage')
                : t('scan.aiProcessingMessage')}
            </Text>
            <View style={styles.aiProcessingWarning}>
              <Ionicons name="warning-outline" size={20} color="#FF9800" />
              <Text style={styles.aiProcessingWarningText}>
                {t('scan.doNotCloseApp')}
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Gallery Permission Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={showGalleryPermissionModal}
        onRequestClose={() => setShowGalleryPermissionModal(false)}
      >
        <BlurView
          intensity={90}
          style={styles.permissionModalOverlay}
          tint="dark"
        >
          <View style={styles.permissionModalContent}>
            <MaterialIcons
              name="perm-media"
              size={48}
              color="#2196F3"
              style={styles.permissionIcon}
            />

            <Text style={styles.permissionTitle}>
              {t('scan.galleryPermissionRequired')}
            </Text>

            <Text style={styles.permissionMessage}>
              {galleryPermissionDeniedForever
                ? t('scan.galleryPermissionDeniedSettings')
                : t('scan.galleryPermissionExplanation')}
            </Text>

            <View style={styles.permissionButtonsContainer}>
              <TouchableOpacity
                style={[styles.permissionButton, styles.cancelButton]}
                onPress={() => setShowGalleryPermissionModal(false)}
              >
                <Text style={styles.cancelButtonText}>{t('scan.cancel')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.permissionButton}
                onPress={
                  galleryPermissionDeniedForever
                    ? () => Linking.openSettings()
                    : handleGalleryPermissionRequest
                }
              >
                <Text style={styles.permissionButtonText}>
                  {galleryPermissionDeniedForever
                    ? t('scan.goToSettings')
                    : t('scan.continue')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </BlurView>
      </Modal>

      {/* Only show ECodeModal when MultiImageSummary is not visible, there's content to show, and score calculation is complete */}
      {!showMultiImageSummary &&
        !isCalculatingScore &&
        (foundECodes.length > 0 || !!foodScore || !!product) && (
          <ECodeModal
            foundECodes={foundECodes}
            expandedCodes={expandedCodes}
            product={product}
            foodScore={foodScore}
            toggleExpand={toggleExpand}
            closeModal={closeModal}
          />
        )}

      {/* Guidance Components */}
      <ScanGuidanceOverlay
        visible={guidance.showGuidanceOverlay}
        scanMode={'text'}
        onClose={guidance.hideGuidanceOverlay}
        onShowTutorial={guidance.showScanTutorial}
      />

      <ScanTutorial
        visible={guidance.showTutorial}
        onClose={guidance.hideTutorial}
      />

      <ScanOnboarding
        visible={guidance.showOnboarding}
        onComplete={guidance.completeOnboarding}
      />

      {/* Multi-Image Summary Modal */}
      <MultiImageSummary
        visible={showMultiImageSummary}
        steps={multiImageScan.steps}
        onClose={() => setShowMultiImageSummary(false)}
        onRetakeStep={(stepNumber) => {
          multiImageScan.retakeStepImage(stepNumber);
          multiImageScan.goToStep(stepNumber);
          setShowMultiImageSummary(false);
        }}
        onStartAnalysis={handleMultiImageFinish}
        isAnalyzing={multiImageScan.isAnalyzing}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  cameraAreaWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  cameraContainer: {
    width: Math.min(SCREEN_WIDTH * 0.85, 320),
    height: Math.min(SCREEN_WIDTH * 0.85, 320),
    backgroundColor: '#1a1a1a',
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },
  cameraPreview: {
    width: '100%',
    height: '100%',
  },
  cameraPlaceholder: {
    width: Math.min(SCREEN_WIDTH * 0.85, 320),
    height: Math.min(SCREEN_WIDTH * 0.85, 320),
    backgroundColor: '#1a1a1a',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },

  // scanLine: { // Keep if needed inside the box
  //   position: 'absolute',
  //   left: 0,
  //   top: '50%',
  //   height: 3,
  //   width: '100%',
  //   backgroundColor: 'rgba(33, 150, 243, 0.7)',
  //   shadowColor: '#2196F3',
  //   shadowOffset: { width: 0, height: 0 },
  //   shadowOpacity: 0.8,
  //   shadowRadius: 10,
  //   elevation: 5,
  // },

  description: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    textAlign: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    height: 50,
    marginBottom: 24,
    marginTop: 8,
    borderRadius: 6,
  },
  bottomControlsContainer: {
    width: '100%',
    alignItems: 'center',
    // paddingBottom: 20, // Already handled by container paddingBottom
  },
  // modalContainer, modalContent styles remain unchanged
  inactiveText: {
    color: 'white',
    marginTop: 16,
    fontSize: 16,
  },
  feedbackContainer: {
    position: 'absolute',
    bottom: 20, // Position feedback relative to screen bottom
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.85)',
    padding: 16,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  feedbackIcon: {
    marginRight: 8,
  },
  feedbackText: {
    color: 'white',
    fontSize: 16,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
    padding: 20,
  },
  permissionModalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionModalContent: {
    backgroundColor: 'white',
    borderRadius: 24,
    padding: 28,
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  permissionIcon: {
    marginBottom: 20,
  },
  permissionTitle: {
    color: '#222',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  permissionMessage: {
    color: '#555',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 28,
    lineHeight: 22,
  },
  permissionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 8,
  },
  permissionButton: {
    flex: 1,
    backgroundColor: '#2196F3',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    marginRight: 12,
  },
  cancelButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '600',
  },
  processingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.85)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  processingText: {
    color: 'white',
    fontSize: 16,
    marginTop: 12,
  },
  aiProcessingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 15,
  },
  aiProcessingContent: {
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 20,
    padding: 32,
    alignItems: 'center',
    maxWidth: '85%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  aiProcessingTitle: {
    color: '#222',
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  aiProcessingSubtitle: {
    color: '#555',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  aiProcessingWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 152, 0, 0.3)',
  },
  aiProcessingWarningText: {
    color: '#FF9800',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
    textAlign: 'center',
  },
});
