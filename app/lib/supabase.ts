import 'react-native-url-polyfill/auto'; // Required for Supabase
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Supabase URL or Anon Key is missing in environment variables.'
  );
}

// Use AsyncStorage for session persistence in React Native
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Define the type for the database row based on the table structure
// This should match the AdditiveFull interface, but mapping DB columns
export interface AdditiveDBRow {
  code: string;
  name_tr: string | null;
  name_en: string | null;
  safety: 'safe' | 'questionable' | 'harmful' | null;
  category_tr: string | null;
  category_en: string | null;
  description_tr: string | null;
  description_en: string | null;
  usage_tr: string | null;
  usage_en: string | null;
  slugs: string[] | null; // Array of alternative spellings for exact matching
  halal: 'halal' | 'haram' | 'mushbooh' | null; // Halal status
}

// Define the AdditiveFull interface matching the original constants file
export interface AdditiveFull {
  code: string;
  name: { TR: string; EN: string };
  safety: 'safe' | 'questionable' | 'harmful';
  categoryTr: string;
  categoryEn: string;
  descriptionTr: string;
  descriptionEn: string;
  usageTr: string;
  usageEn: string;
  slugs?: string[]; // Optional array of alternative spellings for exact matching
  halal?: 'halal' | 'haram' | 'mushbooh'; // Halal status
}

// Helper function to transform DB row to AdditiveFull format
export const transformAdditiveData = (dbRow: AdditiveDBRow): AdditiveFull => {
  const result: AdditiveFull = {
    code: dbRow.code,
    name: {
      TR: dbRow.name_tr ?? '',
      EN: dbRow.name_en ?? '',
    },
    // Provide a default safety level if null, though the DB constraint should prevent this
    safety: dbRow.safety ?? 'questionable',
    categoryTr: dbRow.category_tr ?? '',
    categoryEn: dbRow.category_en ?? '',
    descriptionTr: dbRow.description_tr ?? '',
    descriptionEn: dbRow.description_en ?? '',
    usageTr: dbRow.usage_tr ?? '',
    usageEn: dbRow.usage_en ?? '',
  };

  // Add slugs if they exist
  if (dbRow.slugs && dbRow.slugs.length > 0) {
    result.slugs = dbRow.slugs;
  }

  // Add halal status if it exists
  if (dbRow.halal) {
    result.halal = dbRow.halal;
  }

  return result;
};

// Interface for API configuration
export interface ApiConfiguration {
  id: number;
  service_name: string;
  api_key: string;
  api_url: string | null;
  description: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Helper function to get API configuration by service name
export const getApiConfiguration = async (
  serviceName: string
): Promise<ApiConfiguration | null> => {
  try {
    const { data, error } = await supabase
      .from('api_configurations')
      .select('*')
      .eq('service_name', serviceName)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error(
        `Error fetching API configuration for ${serviceName}:`,
        error
      );
      return null;
    }

    return data as ApiConfiguration;
  } catch (error) {
    console.error(
      `Exception while fetching API configuration for ${serviceName}:`,
      error
    );
    return null;
  }
};

// Helper function to get OpenRouter API configuration specifically
export const getOpenRouterConfig = async (): Promise<{
  apiKey: string;
  apiUrl: string;
} | null> => {
  const config = await getApiConfiguration('openrouter');

  if (!config) {
    console.error('OpenRouter API configuration not found');
    return null;
  }

  return {
    apiKey: config.api_key,
    apiUrl: config.api_url || 'https://openrouter.ai/api/v1/chat/completions',
  };
};
