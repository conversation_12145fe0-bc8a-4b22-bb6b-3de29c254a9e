// OpenRouter API integration using stored credentials from Supabase
import { getOpenRouterConfig } from './supabase';
import { getModelForTask, getModelConfig } from '../services/AIModelConfig';

// Interface for OpenRouter chat completion request
export interface OpenRouterChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface OpenRouterChatRequest {
  model: string;
  messages: OpenRouterChatMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stream?: boolean;
}

export interface OpenRouterChatResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Function to make chat completion requests to OpenRouter
export const createChatCompletion = async (
  request: OpenRouterChatRequest
): Promise<OpenRouterChatResponse | null> => {
  try {
    // Get API configuration from Supabase
    const config = await getOpenRouterConfig();

    if (!config) {
      console.error('OpenRouter API configuration not available');
      return null;
    }

    const response = await fetch(config.apiUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://additives-app.com', // Optional: Your app's URL
        'X-Title': 'Additives App', // Optional: Your app's name
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`OpenRouter API error (${response.status}):`, errorText);
      return null;
    }

    const data = await response.json();
    return data as OpenRouterChatResponse;
  } catch (error) {
    console.error('Error making OpenRouter API request:', error);
    return null;
  }
};

// Example usage function for analyzing ingredient text
export const analyzeIngredients = async (
  ingredientText: string
): Promise<string | null> => {
  // Get model configuration for E-code detection
  const modelConfig = getModelConfig('e-code-detection');
  const model = getModelForTask('e-code-detection');

  const request: OpenRouterChatRequest = {
    model: model,
    messages: [
      {
        role: 'system',
        content:
          'You are an expert in food additives and E-codes. Analyze ingredient lists and identify E-codes and additives. Provide clear, concise information about any additives found.',
      },
      {
        role: 'user',
        content: `Please analyze this ingredient list and identify any E-codes or food additives: "${ingredientText}"`,
      },
    ],
    max_tokens: modelConfig.maxTokens,
    temperature: modelConfig.temperature,
  };

  const response = await createChatCompletion(request);

  if (response && response.choices && response.choices.length > 0) {
    return response.choices[0].message.content;
  }

  return null;
};

// Example usage function for getting additive information
export const getAdditiveInfo = async (
  eCode: string
): Promise<string | null> => {
  // Get model configuration for E-code detection (closest task for additive info)
  const modelConfig = getModelConfig('e-code-detection');
  const model = getModelForTask('e-code-detection');

  const request: OpenRouterChatRequest = {
    model: model,
    messages: [
      {
        role: 'system',
        content:
          'You are an expert in food additives. Provide detailed, accurate information about specific E-codes including their safety, usage, and any health considerations.',
      },
      {
        role: 'user',
        content: `Please provide detailed information about the food additive ${eCode}, including its safety profile, common uses, and any health considerations.`,
      },
    ],
    max_tokens: 400,
    temperature: 0.2,
  };

  const response = await createChatCompletion(request);

  if (response && response.choices && response.choices.length > 0) {
    return response.choices[0].message.content;
  }

  return null;
};
