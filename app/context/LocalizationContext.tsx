import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Language, translations } from '../i18n/translations';

interface LocalizationContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
}

const LocalizationContext = createContext<LocalizationContextType | undefined>(
  undefined
);

export function LocalizationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [language, setLanguage] = useState<Language>('en');

  // Load language from AsyncStorage on mount
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const savedLanguage = await AsyncStorage.getItem('userLanguage');
        if (savedLanguage === 'tr' || savedLanguage === 'en') {
          setLanguage(savedLanguage);
        }
      } catch (error) {
        console.warn('Could not load language preference:', error);
      }
    };

    loadLanguage();
  }, []);

  // Save language to AsyncStorage when it changes
  const handleSetLanguage = async (lang: Language) => {
    try {
      await AsyncStorage.setItem('userLanguage', lang);
      setLanguage(lang);
    } catch (error) {
      console.warn('Could not save language preference:', error);
      // Still set the language in memory even if saving fails
      setLanguage(lang);
    }
  };

  const t = (path: string, params?: Record<string, string | number>) => {
    const keys = path.split('.');
    let current: any = translations[language];

    for (const key of keys) {
      if (current[key] === undefined) {
        console.warn(`Translation missing for key: ${path}`);
        return path;
      }
      current = current[key];
    }

    let result = current;

    // Handle interpolation if params are provided
    if (params && typeof result === 'string') {
      Object.entries(params).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        result = result.replace(new RegExp(placeholder, 'g'), String(value));
      });
    }

    return result;
  };

  return (
    <LocalizationContext.Provider
      value={{ language, setLanguage: handleSetLanguage, t }}
    >
      {children}
    </LocalizationContext.Provider>
  );
}

export function useLocalization() {
  const context = useContext(LocalizationContext);
  if (context === undefined) {
    throw new Error(
      'useLocalization must be used within a LocalizationProvider'
    );
  }
  return context;
}
