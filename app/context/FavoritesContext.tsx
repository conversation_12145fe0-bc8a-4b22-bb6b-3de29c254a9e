import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Favoriler için AsyncStorage anahtarı
const FAVORITES_STORAGE_KEY = '@favorites';

// Context tipi tanımı
interface FavoritesContextType {
  favorites: Set<string>; // Favori E-kodlarının kümesi
  addFavorite: (code: string) => Promise<void>; // Favori ekleme fonksiyonu
  removeFavorite: (code: string) => Promise<void>; // Favori kaldırma fonksiyonu
  toggleFavorite: (code: string) => Promise<void>; // Favori durumunu değiştirme fonksiyonu
  isFavorite: (code: string) => boolean; // Bir kodun favori olup olmadığını kontrol etme fonksiyonu
}

// Context oluşturma
const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined);

// Provider bileşeni
export function FavoritesProvider({ children }: { children: React.ReactNode }) {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Favorileri AsyncStorage'dan yükleme
  useEffect(() => {
    const loadFavorites = async () => {
      try {
        const storedFavorites = await AsyncStorage.getItem(FAVORITES_STORAGE_KEY);
        if (storedFavorites) {
          const favoritesArray = JSON.parse(storedFavorites) as string[];
          setFavorites(new Set(favoritesArray));
          console.log(`Loaded ${favoritesArray.length} favorites from storage`);
        }
      } catch (error) {
        console.error('Failed to load favorites from storage:', error);
      }
    };

    loadFavorites();
  }, []);

  // Favorileri AsyncStorage'a kaydetme
  const saveFavorites = async (newFavorites: Set<string>) => {
    try {
      const favoritesArray = Array.from(newFavorites);
      await AsyncStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(favoritesArray));
      console.log(`Saved ${favoritesArray.length} favorites to storage`);
    } catch (error) {
      console.error('Failed to save favorites to storage:', error);
    }
  };

  // Favori ekleme
  const addFavorite = async (code: string) => {
    const newFavorites = new Set(favorites);
    newFavorites.add(code);
    setFavorites(newFavorites);
    await saveFavorites(newFavorites);
  };

  // Favori kaldırma
  const removeFavorite = async (code: string) => {
    const newFavorites = new Set(favorites);
    newFavorites.delete(code);
    setFavorites(newFavorites);
    await saveFavorites(newFavorites);
  };

  // Favori durumunu değiştirme (toggle)
  const toggleFavorite = async (code: string) => {
    if (favorites.has(code)) {
      await removeFavorite(code);
    } else {
      await addFavorite(code);
    }
  };

  // Bir kodun favori olup olmadığını kontrol etme
  const isFavorite = (code: string) => {
    return favorites.has(code);
  };

  return (
    <FavoritesContext.Provider
      value={{
        favorites,
        addFavorite,
        removeFavorite,
        toggleFavorite,
        isFavorite,
      }}
    >
      {children}
    </FavoritesContext.Provider>
  );
}

// Hook
export function useFavorites() {
  const context = useContext(FavoritesContext);
  if (context === undefined) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
}
