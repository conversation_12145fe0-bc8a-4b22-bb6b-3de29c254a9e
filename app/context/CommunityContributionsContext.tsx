import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../lib/supabase';
import { Platform } from 'react-native';
import * as Device from 'expo-device';
import { useLocalization } from './LocalizationContext';

// Types for community contributions
export interface ProductContribution {
  id?: number;
  barcode?: string;
  product_name: string;
  ingredients?: string;
  additives?: string[];
  contributor_device_id?: string;
  status?: 'pending' | 'approved' | 'rejected';
  created_at?: string;
}

export interface ErrorReport {
  id?: number;
  additive_code?: string;
  barcode?: string;
  report_type: 'additive_info' | 'product_info' | 'other';
  description: string;
  reporter_device_id?: string;
  status?: 'pending' | 'resolved' | 'rejected';
  created_at?: string;
}

export interface Review {
  id?: number;
  additive_code: string;
  rating?: number;
  comment: string;
  reviewer_device_id?: string;
  status?: 'pending' | 'approved' | 'rejected';
  created_at?: string;
}

// Context type
interface CommunityContributionsContextType {
  // Product contributions
  addProductContribution: (contribution: ProductContribution) => Promise<boolean>;
  getProductContributionByBarcode: (barcode: string) => Promise<ProductContribution | null>;
  
  // Error reports
  submitErrorReport: (report: ErrorReport) => Promise<boolean>;
  
  // Reviews
  submitReview: (review: Review) => Promise<boolean>;
  getReviewsForAdditive: (additiveCode: string) => Promise<Review[]>;
  
  // Loading states
  isLoading: boolean;
  error: Error | null;
}

// Create context
const CommunityContributionsContext = createContext<CommunityContributionsContextType | undefined>(undefined);

// Device ID for anonymous contributions
const DEVICE_ID_STORAGE_KEY = '@deviceId';

// Provider component
export function CommunityContributionsProvider({ children }: { children: ReactNode }) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [deviceId, setDeviceId] = useState<string | null>(null);
  const { t } = useLocalization();

  // Generate or retrieve device ID for anonymous contributions
  useEffect(() => {
    const getDeviceId = async () => {
      try {
        // Try to get stored device ID
        let storedDeviceId = await AsyncStorage.getItem(DEVICE_ID_STORAGE_KEY);
        
        if (!storedDeviceId) {
          // Generate a new device ID if none exists
          const deviceName = Device.deviceName || 'unknown';
          const deviceType = Device.deviceType || 0;
          const platform = Platform.OS;
          const timestamp = Date.now();
          
          // Create a unique ID based on device info and timestamp
          storedDeviceId = `${platform}-${deviceType}-${timestamp}-${Math.random().toString(36).substring(2, 10)}`;
          
          // Store the new device ID
          await AsyncStorage.setItem(DEVICE_ID_STORAGE_KEY, storedDeviceId);
        }
        
        setDeviceId(storedDeviceId);
      } catch (error) {
        console.error('Error getting device ID:', error);
        // Generate a fallback ID if there's an error
        const fallbackId = `fallback-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
        setDeviceId(fallbackId);
      }
    };
    
    getDeviceId();
  }, []);

  // Add product contribution
  const addProductContribution = useCallback(async (contribution: ProductContribution): Promise<boolean> => {
    if (!deviceId) return false;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Add device ID to contribution
      const contributionWithDeviceId = {
        ...contribution,
        contributor_device_id: deviceId,
      };
      
      // Insert into Supabase
      const { error } = await supabase
        .from('product_contributions')
        .insert(contributionWithDeviceId);
      
      if (error) throw error;
      
      return true;
    } catch (error: any) {
      console.error('Error adding product contribution:', error);
      setError(error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [deviceId]);

  // Get product contribution by barcode
  const getProductContributionByBarcode = useCallback(async (barcode: string): Promise<ProductContribution | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Query Supabase for approved contributions with this barcode
      const { data, error } = await supabase
        .from('product_contributions')
        .select('*')
        .eq('barcode', barcode)
        .eq('status', 'approved')
        .order('created_at', { ascending: false })
        .limit(1);
      
      if (error) throw error;
      
      return data && data.length > 0 ? data[0] as ProductContribution : null;
    } catch (error: any) {
      console.error('Error getting product contribution:', error);
      setError(error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Submit error report
  const submitErrorReport = useCallback(async (report: ErrorReport): Promise<boolean> => {
    if (!deviceId) return false;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Add device ID to report
      const reportWithDeviceId = {
        ...report,
        reporter_device_id: deviceId,
      };
      
      // Insert into Supabase
      const { error } = await supabase
        .from('error_reports')
        .insert(reportWithDeviceId);
      
      if (error) throw error;
      
      return true;
    } catch (error: any) {
      console.error('Error submitting error report:', error);
      setError(error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [deviceId]);

  // Submit review
  const submitReview = useCallback(async (review: Review): Promise<boolean> => {
    if (!deviceId) return false;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Add device ID to review
      const reviewWithDeviceId = {
        ...review,
        reviewer_device_id: deviceId,
      };
      
      // Insert into Supabase
      const { error } = await supabase
        .from('reviews')
        .insert(reviewWithDeviceId);
      
      if (error) throw error;
      
      return true;
    } catch (error: any) {
      console.error('Error submitting review:', error);
      setError(error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [deviceId]);

  // Get reviews for additive
  const getReviewsForAdditive = useCallback(async (additiveCode: string): Promise<Review[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Query Supabase for approved reviews for this additive
      const { data, error } = await supabase
        .from('reviews')
        .select('*')
        .eq('additive_code', additiveCode)
        .eq('status', 'approved')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      return data as Review[] || [];
    } catch (error: any) {
      console.error('Error getting reviews:', error);
      setError(error);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  return (
    <CommunityContributionsContext.Provider
      value={{
        addProductContribution,
        getProductContributionByBarcode,
        submitErrorReport,
        submitReview,
        getReviewsForAdditive,
        isLoading,
        error,
      }}
    >
      {children}
    </CommunityContributionsContext.Provider>
  );
}

// Custom hook to use the context
export function useCommunityContributions() {
  const context = useContext(CommunityContributionsContext);
  if (context === undefined) {
    throw new Error('useCommunityContributions must be used within a CommunityContributionsProvider');
  }
  return context;
}
