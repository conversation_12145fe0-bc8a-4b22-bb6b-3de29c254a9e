import React, {
  createContext,
  useState,
  useEffect,
  useContext,
  ReactNode,
} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  supabase,
  AdditiveDBRow,
  AdditiveFull,
  transformAdditiveData,
} from '../lib/supabase'; // Assuming AdditiveFull is also exported from supabase.ts

interface AdditivesContextType {
  additives: AdditiveFull[];
  isLoading: boolean;
  error: Error | null;
  refetchAdditives: () => Promise<void>; // Function to manually trigger refetch
}

const AdditivesContext = createContext<AdditivesContextType | undefined>(
  undefined
);

const ADDITIVES_STORAGE_KEY = 'additivesCache';

export const AdditivesProvider = ({ children }: { children: ReactNode }) => {
  const [additives, setAdditives] = useState<AdditiveFull[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchAndCacheAdditives = async () => {
    setIsLoading(true);
    setError(null);
    console.log('Attempting to fetch additives from Supabase...');

    try {
      const { data, error: dbError } = await supabase
        .from('additives')
        .select('*')
        .order('code', { ascending: true }); // Fetch all rows ordered by code

      if (dbError) {
        throw dbError;
      }

      if (data) {
        console.log(`Fetched ${data.length} additives from Supabase.`);
        const transformedData = data.map((row) =>
          transformAdditiveData(row as AdditiveDBRow)
        );
        setAdditives(transformedData);
        // Cache the fetched data
        try {
          await AsyncStorage.setItem(
            ADDITIVES_STORAGE_KEY,
            JSON.stringify(transformedData)
          );
          console.log('Additives data cached successfully.');
        } catch (cacheError) {
          console.error('Failed to cache additives data:', cacheError);
          // Non-fatal error, continue with fetched data
        }
      } else {
        setAdditives([]); // Handle case where data is null/undefined
      }
      setIsLoading(false);
    } catch (fetchError: any) {
      console.error('Failed to fetch additives from Supabase:', fetchError);
      setError(fetchError);
      // Fallback to AsyncStorage
      console.log('Falling back to AsyncStorage for additives data...');
      try {
        const cachedData = await AsyncStorage.getItem(ADDITIVES_STORAGE_KEY);
        if (cachedData) {
          const parsedData: AdditiveFull[] = JSON.parse(cachedData);
          setAdditives(parsedData);
          console.log(
            `Loaded ${parsedData.length} additives from AsyncStorage cache.`
          );
        } else {
          console.log('No additives data found in AsyncStorage cache.');
          setAdditives([]); // Set empty if cache is also empty
        }
      } catch (cacheError) {
        console.error(
          'Failed to load additives data from AsyncStorage:',
          cacheError
        );
        setAdditives([]); // Set empty if cache reading fails
      } finally {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchAndCacheAdditives();
  }, []); // Fetch on initial mount

  return (
    <AdditivesContext.Provider
      value={{
        additives,
        isLoading,
        error,
        refetchAdditives: fetchAndCacheAdditives,
      }}
    >
      {children}
    </AdditivesContext.Provider>
  );
};

export const useAdditives = (): AdditivesContextType => {
  const context = useContext(AdditivesContext);
  if (context === undefined) {
    throw new Error('useAdditives must be used within an AdditivesProvider');
  }
  return context;
};
