import React, { createContext, useContext, useState } from 'react';
import { useSharedValue } from 'react-native-reanimated';

interface FilterContextType {
  filterVisible: boolean;
  setFilterVisible: (visible: boolean) => void;
  activeFilters: {
    safe: boolean;
    questionable: boolean;
    harmful: boolean;
    halal: boolean;
    haram: boolean;
    mushbooh: boolean;
    avoided: boolean;
    customLabels: string[]; // Array of label IDs
  };
  setActiveFilters: React.Dispatch<
    React.SetStateAction<{
      safe: boolean;
      questionable: boolean;
      harmful: boolean;
      halal: boolean;
      haram: boolean;
      mushbooh: boolean;
      avoided: boolean;
      customLabels: string[];
    }>
  >;
  filterHeight: any; // Using any for Reanimated shared value
}

const FilterContext = createContext<FilterContextType | undefined>(undefined);

export function FilterProvider({ children }: { children: React.ReactNode }) {
  const [filterVisible, setFilterVisible] = useState(false);
  const [activeFilters, setActiveFilters] = useState({
    safe: false,
    questionable: false,
    harmful: false,
    halal: false,
    haram: false,
    mushbooh: false,
    avoided: false,
    customLabels: [] as string[],
  });
  const filterHeight = useSharedValue(0);

  return (
    <FilterContext.Provider
      value={{
        filterVisible,
        setFilterVisible,
        activeFilters,
        setActiveFilters,
        filterHeight,
      }}
    >
      {children}
    </FilterContext.Provider>
  );
}

export function useFilter() {
  const context = useContext(FilterContext);
  if (context === undefined) {
    throw new Error('useFilter must be used within a FilterProvider');
  }
  return context;
}
