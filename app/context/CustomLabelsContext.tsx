import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// AsyncStorage keys
const CUSTOM_LABELS_STORAGE_KEY = '@customLabels';
const ADDITIVE_LABELS_STORAGE_KEY = '@additiveLabels';

// Types
export interface CustomLabel {
  id: string;
  name: string;
  color: string;
}

interface AdditiveLabelsMap {
  [additiveCode: string]: string[]; // Maps additive code to array of label IDs
}

interface CustomLabelsContextType {
  labels: CustomLabel[];
  additiveLabels: AdditiveLabelsMap;
  createLabel: (name: string, color: string) => Promise<CustomLabel>;
  updateLabel: (id: string, name: string, color: string) => Promise<void>;
  deleteLabel: (id: string) => Promise<void>;
  addLabelToAdditive: (additiveCode: string, labelId: string) => Promise<void>;
  removeLabelFromAdditive: (
    additiveCode: string,
    labelId: string
  ) => Promise<void>;
  getLabelsForAdditive: (additiveCode: string) => CustomLabel[];
  hasLabel: (additiveCode: string, labelId: string) => boolean;
}

// Create context
const CustomLabelsContext = createContext<CustomLabelsContextType | undefined>(
  undefined
);

// Provider component
export function CustomLabelsProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [labels, setLabels] = useState<CustomLabel[]>([]);
  const [additiveLabels, setAdditiveLabels] = useState<AdditiveLabelsMap>({});

  // Load data from AsyncStorage
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load custom labels
        const storedLabels = await AsyncStorage.getItem(
          CUSTOM_LABELS_STORAGE_KEY
        );
        if (storedLabels) {
          setLabels(JSON.parse(storedLabels));
          console.log(
            `Loaded ${
              JSON.parse(storedLabels).length
            } custom labels from storage`
          );
        } else {
          // Add some default labels for testing if none exist
          const defaultLabels: CustomLabel[] = [
            { id: '1', name: 'Allergen', color: '#FF5252' },
            { id: '2', name: 'Vegan', color: '#4CAF50' },
            { id: '3', name: 'Gluten Free', color: '#2196F3' },
            { id: '4', name: 'Natural', color: '#8BC34A' },
            { id: '5', name: 'Synthetic', color: '#9C27B0' },
          ];
          setLabels(defaultLabels);
          await AsyncStorage.setItem(
            CUSTOM_LABELS_STORAGE_KEY,
            JSON.stringify(defaultLabels)
          );
          console.log('Added default labels for testing');
        }

        // Load additive-label mappings
        const storedAdditiveLabels = await AsyncStorage.getItem(
          ADDITIVE_LABELS_STORAGE_KEY
        );
        if (storedAdditiveLabels) {
          setAdditiveLabels(JSON.parse(storedAdditiveLabels));
          console.log(`Loaded additive-label mappings from storage`);
        }
      } catch (error) {
        console.error('Failed to load custom labels data from storage:', error);
      }
    };

    loadData();
  }, []);

  // Save labels to AsyncStorage
  const saveLabels = async (newLabels: CustomLabel[]) => {
    try {
      await AsyncStorage.setItem(
        CUSTOM_LABELS_STORAGE_KEY,
        JSON.stringify(newLabels)
      );
      console.log(`Saved ${newLabels.length} custom labels to storage`);
    } catch (error) {
      console.error('Failed to save custom labels to storage:', error);
    }
  };

  // Save additive-label mappings to AsyncStorage
  const saveAdditiveLabels = async (newAdditiveLabels: AdditiveLabelsMap) => {
    try {
      await AsyncStorage.setItem(
        ADDITIVE_LABELS_STORAGE_KEY,
        JSON.stringify(newAdditiveLabels)
      );
      console.log('Saved additive-label mappings to storage');
    } catch (error) {
      console.error(
        'Failed to save additive-label mappings to storage:',
        error
      );
    }
  };

  // Create a new label
  const createLabel = async (
    name: string,
    color: string
  ): Promise<CustomLabel> => {
    const newLabel: CustomLabel = {
      id: Date.now().toString(),
      name,
      color,
    };

    const newLabels = [...labels, newLabel];
    setLabels(newLabels);
    await saveLabels(newLabels);
    return newLabel;
  };

  // Update an existing label
  const updateLabel = async (id: string, name: string, color: string) => {
    const newLabels = labels.map((label) =>
      label.id === id ? { ...label, name, color } : label
    );

    setLabels(newLabels);
    await saveLabels(newLabels);
  };

  // Delete a label
  const deleteLabel = async (id: string) => {
    // Remove the label
    const newLabels = labels.filter((label) => label.id !== id);
    setLabels(newLabels);
    await saveLabels(newLabels);

    // Remove the label from all additives
    const newAdditiveLabels = { ...additiveLabels };

    Object.keys(newAdditiveLabels).forEach((additiveCode) => {
      newAdditiveLabels[additiveCode] = newAdditiveLabels[additiveCode].filter(
        (labelId) => labelId !== id
      );

      // Remove empty arrays
      if (newAdditiveLabels[additiveCode].length === 0) {
        delete newAdditiveLabels[additiveCode];
      }
    });

    setAdditiveLabels(newAdditiveLabels);
    await saveAdditiveLabels(newAdditiveLabels);
  };

  // Add a label to an additive
  const addLabelToAdditive = async (additiveCode: string, labelId: string) => {
    const newAdditiveLabels = { ...additiveLabels };

    if (!newAdditiveLabels[additiveCode]) {
      newAdditiveLabels[additiveCode] = [];
    }

    // Only add if not already present
    if (!newAdditiveLabels[additiveCode].includes(labelId)) {
      newAdditiveLabels[additiveCode].push(labelId);
      setAdditiveLabels(newAdditiveLabels);
      await saveAdditiveLabels(newAdditiveLabels);
    }
  };

  // Remove a label from an additive
  const removeLabelFromAdditive = async (
    additiveCode: string,
    labelId: string
  ) => {
    const newAdditiveLabels = { ...additiveLabels };

    if (newAdditiveLabels[additiveCode]) {
      newAdditiveLabels[additiveCode] = newAdditiveLabels[additiveCode].filter(
        (id) => id !== labelId
      );

      // Remove empty arrays
      if (newAdditiveLabels[additiveCode].length === 0) {
        delete newAdditiveLabels[additiveCode];
      }

      setAdditiveLabels(newAdditiveLabels);
      await saveAdditiveLabels(newAdditiveLabels);
    }
  };

  // Get all labels for a specific additive
  const getLabelsForAdditive = (additiveCode: string): CustomLabel[] => {
    const labelIds = additiveLabels[additiveCode] || [];
    return labels.filter((label) => labelIds.includes(label.id));
  };

  // Check if an additive has a specific label
  const hasLabel = (additiveCode: string, labelId: string): boolean => {
    return (additiveLabels[additiveCode] || []).includes(labelId);
  };

  return (
    <CustomLabelsContext.Provider
      value={{
        labels,
        additiveLabels,
        createLabel,
        updateLabel,
        deleteLabel,
        addLabelToAdditive,
        removeLabelFromAdditive,
        getLabelsForAdditive,
        hasLabel,
      }}
    >
      {children}
    </CustomLabelsContext.Provider>
  );
}

// Custom hook to use the context
export function useCustomLabels() {
  const context = useContext(CustomLabelsContext);
  if (context === undefined) {
    throw new Error(
      'useCustomLabels must be used within a CustomLabelsProvider'
    );
  }
  return context;
}
