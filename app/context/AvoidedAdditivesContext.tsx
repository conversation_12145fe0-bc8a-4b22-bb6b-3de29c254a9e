import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// AsyncStorage key for avoided additives
const AVOIDED_ADDITIVES_STORAGE_KEY = '@avoidedAdditives';

// Context type definition
interface AvoidedAdditivesContextType {
  avoidedAdditives: Set<string>; // Set of avoided E-codes
  addAvoidedAdditive: (code: string) => Promise<void>; // Add an additive to avoid
  removeAvoidedAdditive: (code: string) => Promise<void>; // Remove an additive from avoided list
  toggleAvoidedAdditive: (code: string) => Promise<void>; // Toggle avoided status
  isAvoided: (code: string) => boolean; // Check if a code is in the avoided list
}

// Create context
const AvoidedAdditivesContext = createContext<AvoidedAdditivesContextType | undefined>(undefined);

// Provider component
export function AvoidedAdditivesProvider({ children }: { children: React.ReactNode }) {
  const [avoidedAdditives, setAvoidedAdditives] = useState<Set<string>>(new Set());

  // Load avoided additives from AsyncStorage
  useEffect(() => {
    const loadAvoidedAdditives = async () => {
      try {
        const storedAdditives = await AsyncStorage.getItem(AVOIDED_ADDITIVES_STORAGE_KEY);
        if (storedAdditives) {
          const additivesArray = JSON.parse(storedAdditives) as string[];
          setAvoidedAdditives(new Set(additivesArray));
          console.log(`Loaded ${additivesArray.length} avoided additives from storage`);
        }
      } catch (error) {
        console.error('Failed to load avoided additives from storage:', error);
      }
    };

    loadAvoidedAdditives();
  }, []);

  // Save avoided additives to AsyncStorage
  const saveAvoidedAdditives = async (newAvoidedAdditives: Set<string>) => {
    try {
      const additivesArray = Array.from(newAvoidedAdditives);
      await AsyncStorage.setItem(AVOIDED_ADDITIVES_STORAGE_KEY, JSON.stringify(additivesArray));
      console.log(`Saved ${additivesArray.length} avoided additives to storage`);
    } catch (error) {
      console.error('Failed to save avoided additives to storage:', error);
    }
  };

  // Add an additive to avoid
  const addAvoidedAdditive = async (code: string) => {
    const newAvoidedAdditives = new Set(avoidedAdditives);
    newAvoidedAdditives.add(code);
    setAvoidedAdditives(newAvoidedAdditives);
    await saveAvoidedAdditives(newAvoidedAdditives);
  };

  // Remove an additive from avoided list
  const removeAvoidedAdditive = async (code: string) => {
    const newAvoidedAdditives = new Set(avoidedAdditives);
    newAvoidedAdditives.delete(code);
    setAvoidedAdditives(newAvoidedAdditives);
    await saveAvoidedAdditives(newAvoidedAdditives);
  };

  // Toggle avoided status
  const toggleAvoidedAdditive = async (code: string) => {
    if (avoidedAdditives.has(code)) {
      await removeAvoidedAdditive(code);
    } else {
      await addAvoidedAdditive(code);
    }
  };

  // Check if a code is in the avoided list
  const isAvoided = (code: string) => {
    return avoidedAdditives.has(code);
  };

  return (
    <AvoidedAdditivesContext.Provider
      value={{
        avoidedAdditives,
        addAvoidedAdditive,
        removeAvoidedAdditive,
        toggleAvoidedAdditive,
        isAvoided,
      }}
    >
      {children}
    </AvoidedAdditivesContext.Provider>
  );
}

// Custom hook to use the context
export function useAvoidedAdditives() {
  const context = useContext(AvoidedAdditivesContext);
  if (context === undefined) {
    throw new Error('useAvoidedAdditives must be used within an AvoidedAdditivesProvider');
  }
  return context;
}
