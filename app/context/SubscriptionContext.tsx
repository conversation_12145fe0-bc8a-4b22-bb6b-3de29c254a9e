import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from 'react';
import { Alert, Platform } from 'react-native';
import {
  initializeRevenueCat,
  getSubscriptionOfferings,
  hasActivePremiumSubscription,
  getSubscriptionStatus,
  purchaseSubscription,
  restorePurchases,
  SubscriptionStatus,
} from '../services/revenueCatService';
import {
  getUsageStats,
  shouldShowPaywall,
  trackNewConversation,
  trackNewMessage,
} from '../services/usageTrackingService';
import { PurchasesOffering, PurchasesPackage } from 'react-native-purchases';

// Usage stats interface
export interface UsageStats {
  conversationsUsed: number;
  conversationsLimit: number;
  conversationsRemaining: number;
  messagesPerConversation: Record<string, number>;
  messagesLimit: number;
}

// Context interface
interface SubscriptionContextType {
  // Subscription state
  subscriptionStatus: SubscriptionStatus;
  usageStats: UsageStats;
  offerings: PurchasesOffering | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  refreshSubscriptionStatus: () => Promise<void>;
  refreshUsageStats: () => Promise<void>;
  purchasePackage: (packageToPurchase: PurchasesPackage) => Promise<boolean>;
  restoreUserPurchases: () => Promise<boolean>;
  checkPaywallCondition: (
    action: 'create_conversation' | 'send_message',
    conversationId?: string
  ) => Promise<boolean>;
  trackConversationCreated: (conversationId: string) => Promise<void>;
  trackMessageSent: (conversationId: string) => Promise<void>;
}

// Default values
const defaultSubscriptionStatus: SubscriptionStatus = {
  isPremium: false,
  isActive: false,
  productIdentifier: null,
  expirationDate: null,
  expirationDateMillis: null,
  purchaseDate: null,
  purchaseDateMillis: null,
  willRenew: false,
  isInGracePeriod: false,
  isSandbox: false,
  periodType: null,
  store: null,
  ownershipType: null,
  billingIssuesDetectedAt: null,
  unsubscribeDetectedAt: null,
  refundedAt: null,
};

const defaultUsageStats: UsageStats = {
  conversationsUsed: 0,
  conversationsLimit: 3,
  conversationsRemaining: 3,
  messagesPerConversation: {},
  messagesLimit: 2,
};

// Create context
const SubscriptionContext = createContext<SubscriptionContextType | undefined>(
  undefined
);

// Provider component
export const SubscriptionProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [subscriptionStatus, setSubscriptionStatus] =
    useState<SubscriptionStatus>(defaultSubscriptionStatus);
  const [usageStats, setUsageStats] = useState<UsageStats>(defaultUsageStats);
  const [offerings, setOfferings] = useState<PurchasesOffering | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize RevenueCat and load initial data
  useEffect(() => {
    const initialize = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Initialize RevenueCat
        await initializeRevenueCat();

        // Load subscription status and usage stats in parallel
        await Promise.all([
          refreshSubscriptionStatus(),
          refreshUsageStats(),
          loadOfferings(),
        ]);
      } catch (err) {
        console.error('Failed to initialize subscription context:', err);
        setError('Failed to load subscription data');
      } finally {
        setIsLoading(false);
      }
    };

    initialize();
  }, []);

  // Refresh subscription status
  const refreshSubscriptionStatus = useCallback(async () => {
    try {
      const status = await getSubscriptionStatus();
      setSubscriptionStatus(status);
    } catch (err) {
      console.error('Failed to refresh subscription status:', err);
      setError('Failed to load subscription status');
    }
  }, []);

  // Refresh usage stats
  const refreshUsageStats = useCallback(async () => {
    try {
      const stats = await getUsageStats();
      setUsageStats(stats);
    } catch (err) {
      console.error('Failed to refresh usage stats:', err);
      setError('Failed to load usage statistics');
    }
  }, []);

  // Load offerings
  const loadOfferings = useCallback(async () => {
    try {
      const availableOfferings = await getSubscriptionOfferings();
      setOfferings(availableOfferings);
    } catch (err) {
      console.error('Failed to load offerings:', err);
      setError('Failed to load subscription options');
    }
  }, []);

  // Purchase a package
  const purchasePackage = useCallback(
    async (packageToPurchase: PurchasesPackage): Promise<boolean> => {
      try {
        setIsLoading(true);
        setError(null);

        await purchaseSubscription(packageToPurchase);

        // Refresh subscription status after purchase
        await refreshSubscriptionStatus();

        return true;
      } catch (err) {
        console.error('Failed to purchase subscription:', err);
        setError('Purchase failed. Please try again.');
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [refreshSubscriptionStatus]
  );

  // Restore purchases
  const restoreUserPurchases = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      await restorePurchases();

      // Refresh subscription status after restore
      await refreshSubscriptionStatus();

      return true;
    } catch (err) {
      console.error('Failed to restore purchases:', err);
      setError('Failed to restore purchases');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [refreshSubscriptionStatus]);

  // Check if paywall should be shown
  const checkPaywallCondition = useCallback(
    async (
      action: 'create_conversation' | 'send_message',
      conversationId?: string
    ): Promise<boolean> => {
      // Premium users never see paywall
      if (subscriptionStatus.isPremium) {
        return false;
      }

      // Check usage limits for free users
      return await shouldShowPaywall(action, conversationId);
    },
    [subscriptionStatus.isPremium]
  );

  // Track conversation creation
  const trackConversationCreated = useCallback(
    async (conversationId: string) => {
      try {
        await trackNewConversation(conversationId);
        await refreshUsageStats();
      } catch (err) {
        console.error('Failed to track conversation creation:', err);
      }
    },
    [refreshUsageStats]
  );

  // Track message sent
  const trackMessageSent = useCallback(
    async (conversationId: string) => {
      try {
        await trackNewMessage(conversationId);
        await refreshUsageStats();
      } catch (err) {
        console.error('Failed to track message sent:', err);
      }
    },
    [refreshUsageStats]
  );

  // Context value
  const contextValue: SubscriptionContextType = {
    // State
    subscriptionStatus,
    usageStats,
    offerings,
    isLoading,
    error,

    // Actions
    refreshSubscriptionStatus,
    refreshUsageStats,
    purchasePackage,
    restoreUserPurchases,
    checkPaywallCondition,
    trackConversationCreated,
    trackMessageSent,
  };

  return (
    <SubscriptionContext.Provider value={contextValue}>
      {children}
    </SubscriptionContext.Provider>
  );
};

// Hook to use subscription context
export const useSubscription = (): SubscriptionContextType => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error(
      'useSubscription must be used within a SubscriptionProvider'
    );
  }
  return context;
};
