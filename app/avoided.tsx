import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from './context/LocalizationContext';
import { useAvoidedAdditives } from './context/AvoidedAdditivesContext';
import { localAdditivesService } from './utils/LocalAdditivesService';
import ECodeItem from './components/ECodeItem';

export default function AvoidedScreen() {
  const { t } = useLocalization();
  const router = useRouter();
  const { avoidedAdditives } = useAvoidedAdditives();
  const [expandedCodes, setExpandedCodes] = useState<Set<string>>(new Set());

  // Convert Set to Array for FlatList
  const avoidedList = useMemo(() => {
    return Array.from(avoidedAdditives);
  }, [avoidedAdditives]);

  // Toggle expanded state for a code
  const toggleExpand = useCallback((code: string) => {
    setExpandedCodes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(code)) {
        newSet.delete(code);
      } else {
        newSet.add(code);
      }
      return newSet;
    });
  }, []);

  // Render each item
  const renderItem = useCallback(
    ({ item }: { item: string }) => (
      <ECodeItem
        code={item}
        isExpanded={expandedCodes.has(item)}
        onToggle={() => toggleExpand(item)}
      />
    ),
    [expandedCodes, toggleExpand]
  );

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: t('settings.avoidedAdditives'),
          headerShown: true,
        }}
      />
      <StatusBar barStyle="dark-content" />

      <View style={styles.content}>
        {avoidedList.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="close-circle-outline" size={64} color="#ccc" />
            <Text style={styles.emptyText}>{t('common.avoided')}</Text>
            <Text style={styles.emptySubText}>{t('common.noResults')}</Text>
            <TouchableOpacity
              style={styles.browseButton}
              onPress={() => router.push('/(tabs)/database' as any)}
            >
              <Text style={styles.browseButtonText}>
                {t('favorites.browseCodes')}
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            <View style={styles.headerContainer}>
              <Text style={styles.headerText}>
                {avoidedList.length} {t('common.avoided')}
              </Text>
            </View>
            <FlatList
              data={avoidedList}
              renderItem={renderItem}
              keyExtractor={(item) => item}
              contentContainerStyle={styles.list}
              showsVerticalScrollIndicator={false}
            />
          </>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
  },
  emptySubText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 30,
  },
  browseButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  headerContainer: {
    marginBottom: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  list: {
    paddingBottom: 20,
  },
});
