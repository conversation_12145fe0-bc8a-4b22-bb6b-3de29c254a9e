import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../context/LocalizationContext';
import ECodeItem from '../components/ECodeItem';
import FoodScoreDisplay from '../components/FoodScoreDisplay';
import HistoryImageGallery from '../components/HistoryImageGallery';
import MultiImageViewer from '../components/MultiImageViewer';
import { FoodScore } from '../utils/FoodScoreCalculator';
import { StoredImageInfo } from '../utils/ImageStorage';
import { formatEnergyValue } from '../utils/NutritionFormatter';

export default function ECodeModalScreen() {
  const { t } = useLocalization();
  const router = useRouter();
  const params = useLocalSearchParams();

  // State for image viewer
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [selectedImages, setSelectedImages] = useState<StoredImageInfo[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Parse the passed data
  const foundECodes = params.foundECodes
    ? JSON.parse(params.foundECodes as string)
    : [];
  const expandedCodes = new Set(
    params.expandedCodes ? JSON.parse(params.expandedCodes as string) : []
  );
  const product = params.product ? JSON.parse(params.product as string) : null;
  const foodScore: FoodScore | null = params.foodScore
    ? JSON.parse(params.foodScore as string)
    : null;
  const images: StoredImageInfo[] = params.images
    ? JSON.parse(params.images as string)
    : [];

  const toggleExpand = (code: string) => {
    // This would need to be handled by the parent component
    // For now, we'll just navigate back and let parent handle it
    console.log('Toggle expand:', code);
  };

  const handleReportError = (code?: string) => {
    router.push({
      pathname: '/report',
      params: {
        additiveCode: code,
        barcode: product?.code,
      },
    });
  };

  const handleShowBreakdown = () => {
    router.push({
      pathname: '/modal-stack/score-breakdown' as any,
      params: {
        foodScore: JSON.stringify(foodScore),
      },
    });
  };

  const handleImagePress = (
    images: StoredImageInfo[],
    initialIndex: number
  ) => {
    console.log('Image pressed in modal:', initialIndex);
    setSelectedImages(images);
    setSelectedImageIndex(initialIndex);
    setImageViewerVisible(true);
  };

  const closeImageViewer = () => {
    setImageViewerVisible(false);
    setSelectedImages([]);
    setSelectedImageIndex(0);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Product Info Section */}
        {!!product &&
        (product.brands || product.product_name_en || product?.nutriments) ? (
          <>
            <Text style={styles.modalTitle}>{t('food.productInfo')}</Text>
            {(product.brands || product.product_name_en) && (
              <View style={styles.productSection}>
                {product.brands && (
                  <Text style={styles.sectionTitle}>{product.brands}</Text>
                )}
                {product.product_name_en && (
                  <Text style={styles.productName}>
                    {product.product_name_en}
                  </Text>
                )}
              </View>
            )}
            {product?.nutriments && (
              <View style={styles.nutritionSection}>
                <Text style={styles.sectionTitle}>
                  {t('food.nutritionFacts')}
                </Text>
                <Text>
                  {t('food.energy')}: {formatEnergyValue(product?.nutriments)}
                </Text>
                <Text>
                  Protein: {product?.nutriments?.proteins_100g || '-'}
                  {product?.nutriments?.proteins_100g !== '-' ? 'g' : ''}
                </Text>
                <Text>
                  {t('food.carbohydrate')}:{' '}
                  {product?.nutriments?.carbohydrates_100g || '-'}
                  {product?.nutriments?.carbohydrates_100g !== '-' ? 'g' : ''}
                </Text>
                <Text>
                  {t('food.fat')}: {product?.nutriments?.fat_100g || '-'}
                  {product?.nutriments?.fat_100g !== '-' ? 'g' : ''}
                </Text>
              </View>
            )}

            {/* Report Error Button for Product */}
            <TouchableOpacity
              style={styles.reportButton}
              onPress={() => handleReportError(undefined)}
              activeOpacity={0.7}
            >
              <Ionicons name="alert-circle-outline" size={16} color="#F44336" />
              <Text style={styles.reportButtonText}>
                {t('community.reportIssue')}
              </Text>
            </TouchableOpacity>
          </>
        ) : null}

        {/* Images Gallery */}
        {images && images.length > 0 && (
          <View style={styles.imagesSection}>
            <Text style={styles.sectionTitle}>
              {t('history.scannedImages')}
            </Text>
            <HistoryImageGallery
              images={images}
              onImagePress={handleImagePress}
            />
          </View>
        )}

        {/* Food Score Display */}
        {foodScore && (
          <FoodScoreDisplay
            score={foodScore}
            onShowBreakdown={handleShowBreakdown}
            compact={false}
          />
        )}

        {/* E-codes section */}
        {foundECodes.length > 0 && (
          <>
            <Text style={styles.sectionTitle}>{t('scan.foundECodes')}</Text>
            {foundECodes.map((code: string) => (
              <ECodeItem
                key={code}
                code={code}
                isExpanded={expandedCodes.has(code)}
                onToggle={() => toggleExpand(code)}
              />
            ))}
          </>
        )}

        {/* No E-codes found message */}
        {foundECodes.length === 0 &&
          (foodScore || product || (images && images.length > 0)) && (
            <View style={styles.noECodesSection}>
              <Text style={styles.sectionTitle}>
                {t('scan.analysisResults')}
              </Text>
              <View style={styles.noECodesContainer}>
                <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
                <Text style={styles.noECodesText}>
                  {t('scan.noECodesFound')}
                </Text>
                <Text style={styles.noECodesSubtext}>
                  {t('scan.noECodesFoundDescription')}
                </Text>
              </View>
            </View>
          )}
      </ScrollView>

      {/* Multi Image Viewer Modal */}
      <MultiImageViewer
        visible={imageViewerVisible}
        images={selectedImages}
        initialIndex={selectedImageIndex}
        onClose={closeImageViewer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  productSection: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  productName: {
    fontSize: 16,
    color: '#666',
  },
  nutritionSection: {
    backgroundColor: '#f8f8f8',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
  },
  imagesSection: {
    marginBottom: 20,
  },
  reportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF5F5',
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FFCDD2',
    marginBottom: 15,
  },
  reportButtonText: {
    color: '#F44336',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  noECodesSection: {
    marginTop: 16,
  },
  noECodesContainer: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    marginTop: 8,
  },
  noECodesText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginTop: 8,
    textAlign: 'center',
  },
  noECodesSubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
    lineHeight: 20,
  },
});
