import React from 'react';
import { Stack, useRouter } from 'expo-router';
import { TouchableOpacity, Text } from 'react-native';
import { useLocalization } from '../context/LocalizationContext';

export default function ModalStackLayout() {
  const { t } = useLocalization();
  const router = useRouter();

  return (
    <Stack screenOptions={{ headerShown: true }}>
      <Stack.Screen
        name="ecode-modal"
        options={{
          headerTitle: t('food.productInfo'),
          headerStyle: {
            backgroundColor: 'white',
          },
          headerTitleStyle: {
            fontSize: 18,
            fontWeight: '600',
          },
          headerShadowVisible: false,
          headerRight: () => (
            <TouchableOpacity
              onPress={() => router.back()}
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
              }}
              activeOpacity={0.7}
            >
              <Text
                style={{
                  fontSize: 16,
                  color: '#007AFF',
                  fontWeight: '500',
                }}
              >
                {t('scan.close')}
              </Text>
            </TouchableOpacity>
          ),
        }}
      />
      <Stack.Screen
        name="score-breakdown"
        options={{
          headerTitle: t('foodScore.detailedBreakdown'),
          headerStyle: {
            backgroundColor: 'white',
          },
          headerTitleStyle: {
            fontSize: 18,
            fontWeight: '600',
          },
          headerBackTitle: t('common.back'),
        }}
      />
    </Stack>
  );
}
