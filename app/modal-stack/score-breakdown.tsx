import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import ScoreBreakdown from '../components/ScoreBreakdown';
import { FoodScore } from '../utils/FoodScoreCalculator';

export default function ScoreBreakdownScreen() {
  const params = useLocalSearchParams();

  // Parse the food score from params
  const foodScore: FoodScore = params.foodScore
    ? JSON.parse(params.foodScore as string)
    : null;

  if (!foodScore) {
    return null;
  }

  return (
    <View style={styles.container}>
      <ScoreBreakdown score={foodScore} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
});
