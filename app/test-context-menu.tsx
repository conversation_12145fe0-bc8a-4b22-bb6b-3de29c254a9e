import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import ContextMenu from 'react-native-context-menu-view';
import { useLocalization } from './context/LocalizationContext';

export default function TestContextMenuScreen() {
  const { t } = useLocalization();
  const router = useRouter();

  const handleContextMenuPress = (e: any) => {
    const { index, name } = e.nativeEvent;

    switch (index) {
      case 0:
        Alert.alert('Test', 'Share action selected');
        break;
      case 1:
        Alert.alert('Test', 'Edit action selected');
        break;
      case 2:
        Alert.alert('Test', 'Delete action selected');
        break;
      default:
        console.log('Unknown action:', { index, name });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Context Menu Test',
          headerShown: true,
          headerLeft: () => (
            <TouchableOpacity
              onPress={() => router.back()}
              style={styles.backButton}
            >
              <Ionicons name="arrow-back" size={24} color="#2196F3" />
            </TouchableOpacity>
          ),
        }}
      />

      <View style={styles.content}>
        <Text style={styles.title}>Context Menu Test</Text>
        <Text style={styles.subtitle}>
          Long press on the card below to test the native context menu
        </Text>

        <ContextMenu
          actions={[
            {
              title: 'Share',
              systemIcon: 'square.and.arrow.up',
            },
            {
              title: 'Edit',
              systemIcon: 'pencil',
            },
            {
              title: 'Delete',
              destructive: true,
              systemIcon: 'trash',
            },
          ]}
          onPress={handleContextMenuPress}
        >
          <View style={styles.testCard}>
            <Ionicons name="document-text" size={48} color="#2196F3" />
            <Text style={styles.cardTitle}>Test Card</Text>
            <Text style={styles.cardSubtitle}>
              Long press to open context menu
            </Text>
          </View>
        </ContextMenu>

        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>Test Instructions:</Text>
          <Text style={styles.infoText}>1. Long press on the card above</Text>
          <Text style={styles.infoText}>
            2. Native context menu should appear with 3 options
          </Text>
          <Text style={styles.infoText}>
            3. Select any option to see the alert
          </Text>
          <Text style={styles.infoText}>
            4. Test on both iOS and Android for native behavior
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  backButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 40,
  },
  testCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 40,
    minWidth: 200,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 12,
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  infoSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '100%',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
});
