export type Language = 'tr' | 'en';

export interface Translations {
  common: {
    languageCode: string;
    search: string;
    searchPlaceholder: string;
    noResults: string;
    safe: string;
    questionable: string;
    harmful: string;
    halal: string;
    haram: string;
    mushbooh: string;
    // Short versions for badges
    safeShort: string;
    questionableShort: string;
    harmfulShort: string;
    halalShort: string;
    haramShort: string;
    mushboohShort: string;
    halalDescription: string;
    haramDescription: string;
    mushboohDescription: string;
    category: string;
    detailedInfo: string;
    usageAreas: string;
    safetySummary: string;
    halalSummary: string;
    safeDescription: string;
    questionableDescription: string;
    harmfulDescription: string;
    ecodeDatabase: string;
    filterByStatus: string;
    filterByHalal: string;
    filterByAvoided: string;
    filterByCustomLabels: string;
    resultsFound: string;
    resetFilters: string;
    showFilters: string;
    hideFilters: string;
    ok: string;
    clear: string;
    cancel: string;
    foodAdditives: string;
    scanHistory: string;
    loadingData: string;
    settings: string;
    language: string;
    itemsShown: string;
    loadMore: string;
    addToFavorites: string;
    removeFromFavorites: string;
    favorites: string;
    addedToFavorites: string;
    removedFromFavorites: string;
    addToAvoided: string;
    removeFromAvoided: string;
    avoided: string;
    addedToAvoided: string;
    removedFromAvoided: string;
    customLabels: string;
    manageLabels: string;
    addLabel: string;
    editLabel: string;
    deleteLabel: string;
    labelName: string;
    labelColor: string;
    save: string;
    delete: string;
    confirmDelete: string;
    labelsForAdditive: string;
    selectLabelsForAdditive: string;
    errorSavingLabel: string;
    errorDeletingLabel: string;
    labelNameRequired: string;
    error: string;
    noCustomLabels: string;
    loading: string;
    retry: string;
    goBack: string;
    errorLoading: string;
    submit: string;
    thankYou: string;
    personalization: string;
    personalizationDescription: string;
    personalizationFooter: string;
    items: string;
    labels: string;
    additives: string;
    back: string;
  };
  healthConsultation: {
    tabTitle: string;
    welcomeTitle: string;
    welcomeMessage: string;
    startNewConsultation: string;
    quickTopicsTitle: string;
    quickTopics: {
      nutrition: string;
      symptoms: string;
      wellness: string;
      prevention: string;
    };
    quickTopicReplies: {
      nutrition: string[];
      symptoms: string[];
      wellness: string[];
      prevention: string[];
    };
    recentConversations: string;
    noConversations: string;
    startFirstConsultation: string;
    disclaimer: {
      title: string;
      message: string;
    };
    typeMessage: string;
    aiTyping: string;
    sendMessageError: string;
    retryMessageError: string;
    loading: string;
    loadingConversation: string;
    aiDoctor: string;
    conversationOptions: {
      title: string;
      shareConversation: string;
      deleteConversation: string;
    };
    confirmDelete: {
      title: string;
      message: string;
    };
    error: {
      title: string;
      conversationNotFound: string;
      failedToLoadConversation: string;
    };
    history: {
      title: string;
      searchPlaceholder: string;
      recentSearches: string;
      noConversations: string;
      noSearchResults: string;
      startFirstConsultation: string;
      tryDifferentSearch: string;
      messages: string;
      selected: string;
      deleteConfirmTitle: string;
      deleteConfirmMessage: string;
      archived: string;
      active: string;
      sortBy: string;
      filterBy: string;
      sortOptions: {
        date: string;
        messages: string;
        title: string;
        created: string;
      };
      filterOptions: {
        all: string;
        active: string;
        archived: string;
      };
      deleteTitle: string;
      deleteMessage: string;
      deleteError: string;
      archiveError: string;
      selectAction: string;
    };
    filters: {
      title: string;
      apply: string;
      reset: string;
      dateRange: string;
      startDate: string;
      endDate: string;
      selectDate: string;
      clearDates: string;
    };
    detail: {
      title: string;
      statistics: string;
      totalMessages: string;
      userMessages: string;
      aiResponses: string;
      duration: string;
      averageResponseTime: string;
      lastMessage: string;
      openChat: string;
      created: string;
      lastUpdated: string;
      minutes: string;
      hours: string;
    };
    quickReplies: {
      common: {
        tellMore: string;
        whatShouldDo: string;
        isSerious: string;
        prevention: string;
        whenDoctor: string;
        homeRemedies: string;
      };
      nutrition: {
        healthyDiet: string;
        weightLoss: string;
        vitamins: string;
        mealPlanning: string;
      };
      symptoms: {
        headache: string;
        fatigue: string;
        digestive: string;
        sleepProblems: string;
      };
      wellness: {
        exercise: string;
        stressManagement: string;
        mentalHealth: string;
        sleepHygiene: string;
      };
      prevention: {
        immuneSystem: string;
        screenings: string;
        vaccinations: string;
        lifestyle: string;
      };
    };
  };
  favorites: {
    empty: string;
    emptyDescription: string;
    browseCodes: string;
    saved: string;
    viewFavorites: string;
  };
  tabs: {
    healthConsultation: string;
    health: string;
    database: string;
    scan: string;
    history: string;
    settings: string;
  };
  rating: {
    enjoyingApp: string;
    helpUsImprove: string;
    tapToRate: string;
    terrible: string;
    poor: string;
    okay: string;
    good: string;
    excellent: string;
    maybeLater: string;
    thankYou: string;
  };
  settings: {
    title: string;
    account: string;
    subscription: string;
    subscriptionStatus: string;
    subscriptionActive: string;
    subscriptionExpired: string;
    subscriptionExpires: string;
    subscriptionRenews: string;
    manageSubscription: string;
    upgradeSubscription: string;
    restorePurchases: string;
    language: string;
    personalization: string;
    support: string;
    about: string;
    version: string;
    termsOfService: string;
    privacyPolicy: string;
    contactSupport: string;
    rateApp: string;
    shareApp: string;
    premium: string;
    free: string;
    unlimited: string;
    remaining: string;
    used: string;
    conversations: string;
    messages: string;
    usageStats: string;
    currentPlan: string;
    planDetails: string;
    billingCycle: string;
    nextBilling: string;
    cancelSubscription: string;
    reactivateSubscription: string;
    // Descriptions
    upgradeDescription: string;
    manageDescription: string;
    restoreDescription: string;
    contactDescription: string;
    rateDescription: string;
    shareDescription: string;
    termsDescription: string;
    privacyDescription: string;
    // Messages
    purchasesRestored: string;
    supportSubject: string;
    shareMessage: string;
    avoidedAdditives: string;
    customLabels: string;
  };
  subscription: {
    unlockPremium: string;
    unlimitedConsultations: string;
    currentUsage: string;
    conversations: string;
    messagesPerConversation: string;
    premiumFeatures: string;
    unlimitedConversations: string;
    unlimitedMessages: string;
    prioritySupport: string;
    advancedFeatures: string;
    yearly: string;
    monthly: string;
    save: string;
    perYear: string;
    perMonth: string;
    mo: string;
    startSubscription: string;
    restorePurchases: string;
    termsAndConditions: string;
    purchaseError: string;
    purchaseErrorMessage: string;
    restoreError: string;
    noRestorablePurchases: string;
    restoreErrorMessage: string;
    // New paywall keys
    freeTrialTitle: string;
    timelineNow: string;
    timelineNowDesc: string;
    timelineDay5: string;
    timelineDay5Desc: string;
    timelineDay7: string;
    timelineDay7Desc: string;
    off: string;
    billedAt: string;
    afterFreeTrial: string;
    getStartedFree: string;
    terms: string;
    privacy: string;
  };
  community: {
    // Community contribution features
    communityContributions: string;
    addProductInfo: string;
    reportError: string;
    communityReviews: string;
    addReview: string;
    thankYou: string;

    // Product contribution
    addProductInfoDescription: string;
    barcode: string;
    productName: string;
    productNamePlaceholder: string;
    ingredients: string;
    ingredientsPlaceholder: string;
    productInfoSubmitted: string;
    errorSubmittingProductInfo: string;
    productNameRequired: string;

    // Error reporting
    reportErrorDescription: string;
    additive: string;
    reportType: string;
    additiveInfo: string;
    productInfo: string;
    other: string;
    description: string;
    descriptionPlaceholder: string;
    errorReportSubmitted: string;
    errorSubmittingReport: string;
    descriptionRequired: string;

    // Reviews
    rating: string;
    comment: string;
    commentPlaceholder: string;
    reviewSubmitted: string;
    errorSubmittingReview: string;
    commentRequired: string;
    noReviews: string;

    // Buttons
    submit: string;
    addMissingProduct: string;
    reportIssue: string;
  };
  scan: {
    cameraPermissionRequired: string;
    cameraPermissionDeniedSettings: string;
    goToSettings: string;
    cameraPermissionExplanation: string;
    galleryPermissionRequired: string;
    galleryPermissionExplanation: string;
    allow: string;
    continue: string;
    cancel: string;
    cameraPermissionDenied: string;
    galleryPermissionDenied: string;
    galleryPermissionDeniedSettings: string;
    scanBarcode: string;
    scanText: string;
    noProductOrECodeFound: string;
    noECodeFoundInImage: string;
    productInfoButNoECodes: string;
    foundECodes: string;
    analysisResults: string;
    noECodesFound: string;
    noECodesFoundDescription: string;
    close: string;
    barcode: string;
    text: string;
    barcodeDescription: string;
    textDescription: string;
    barcodeMode: string;
    textMode: string;
    processing: string;
    errorProcessingImage: string;
    preparingCamera: string;
    unknownProduct: string;
    noIngredientsFound: string;
    aiAnalysis: string;
    aiUnavailable: string;
    aiProcessing: string;
    // Guidance and tips
    guidanceTitle: string;
    guidanceSubtitle: string;
    ingredientTip: string;
    positioningTip: string;
    lightingTip: string;
    focusTip: string;
    showGuidance: string;
    hideGuidance: string;
    gotIt: string;
    skipTutorial: string;
    nextTip: string;
    previousTip: string;
    tipCounter: string;
    scanningTips: string;
    bestPractices: string;
    frameIngredients: string;
    holdSteady: string;
    goodLighting: string;
    clearText: string;
    // Visual examples for tutorial
    exampleIngredients: string;
    exampleHoldSteady: string;
    exampleGoodLighting: string;
    exampleClearText: string;
    // Multi-image scanning
    multiImageTitle: string;
    multiImageSubtitle: string;
    step1Title: string;
    step1Description: string;
    step1Tip: string;
    step2Title: string;
    step2Description: string;
    step2Tip: string;
    step3Title: string;
    step3Description: string;
    step3Tip: string;
    stepProgress: string;
    nextImage: string;
    retakeImage: string;
    finishScanning: string;
    imageQualityGood: string;
    imageQualityPoor: string;
    improveImageQuality: string;
    allImagesComplete: string;
    allStepsCompleted: string;
    reviewImages: string;
    startAnalysis: string;
    imageContributed: string;
    fromImage: string;
    ingredientsImage: string;
    nutritionImage: string;
    labelImage: string;
    // Capture feedback
    capturing: string;
    captureSuccess: string;
    // Processing states
    processingImage: string;
    processingImageMessage: string;
    pleaseWait: string;
    doNotTakePhotos: string;
    imageBeingProcessed: string;
    // Button actions
    takePicture: string;
    pickFromGallery: string;
    // Debug mode
    debugMode: string;
    debugModeDescription: string;
    directImageAnalysis: string;
    textExtractionAnalysis: string;
    currentMode: string;
    // AI Processing
    aiAnalyzing: string;
    aiProcessingMessage: string;
    doNotCloseApp: string;
    calculatingScore: string;
    calculatingScoreMessage: string;
    // New sophisticated scanning keys
    step: string;
    of: string;
    tip: {
      ingredients: string;
      nutrition: string;
      general: string;
    };
  };
  history: {
    loadErrorTitle: string;
    loadErrorMessage: string;
    clearConfirmTitle: string;
    clearConfirmMessage: string;
    clearSuccessTitle: string;
    clearSuccessMessage: string;
    clearErrorTitle: string;
    clearErrorMessage: string;
    empty: string;
    clearAll: string;
    codesFound: string;
    none: string;
    textScanTitle: string;
    today: string; // Add date grouping key
    yesterday: string; // Add date grouping key
    older: string; // Add date grouping key
    scannedImages: string;
    originalImage: string;
    barcodeImage: string;
    ingredientImage: string;
    nutritionImage: string;
    labelImage: string;
    image: string;
    viewFullSize: string;
    imageLoadError: string;
    noImagesAvailable: string;
    tapToViewFullSize: string;
    deleteItem: string;
    deleteConfirmTitle: string;
    deleteConfirmMessage: string;
    deleteSuccessMessage: string;
    deleteErrorTitle: string;
    deleteErrorMessage: string;
    swipeToDelete: string;
    undo: string;
    deleted: string;
    viewDetails: string;
    selectAction: string;
  };
  food: {
    productInfo: string;
    nutritionFacts: string;
    energy: string;
    fat: string;
    carbohydrate: string;
  };
  foodScore: {
    overallScore: string;
    health: string;
    quality: string;
    nutrition: string;
    additives: string;
    processing: string;
    recommendation: string;
    showBreakdown: string;
    detailedBreakdown: string;
    howCalculated: string;
    calculationExplanation: string;
    aiCalculationExplanation: string;
    formula: string;
    aiAnalysis: string;
    keyPoints: string;
    overallScoreExplanation: string;
    displayNote: string;
    ingredients: string;
  };
}

export const translations: Record<Language, Translations> = {
  tr: {
    common: {
      languageCode: 'tr',
      search: 'Ara',
      searchPlaceholder: 'E-code, isim veya kategori ara...',
      noResults: 'Sonuç bulunamadı',
      safe: 'Güvenli',
      questionable: 'Şüpheli',
      harmful: 'Zararlı',
      halal: 'Helal',
      haram: 'Haram',
      mushbooh: 'Şüpheli Helal',
      // Short versions for badges
      safeShort: 'Güvenli',
      questionableShort: 'Şüpheli',
      harmfulShort: 'Zararlı',
      halalShort: 'Helal',
      haramShort: 'Haram',
      mushboohShort: 'Şüpheli',
      halalDescription:
        'Bu katkı maddesi İslami kurallara göre helal kabul edilmektedir.',
      haramDescription:
        'Bu katkı maddesi İslami kurallara göre haram kabul edilmektedir.',
      mushboohDescription:
        'Bu katkı maddesinin helal durumu şüphelidir veya kaynağına göre değişebilir.',
      category: 'Kategori',
      detailedInfo: 'Detaylı Bilgi',
      usageAreas: 'Kullanım Alanları',
      safetySummary: 'Güvenlik Özeti',
      halalSummary: 'Helal Durumu',
      safeDescription:
        'Bu katkı maddesi, yaygın araştırmalara göre güvenli kabul edilmektedir. Çoğu insan için yan etki riski düşüktür.',
      questionableDescription:
        'Bu katkı maddesi hakkında çelişkili araştırmalar bulunmaktadır. Bazı durumlarda duyarlı kişilerde reaksiyonlara neden olabilir.',
      harmfulDescription:
        'Bu katkı maddesi, araştırmalara göre zararlı olabilir. Alerjik reaksiyonlar, sağlık sorunları veya diğer yan etkilerle ilişkilendirilmiştir.',
      ecodeDatabase: 'E-Kod Veritabanı',
      filterByStatus: 'Güvenlik Durumuna Göre Filtrele',
      filterByHalal: 'Helal Durumuna Göre Filtrele',
      filterByAvoided: 'Kaçınılan Katkı Maddelerine Göre Filtrele',
      filterByCustomLabels: 'Özel Etiketlere Göre Filtrele',
      resultsFound: 'sonuç bulundu',
      resetFilters: 'Filtreleri Sıfırla',
      showFilters: 'Filtreleri Göster',
      hideFilters: 'Filtreleri Gizle',
      ok: 'Tamam',
      clear: 'Temizle',
      cancel: 'İptal',
      foodAdditives: 'Gıda Katkıları',
      scanHistory: 'Tarama Geçmişi',
      loadingData: 'Veriler yükleniyor...',
      settings: 'Ayarlar',
      language: 'Dil',
      itemsShown: 'öğe gösteriliyor',
      loadMore: 'Daha Fazla Yükle',
      addToFavorites: 'Favorilere Ekle',
      removeFromFavorites: 'Favorilerden Çıkar',
      favorites: 'Favoriler',
      addedToFavorites: 'Favorilere eklendi',
      removedFromFavorites: 'Favorilerden çıkarıldı',
      addToAvoided: 'Kaçınılacaklara Ekle',
      removeFromAvoided: 'Kaçınılacaklardan Çıkar',
      avoided: 'Kaçınılan Katkı Maddeleri',
      addedToAvoided: 'Kaçınılacaklara eklendi',
      removedFromAvoided: 'Kaçınılacaklardan çıkarıldı',
      customLabels: 'Özel Etiketler',
      manageLabels: 'Etiketleri Yönet',
      addLabel: 'Etiket Ekle',
      editLabel: 'Etiketi Düzenle',
      deleteLabel: 'Etiketi Sil',
      labelName: 'Etiket Adı',
      labelColor: 'Etiket Rengi',
      save: 'Kaydet',
      delete: 'Sil',
      confirmDelete: 'Silmek istediğinizden emin misiniz?',
      labelsForAdditive: 'Katkı Maddesi için Etiketler',
      selectLabelsForAdditive: 'Bu katkı maddesi için etiketleri seçin',
      errorSavingLabel: 'Etiket kaydedilirken bir hata oluştu',
      errorDeletingLabel: 'Etiket silinirken bir hata oluştu',
      labelNameRequired: 'Etiket adı gereklidir',
      error: 'Hata',
      noCustomLabels: 'Henüz özel etiket oluşturmadınız',
      loading: 'Yükleniyor...',
      retry: 'Tekrar Dene',
      errorLoading: 'Yükleme hatası',
      submit: 'Gönder',
      thankYou: 'Teşekkürler',
      personalization: 'Kişiselleştirme',
      personalizationDescription:
        'Favorilerinizi, özel etiketlerinizi ve kaçınmak istediğiniz katkı maddelerini yönetin.',
      personalizationFooter:
        'Bu ayarlar sadece cihazınızda saklanır ve gizliliğinizi korur.',
      items: 'öğe',
      labels: 'etiket',
      additives: 'katkı maddesi',
      back: 'Geri',
      goBack: 'Geri Dön',
    },
    favorites: {
      empty: 'Henüz favori eklenmedi',
      emptyDescription:
        'Favori E-kodlarınızı burada görmek için veritabanından E-kodları favorilere ekleyin.',
      browseCodes: 'E-Kodları Görüntüle',
      saved: 'favori kaydedildi',
      viewFavorites: 'Favorileri Görüntüle',
    },
    healthConsultation: {
      tabTitle: 'Sağlık Danışmanlığı',
      welcomeTitle: 'Sağlık Danışmanınız',
      welcomeMessage:
        'Sağlık sorularınız için yapay zeka destekli profesyonel danışmanlık hizmeti. Beslenme, semptomlar ve genel sağlık konularında rehberlik alın.',
      startNewConsultation: 'Yeni Danışmanlık Başlat',
      quickTopicsTitle: 'Hızlı Konular',
      quickTopics: {
        nutrition: 'Beslenme ve Diyet',
        symptoms: 'Semptom Değerlendirmesi',
        wellness: 'Genel Sağlık',
        prevention: 'Koruyucu Sağlık',
      },
      quickTopicReplies: {
        nutrition: [
          'Daha iyi sağlık için ne yemeliyim?',
          'Diyetimi nasıl geliştirebilirim?',
          'Vitamin ve mineraller hakkında bilgi ver',
        ],
        symptoms: [
          'Son zamanlarda yorgun hissediyorum',
          'Baş ağrısına ne sebep olabilir?',
          'Sindirim sorunlarım var',
        ],
        wellness: [
          'Uykumu nasıl geliştirebilirim?',
          'Benim için hangi egzersizler iyi?',
          'Stresi nasıl yönetirim?',
        ],
        prevention: [
          'Yaygın hastalıkları nasıl önlerim?',
          'Hangi sağlık taramalarına ihtiyacım var?',
          'Bağışıklık sistemimi nasıl güçlendiririm?',
        ],
      },
      recentConversations: 'Son Görüşmeler',
      noConversations: 'Henüz görüşme yok',
      startFirstConsultation: 'İlk sağlık danışmanlığınızı başlatın',
      disclaimer: {
        title: 'Önemli Uyarı',
        message:
          'Bu hizmet genel sağlık bilgisi sağlar ve tıbbi teşhis yerine geçmez. Acil durumlar için doktorunuza başvurun.',
      },
      typeMessage: 'Mesajınızı yazın...',
      aiTyping: 'AI yazıyor',
      sendMessageError: 'Mesaj gönderilirken hata oluştu',
      retryMessageError: 'Mesaj tekrar gönderilirken hata oluştu',
      loading: 'Yükleniyor...',
      loadingConversation: 'Görüşme yükleniyor...',
      aiDoctor: 'AI Doktor',
      conversationOptions: {
        title: 'Görüşme Seçenekleri',
        shareConversation: 'Görüşmeyi Paylaş',
        deleteConversation: 'Görüşmeyi Sil',
      },
      confirmDelete: {
        title: 'Görüşmeyi Sil',
        message:
          'Bu görüşmeyi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
      },
      error: {
        title: 'Hata',
        conversationNotFound: 'Görüşme bulunamadı',
        failedToLoadConversation: 'Görüşme yüklenemedi',
      },
      history: {
        title: 'Görüşme Geçmişi',
        searchPlaceholder: 'Görüşmelerde ara...',
        recentSearches: 'Son Aramalar',
        noConversations: 'Henüz görüşme yok',
        noSearchResults: 'Arama sonucu bulunamadı',
        startFirstConsultation: 'İlk danışmanlığınızı başlatın',
        tryDifferentSearch: 'Farklı arama terimleri deneyin',
        messages: 'mesaj',
        selected: 'seçildi',
        deleteConfirmTitle: 'Görüşmeleri Sil',
        deleteConfirmMessage:
          '{{count}} görüşmeyi silmek istediğinizden emin misiniz?',
        archived: 'Arşivlendi',
        active: 'Aktif',
        sortBy: 'Sırala',
        filterBy: 'Filtrele',
        sortOptions: {
          date: 'Tarihe göre',
          messages: 'Mesaj sayısına göre',
          title: 'Başlığa göre',
          created: 'Oluşturulma tarihine göre',
        },
        filterOptions: {
          all: 'Tümü',
          active: 'Aktif',
          archived: 'Arşivlenmiş',
        },
        deleteTitle: 'Görüşmeyi Sil',
        deleteMessage: 'Bu görüşmeyi silmek istediğinizden emin misiniz?',
        deleteError: 'Görüşme silinirken hata oluştu',
        archiveError: 'Görüşme arşivlenirken hata oluştu',
        selectAction: 'Ne yapmak istiyorsunuz?',
      },
      filters: {
        title: 'Filtreler ve Sıralama',
        apply: 'Uygula',
        reset: 'Sıfırla',
        dateRange: 'Tarih Aralığı',
        startDate: 'Başlangıç Tarihi',
        endDate: 'Bitiş Tarihi',
        selectDate: 'Tarih Seç',
        clearDates: 'Tarihleri Temizle',
      },
      detail: {
        title: 'Görüşme Detayları',
        statistics: 'İstatistikler',
        totalMessages: 'Toplam Mesaj',
        userMessages: 'Kullanıcı Mesajları',
        aiResponses: 'AI Yanıtları',
        duration: 'Süre',
        averageResponseTime: 'Ortalama Yanıt Süresi',
        lastMessage: 'Son Mesaj',
        openChat: 'Sohbeti Aç',
        created: 'Oluşturulma',
        lastUpdated: 'Son Güncelleme',
        minutes: 'dk',
        hours: 'sa',
      },
      quickReplies: {
        common: {
          tellMore: 'Daha fazla bilgi ver',
          whatShouldDo: 'Ne yapmalıyım?',
          isSerious: 'Bu ciddi mi?',
          prevention: 'Nasıl önlenir?',
          whenDoctor: 'Ne zaman doktora gitmeli?',
          homeRemedies: 'Evde tedavi yöntemleri?',
        },
        nutrition: {
          healthyDiet: 'Sağlıklı beslenme ipuçları',
          weightLoss: 'Kilo yönetimi',
          vitamins: 'Vitamin ve mineraller',
          mealPlanning: 'Öğün planlaması',
        },
        symptoms: {
          headache: 'Baş ağrısı tedavisi',
          fatigue: 'Yorgunluk hissi',
          digestive: 'Sindirim sorunları',
          sleepProblems: 'Uyku sorunları',
        },
        wellness: {
          exercise: 'Egzersiz rutini',
          stressManagement: 'Stres yönetimi',
          mentalHealth: 'Ruh sağlığı',
          sleepHygiene: 'Daha iyi uyku',
        },
        prevention: {
          immuneSystem: 'Bağışıklığı güçlendir',
          screenings: 'Sağlık taramaları',
          vaccinations: 'Aşılar',
          lifestyle: 'Sağlıklı yaşam tarzı',
        },
      },
    },
    tabs: {
      healthConsultation: 'Sağlık Danışmanlığı',
      health: 'Sağlık',
      database: 'Veritabanı',
      scan: 'Tara',
      history: 'Geçmiş',
      settings: 'Ayarlar',
    },
    settings: {
      title: 'Ayarlar',
      account: 'Hesap',
      subscription: 'Abonelik',
      subscriptionStatus: 'Abonelik Durumu',
      subscriptionActive: 'Aktif',
      subscriptionExpired: 'Süresi Dolmuş',
      subscriptionExpires: 'Bitiş Tarihi',
      subscriptionRenews: 'Yenileme Tarihi',
      manageSubscription: 'Aboneliği Yönet',
      upgradeSubscription: "Premium'a Geç",
      restorePurchases: 'Satın Alımları Geri Yükle',
      language: 'Dil',
      personalization: 'Kişiselleştirme',
      support: 'Destek',
      about: 'Hakkında',
      version: 'Sürüm',
      termsOfService: 'Kullanım Şartları',
      privacyPolicy: 'Gizlilik Politikası',
      contactSupport: 'Destek İletişim',
      rateApp: 'Uygulamayı Değerlendir',
      shareApp: 'Uygulamayı Paylaş',
      premium: 'Premium',
      free: 'Ücretsiz',
      unlimited: 'Sınırsız',
      remaining: 'Kalan',
      used: 'Kullanılan',
      conversations: 'Konuşma',
      messages: 'Mesaj',
      usageStats: 'Kullanım İstatistikleri',
      currentPlan: 'Mevcut Plan',
      planDetails: 'Plan Detayları',
      billingCycle: 'Faturalama Döngüsü',
      nextBilling: 'Sonraki Faturalama',
      cancelSubscription: 'Aboneliği İptal Et',
      reactivateSubscription: 'Aboneliği Yeniden Aktifleştir',
      // Descriptions
      upgradeDescription: 'Tüm özelliklere sınırsız erişim',
      manageDescription: "App Store'da aboneliğinizi yönetin",
      restoreDescription: 'Önceki satın alımları geri yükle',
      contactDescription: 'Yardım ve destek alın',
      rateDescription: "App Store'da bizi değerlendirin",
      shareDescription: 'Arkadaşlarınızla paylaşın',
      termsDescription: 'Kullanım şartlarımızı okuyun',
      privacyDescription: 'Veri gizliliği hakkında bilgi alın',
      // Messages
      purchasesRestored: 'Satın alımlar başarıyla geri yüklendi!',
      supportSubject: 'Destek Talebi',
      shareMessage: 'Bu harika gıda katkı maddeleri uygulamasına göz atın!',
      avoidedAdditives: 'Kaçınılan Katkı Maddeleri',
      customLabels: 'Özel Etiketler',
    },
    subscription: {
      unlockPremium: 'Premium Özellikleri Aç',
      unlimitedConsultations:
        'Sınırsız sağlık danışmanlığı ve gelişmiş özellikler',
      currentUsage: 'Mevcut Kullanım',
      conversations: 'Konuşma',
      messagesPerConversation: 'Konuşma başına mesaj',
      premiumFeatures: 'Premium Özellikler',
      unlimitedConversations: 'Sınırsız konuşma',
      unlimitedMessages: 'Sınırsız mesaj',
      prioritySupport: 'Öncelikli destek',
      advancedFeatures: 'Gelişmiş özellikler',
      yearly: 'Yıllık',
      monthly: 'Aylık',
      save: 'Tasarruf',
      perYear: 'yıllık',
      perMonth: 'aylık',
      mo: 'ay',
      startSubscription: 'Aboneliği Başlat',
      restorePurchases: 'Satın Alımları Geri Yükle',
      termsAndConditions:
        'Abonelik otomatik olarak yenilenir. İptal etmek için App Store ayarlarından aboneliği yönetin.',
      purchaseError: 'Satın Alma Hatası',
      purchaseErrorMessage:
        'Satın alma işlemi başarısız oldu. Lütfen tekrar deneyin.',
      restoreError: 'Geri Yükleme Hatası',
      noRestorablePurchases: 'Geri yüklenebilir satın alma bulunamadı.',
      restoreErrorMessage:
        'Satın alımlar geri yüklenemedi. Lütfen tekrar deneyin.',
      // New paywall keys
      freeTrialTitle: 'Ücretsiz deneme nasıl çalışır',
      timelineNow: 'Şimdi',
      timelineNowDesc: 'Tüm özelliklere tam erişim',
      timelineDay5: '5. Gün',
      timelineDay5Desc:
        'Deneme sürenizin bittiğini e-posta ile hatırlatacağız.',
      timelineDay7: '7. Gün',
      timelineDay7Desc:
        'Deneme süresi biter. Premium ile devam edin veya istediğiniz zaman iptal edin.',
      off: 'İNDİRİM',
      billedAt: 'Faturalandırma:',
      afterFreeTrial: 'ücretsiz denemeden sonra.',
      getStartedFree: 'Ücretsiz başla',
      terms: 'Şartlar',
      privacy: 'Gizlilik',
    },
    scan: {
      cameraPermissionRequired: 'Kamera İzni Gerekli',
      unknownProduct: 'Bilinmeyen Ürün',
      noIngredientsFound: 'Ürün bulundu, ancak içerik listesi algılanamadı.',
      cameraPermissionDeniedSettings:
        'Kamera izni reddedildi. Lütfen ayarlar üzerinden izni etkinleştirin.',
      galleryPermissionDeniedSettings:
        'Galeri izni reddedildi. Lütfen ayarlar üzerinden izni etkinleştirin.',
      goToSettings: "Ayarlar'a Git",
      cameraPermissionExplanation:
        'Barkod ve metin taraması yapmak için kameranıza erişim sağlamamız gerekiyor.',
      galleryPermissionRequired: 'Galeri İzni Gerekli',
      galleryPermissionExplanation:
        'Metin tanıma için galerinize erişim sağlamamız gerekiyor.',
      allow: 'İzin Ver',
      continue: 'Devam Et',
      cancel: 'İptal',
      cameraPermissionDenied: 'Kamera izni reddedildi',
      galleryPermissionDenied: 'Galeri izni reddedildi',
      scanBarcode: 'Barkodu tarat',
      scanText: 'E-kod içeren metni tarat',
      noProductOrECodeFound: 'Ürün veya E-kod bulunamadı',
      noECodeFoundInImage: 'Resimde E-kod bulunamadı',
      productInfoButNoECodes:
        'Ürün bilgileri tanındı ancak E-kodu bulunamadı. Bu bir ürün etiketi olabilir.',
      foundECodes: 'Bulunan E-kodlar',
      analysisResults: 'Analiz Sonuçları',
      noECodesFound: 'E-kod bulunamadı',
      noECodesFoundDescription:
        'Bu üründe zararlı katkı maddesi tespit edilmedi.',
      close: 'Kapat',
      barcode: 'Barkod',
      text: 'Metin',
      barcodeDescription:
        'Ürün bilgilerini ve içeriklerini almak için barkodu tarayın.',
      textDescription:
        'E-kodları AI ile doğrudan tespit etmek için metni tarayın.',
      barcodeMode: 'Barkod Modu',
      textMode: 'Metin Modu',
      processing: 'İşleniyor...',
      preparingCamera: 'Kamera hazırlanıyor...',
      errorProcessingImage:
        'Görüntü işlenirken bir hata oluştu. Lütfen tekrar deneyin.',
      aiAnalysis: 'AI Analizi',
      aiUnavailable: 'AI Kullanılamıyor',
      aiProcessing: 'AI ile işleniyor...',
      // Guidance and tips
      guidanceTitle: 'Tarama İpuçları',
      guidanceSubtitle: 'Daha iyi sonuçlar için',
      ingredientTip: 'İçindekiler listesini çerçeve içine alın',
      positioningTip: 'Telefonu sabit tutun ve metni ortalayın',
      lightingTip: 'İyi aydınlatma altında tarayın',
      focusTip: 'Metin net ve okunabilir olmalı',
      showGuidance: 'İpuçlarını Göster',
      hideGuidance: 'İpuçlarını Gizle',
      gotIt: 'Anladım',
      skipTutorial: 'Atla',
      nextTip: 'Sonraki',
      previousTip: 'Önceki',
      tipCounter: '{{current}}/{{total}}',
      scanningTips: 'Tarama İpuçları',
      bestPractices: 'En İyi Uygulamalar',
      frameIngredients: 'İçindekiler bölümünü çerçeveleyin',
      holdSteady: 'Telefonu sabit tutun',
      goodLighting: 'İyi ışık kullanın',
      clearText: 'Metin net olmalı',
      // Visual examples for tutorial
      exampleIngredients: 'İçindekiler: E200, E300...',
      exampleHoldSteady: '📱 Sabit tutun',
      exampleGoodLighting: '☀️ İyi ışık',
      exampleClearText: 'Net metin',
      // Multi-image scanning
      multiImageTitle: 'Çoklu Görüntü Taraması',
      multiImageSubtitle: 'Daha doğru analiz için 3 görüntü çekin',
      step1Title: 'İçerik Listesi',
      step1Description: 'Ürünün içerik listesini fotoğraflayın',
      step1Tip:
        'E-kodları ve katkı maddelerini algılamak için içerik listesine odaklanın',
      step2Title: 'Besin Değerleri',
      step2Description: 'Besin değerleri tablosunu fotoğraflayın',
      step2Tip:
        'Gıda puanlaması için kalori, yağ, şeker ve tuz bilgilerini yakalayın',
      step3Title: 'Genel Etiket',
      step3Description: 'Ürün etiketinin genel görünümünü fotoğraflayın',
      step3Tip:
        'Ek ürün bilgileri ve eksik detaylar için genel etiketi tarayın',
      stepProgress: '{current} / {total}',
      nextImage: 'Sonraki Görüntü',
      retakeImage: 'Tekrar Çek',
      finishScanning: 'Taramayı Bitir',
      imageQualityGood: 'Görüntü kalitesi iyi',
      imageQualityPoor: 'Görüntü kalitesi düşük',
      improveImageQuality: 'Daha iyi aydınlatma ve odaklama deneyin',
      allImagesComplete: 'Tüm görüntüler tamamlandı',
      allStepsCompleted: 'Tüm adımlar tamamlandı',
      reviewImages: 'Görüntüleri İncele',
      startAnalysis: 'Analizi Başlat',
      imageContributed: 'Bu görüntüden',
      fromImage: "Görüntü {number}'den",
      ingredientsImage: 'İçerik Listesi',
      nutritionImage: 'Besin Değerleri',
      labelImage: 'Genel Etiket',
      // Capture feedback
      capturing: 'Fotoğraf çekiliyor...',
      captureSuccess: 'Fotoğraf başarıyla çekildi!',
      // Processing states
      processingImage: 'Fotoğraf işleniyor...',
      processingImageMessage: 'Fotoğraf işleniyor, lütfen bekleyin...',
      pleaseWait: 'Lütfen bekleyin',
      doNotTakePhotos: 'İşlem tamamlanana kadar yeni fotoğraf çekmeyin',
      imageBeingProcessed: 'Görüntü işleniyor',
      // Button actions
      takePicture: 'Fotoğraf Çek',
      pickFromGallery: 'Galeriden Seç',
      // Debug mode
      debugMode: 'Debug Modu',
      debugModeDescription: 'Farklı analiz yöntemlerini test edin',
      directImageAnalysis: 'Doğrudan Görüntü Analizi',
      textExtractionAnalysis: 'Metin Çıkarma + AI Analizi',
      currentMode: 'Mevcut Mod',
      // AI Processing
      aiAnalyzing: 'AI Analiz Ediyor',
      aiProcessingMessage:
        'Görüntüleriniz AI ile analiz ediliyor. Bu işlem birkaç saniye sürebilir.',
      doNotCloseApp: 'Lütfen analiz tamamlanana kadar uygulamayı kapatmayın',
      calculatingScore: 'Gıda Puanı Hesaplanıyor',
      calculatingScoreMessage:
        'E-kodlar ve besin değerleri analiz edilerek genel sağlık puanı hesaplanıyor.',
      // New sophisticated scanning keys
      step: 'Adım',
      of: '/',
      tip: {
        ingredients: 'İçerik listesini net bir şekilde çerçeveleyin',
        nutrition: 'Beslenme değerleri tablosunu tam olarak görüntüleyin',
        general: 'Ürün etiketinin genel görünümünü yakalayın',
      },
    },
    history: {
      loadErrorTitle: 'Yükleme Hatası',
      loadErrorMessage: 'Tarama geçmişi yüklenemedi.',
      clearConfirmTitle: 'Geçmişi Temizle',
      clearConfirmMessage:
        'Tüm tarama geçmişini silmek istediğinizden emin misiniz?',
      clearSuccessTitle: 'Geçmiş Temizlendi',
      clearSuccessMessage: 'Tarama geçmişi temizlendi.',
      clearErrorTitle: 'Temizleme Hatası',
      clearErrorMessage: 'Tarama geçmişi temizlenemedi.',
      empty: 'Henüz tarama geçmişi yok.',
      clearAll: 'Tüm Geçmişi Temizle',
      codesFound: 'Kodlar',
      none: 'Yok',
      textScanTitle: 'Metin Taraması',
      today: 'Bugün',
      yesterday: 'Dün',
      older: 'Daha Eski',
      scannedImages: 'Taranan Görüntüler',
      originalImage: 'Orijinal Görüntü',
      barcodeImage: 'Barkod',
      ingredientImage: 'İçerik',
      nutritionImage: 'Beslenme Bilgileri',
      labelImage: 'Genel Etiket',
      image: 'Görüntü',
      viewFullSize: 'Tam Boyutta Görüntüle',
      imageLoadError: 'Görüntü yüklenemedi',
      noImagesAvailable: 'Görüntü mevcut değil',
      tapToViewFullSize: 'Tam boyutta görmek için dokunun',
      deleteItem: 'Sil',
      deleteConfirmTitle: 'Öğeyi Sil',
      deleteConfirmMessage:
        'Bu tarama kaydını silmek istediğinizden emin misiniz?',
      deleteSuccessMessage: 'Tarama kaydı silindi',
      deleteErrorTitle: 'Silme Hatası',
      deleteErrorMessage: 'Tarama kaydı silinemedi',
      swipeToDelete: 'Silmek için kaydırın',
      undo: 'Geri Al',
      deleted: 'Silindi',
      viewDetails: 'Detayları Görüntüle',
      selectAction: 'Bir işlem seçin',
    },
    food: {
      productInfo: 'Ürün Bilgileri',
      nutritionFacts: 'Besin Değerleri',
      energy: 'Enerji',
      fat: 'Yağ',
      carbohydrate: 'Karbonhidrat',
    },
    foodScore: {
      overallScore: 'Genel Puan',
      health: 'Sağlık',
      quality: 'Kalite',
      nutrition: 'Beslenme',
      additives: 'Katkı Maddeleri',
      processing: 'İşleme',
      recommendation: 'Öneri',
      showBreakdown: 'Detayları Göster',
      detailedBreakdown: 'Detaylı Analiz',
      howCalculated: 'Nasıl Hesaplanır',
      calculationExplanation:
        'Bu puan, katkı maddeleri (%40), beslenme değeri (%40) ve işleme düzeyi (%20) kriterlerine göre hesaplanır.',
      aiCalculationExplanation:
        'Bu puan, besin verileri, katkı maddeleri ve içeriklerin AI analizi kullanılarak kapsamlı bir sağlık değerlendirmesi sağlamak için hesaplanır.',
      formula: 'Formül',
      aiAnalysis: 'AI Analizi',
      keyPoints: 'Önemli Noktalar',
      overallScoreExplanation:
        'Genel puan, katkı maddeleri, beslenme değeri, işleme seviyesi ve içerik analizi olmak üzere 4 farklı kategorinin ağırlıklı ortalamasıdır.',
      displayNote:
        'Ekranda sadece ana 3 kategori gösterilir, ancak hesaplamaya içerik analizi de dahildir.',
      ingredients: 'İçerik',
    },
    community: {
      communityContributions: 'Topluluk Katkıları',
      addProductInfo: 'Ürün Bilgisi Ekle',
      reportError: 'Hata Bildir',
      communityReviews: 'Yorumlar',
      addReview: 'Yorum Ekle',
      thankYou: 'Teşekkürler',

      // Product contribution
      addProductInfoDescription:
        'Eksik ürün bilgilerini ekleyerek topluluk veritabanına katkıda bulunabilirsiniz.',
      barcode: 'Barkod',
      productName: 'Ürün Adı',
      productNamePlaceholder: 'Ürün adını girin',
      ingredients: 'İçindekiler',
      ingredientsPlaceholder: 'Ürün içeriklerini girin',
      productInfoSubmitted:
        'Ürün bilgisi başarıyla gönderildi. Katkınız için teşekkürler!',
      errorSubmittingProductInfo:
        'Ürün bilgisi gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.',
      productNameRequired: 'Ürün adı gereklidir',

      // Error reporting
      reportErrorDescription:
        'Hatalı veya eksik bilgileri bildirerek veritabanımızı iyileştirmemize yardımcı olabilirsiniz.',
      additive: 'Katkı Maddesi',
      reportType: 'Rapor Türü',
      additiveInfo: 'Katkı Maddesi Bilgisi',
      productInfo: 'Ürün Bilgisi',
      other: 'Diğer',
      description: 'Açıklama',
      descriptionPlaceholder: 'Lütfen hatayı detaylı olarak açıklayın',
      errorReportSubmitted:
        'Hata raporu başarıyla gönderildi. Katkınız için teşekkürler!',
      errorSubmittingReport:
        'Hata raporu gönderilirken bir sorun oluştu. Lütfen daha sonra tekrar deneyin.',
      descriptionRequired: 'Açıklama gereklidir',

      // Reviews
      rating: 'Değerlendirme',
      comment: 'Yorum',
      commentPlaceholder: 'Bu katkı maddesi hakkında düşüncelerinizi paylaşın',
      reviewSubmitted:
        'Yorumunuz başarıyla gönderildi. Katkınız için teşekkürler!',
      errorSubmittingReview:
        'Yorum gönderilirken bir sorun oluştu. Lütfen daha sonra tekrar deneyin.',
      commentRequired: 'Yorum gereklidir',
      noReviews: 'Henüz yorum yok',

      // Buttons
      submit: 'Gönder',
      addMissingProduct: 'Eksik Ürün Ekle',
      reportIssue: 'Sorun Bildir',
    },
    rating: {
      enjoyingApp: 'Uygulamadan Keyif Alıyor Musunuz?',
      helpUsImprove:
        'Deneyiminizi bizimle paylaşın ve uygulamayı geliştirmemize yardımcı olun',
      tapToRate: 'Değerlendirmek için yıldızlara dokunun',
      terrible: 'Çok Kötü',
      poor: 'Kötü',
      okay: 'İdare Eder',
      good: 'İyi',
      excellent: 'Mükemmel',
      maybeLater: 'Belki Daha Sonra',
      thankYou: 'Geri bildiriminiz için teşekkürler!',
    },
  },
  en: {
    common: {
      languageCode: 'en',
      search: 'Search',
      searchPlaceholder: 'Search by E-code, name or category...',
      noResults: 'No results found',
      safe: 'Safe',
      questionable: 'Questionable',
      harmful: 'Harmful',
      halal: 'Halal',
      haram: 'Haram',
      mushbooh: 'Mushbooh',
      // Short versions for badges
      safeShort: 'Safe',
      questionableShort: 'Quest.',
      harmfulShort: 'Harmful',
      halalShort: 'Halal',
      haramShort: 'Haram',
      mushboohShort: 'Mush.',
      halalDescription:
        'This additive is considered halal according to Islamic dietary laws.',
      haramDescription:
        'This additive is considered haram (forbidden) according to Islamic dietary laws.',
      mushboohDescription:
        'The halal status of this additive is doubtful or may vary depending on its source.',
      category: 'Category',
      detailedInfo: 'Detailed Information',
      usageAreas: 'Usage Areas',
      safetySummary: 'Safety Summary',
      halalSummary: 'Halal Status',
      safeDescription:
        'This additive is considered safe according to widespread research. Low risk of side effects for most people.',
      questionableDescription:
        'There are conflicting studies about this additive. May cause reactions in sensitive individuals in some cases.',
      harmfulDescription:
        'This additive may be harmful according to research. It has been associated with allergic reactions, health issues, or other side effects.',
      ecodeDatabase: 'E-Code Database',
      filterByStatus: 'Filter by Safety Status',
      filterByHalal: 'Filter by Halal Status',
      filterByAvoided: 'Filter by Additives to Avoid',
      filterByCustomLabels: 'Filter by Custom Labels',
      resultsFound: 'results found',
      resetFilters: 'Reset Filters',
      showFilters: 'Show Filters',
      hideFilters: 'Hide Filters',
      ok: 'OK',
      clear: 'Clear',
      cancel: 'Cancel',
      foodAdditives: 'Food Additives',
      scanHistory: 'Scan History',
      loadingData: 'Loading data...',
      settings: 'Settings',
      language: 'Language',
      itemsShown: 'items shown',
      loadMore: 'Load More',
      addToFavorites: 'Add to Favorites',
      removeFromFavorites: 'Remove from Favorites',
      favorites: 'Favorites',
      addedToFavorites: 'Added to favorites',
      removedFromFavorites: 'Removed from favorites',
      addToAvoided: 'Add to Avoid List',
      removeFromAvoided: 'Remove from Avoid List',
      avoided: 'Additives to Avoid',
      addedToAvoided: 'Added to avoid list',
      removedFromAvoided: 'Removed from avoid list',
      customLabels: 'Custom Labels',
      manageLabels: 'Manage Labels',
      addLabel: 'Add Label',
      editLabel: 'Edit Label',
      deleteLabel: 'Delete Label',
      labelName: 'Label Name',
      labelColor: 'Label Color',
      save: 'Save',
      delete: 'Delete',
      confirmDelete: 'Are you sure you want to delete this?',
      labelsForAdditive: 'Labels for Additive',
      selectLabelsForAdditive: 'Select labels for this additive',
      errorSavingLabel: 'Error saving label',
      errorDeletingLabel: 'Error deleting label',
      labelNameRequired: 'Label name is required',
      error: 'Error',
      noCustomLabels: 'No custom labels created yet',
      loading: 'Loading...',
      retry: 'Retry',
      errorLoading: 'Error loading',
      submit: 'Submit',
      thankYou: 'Thank You',
      personalization: 'Personalization',
      personalizationDescription:
        'Manage your favorites, custom labels, and additives to avoid.',
      personalizationFooter:
        'These settings are stored only on your device and protect your privacy.',
      items: 'items',
      labels: 'labels',
      additives: 'additives',
      back: 'Back',
      goBack: 'Go Back',
    },
    favorites: {
      empty: 'No favorites yet',
      emptyDescription:
        'Add E-codes to your favorites from the database to see them here.',
      browseCodes: 'Browse E-Codes',
      saved: 'favorites saved',
      viewFavorites: 'View Favorites',
    },
    healthConsultation: {
      tabTitle: 'Health Consultation',
      welcomeTitle: 'Your Health Consultant',
      welcomeMessage:
        'AI-powered professional health consultation service for your health questions. Get guidance on nutrition, symptoms, and general health topics.',
      startNewConsultation: 'Start New Consultation',
      quickTopicsTitle: 'Quick Topics',
      quickTopics: {
        nutrition: 'Nutrition & Diet',
        symptoms: 'Symptom Assessment',
        wellness: 'General Wellness',
        prevention: 'Preventive Health',
      },
      quickTopicReplies: {
        nutrition: [
          'What should I eat for better health?',
          'How can I improve my diet?',
          'Tell me about vitamins and minerals',
        ],
        symptoms: [
          'I have been feeling tired lately',
          'What could cause headaches?',
          'I have digestive issues',
        ],
        wellness: [
          'How can I improve my sleep?',
          'What exercises are good for me?',
          'How to manage stress?',
        ],
        prevention: [
          'How to prevent common illnesses?',
          'What health screenings do I need?',
          'How to boost my immune system?',
        ],
      },
      recentConversations: 'Recent Conversations',
      noConversations: 'No conversations yet',
      startFirstConsultation: 'Start your first health consultation',
      disclaimer: {
        title: 'Important Notice',
        message:
          'This service provides general health information and is not a substitute for medical diagnosis. Consult your doctor for urgent situations.',
      },
      typeMessage: 'Type your message...',
      aiTyping: 'AI is typing',
      sendMessageError: 'Error sending message',
      retryMessageError: 'Error retrying message',
      loading: 'Loading...',
      loadingConversation: 'Loading conversation...',
      aiDoctor: 'AI Doctor',
      conversationOptions: {
        title: 'Conversation Options',
        shareConversation: 'Share Conversation',
        deleteConversation: 'Delete Conversation',
      },
      confirmDelete: {
        title: 'Delete Conversation',
        message:
          'Are you sure you want to delete this conversation? This action cannot be undone.',
      },
      error: {
        title: 'Error',
        conversationNotFound: 'Conversation not found',
        failedToLoadConversation: 'Failed to load conversation',
      },
      history: {
        title: 'Conversation History',
        searchPlaceholder: 'Search conversations...',
        recentSearches: 'Recent Searches',
        noConversations: 'No conversations yet',
        noSearchResults: 'No search results found',
        startFirstConsultation: 'Start your first consultation',
        tryDifferentSearch: 'Try different search terms',
        messages: 'messages',
        selected: 'selected',
        deleteConfirmTitle: 'Delete Conversations',
        deleteConfirmMessage:
          'Are you sure you want to delete {{count}} conversations?',
        archived: 'Archived',
        active: 'Active',
        sortBy: 'Sort by',
        filterBy: 'Filter by',
        sortOptions: {
          date: 'Date',
          messages: 'Message count',
          title: 'Title',
          created: 'Created date',
        },
        filterOptions: {
          all: 'All',
          active: 'Active',
          archived: 'Archived',
        },
        deleteTitle: 'Delete Conversation',
        deleteMessage: 'Are you sure you want to delete this conversation?',
        deleteError: 'Error occurred while deleting conversation',
        archiveError: 'Error occurred while archiving conversation',
        selectAction: 'What would you like to do?',
      },
      filters: {
        title: 'Filters & Sorting',
        apply: 'Apply',
        reset: 'Reset',
        dateRange: 'Date Range',
        startDate: 'Start Date',
        endDate: 'End Date',
        selectDate: 'Select Date',
        clearDates: 'Clear Dates',
      },
      detail: {
        title: 'Conversation Details',
        statistics: 'Statistics',
        totalMessages: 'Total Messages',
        userMessages: 'User Messages',
        aiResponses: 'AI Responses',
        duration: 'Duration',
        averageResponseTime: 'Average Response Time',
        lastMessage: 'Last Message',
        openChat: 'Open Chat',
        created: 'Created',
        lastUpdated: 'Last Updated',
        minutes: 'min',
        hours: 'h',
      },
      quickReplies: {
        common: {
          tellMore: 'Tell me more',
          whatShouldDo: 'What should I do?',
          isSerious: 'Is this serious?',
          prevention: 'How to prevent?',
          whenDoctor: 'When to see a doctor?',
          homeRemedies: 'Home remedies?',
        },
        nutrition: {
          healthyDiet: 'Healthy diet tips',
          weightLoss: 'Weight management',
          vitamins: 'Vitamins & minerals',
          mealPlanning: 'Meal planning',
        },
        symptoms: {
          headache: 'Headache relief',
          fatigue: 'Feeling tired',
          digestive: 'Digestive issues',
          sleepProblems: 'Sleep problems',
        },
        wellness: {
          exercise: 'Exercise routine',
          stressManagement: 'Stress relief',
          mentalHealth: 'Mental wellness',
          sleepHygiene: 'Better sleep',
        },
        prevention: {
          immuneSystem: 'Boost immunity',
          screenings: 'Health screenings',
          vaccinations: 'Vaccinations',
          lifestyle: 'Healthy lifestyle',
        },
      },
    },
    tabs: {
      healthConsultation: 'Health Consultation',
      health: 'Health',
      database: 'Database',
      scan: 'Scan',
      history: 'History',
      settings: 'Settings',
    },
    settings: {
      title: 'Settings',
      account: 'Account',
      subscription: 'Subscription',
      subscriptionStatus: 'Subscription Status',
      subscriptionActive: 'Active',
      subscriptionExpired: 'Expired',
      subscriptionExpires: 'Expires',
      subscriptionRenews: 'Renews',
      manageSubscription: 'Manage Subscription',
      upgradeSubscription: 'Upgrade to Premium',
      restorePurchases: 'Restore Purchases',
      language: 'Language',
      personalization: 'Personalization',
      support: 'Support',
      about: 'About',
      version: 'Version',
      termsOfService: 'Terms of Service',
      privacyPolicy: 'Privacy Policy',
      contactSupport: 'Contact Support',
      rateApp: 'Rate App',
      shareApp: 'Share App',
      premium: 'Premium',
      free: 'Free',
      unlimited: 'Unlimited',
      remaining: 'Remaining',
      used: 'Used',
      conversations: 'Conversations',
      messages: 'Messages',
      usageStats: 'Usage Statistics',
      currentPlan: 'Current Plan',
      planDetails: 'Plan Details',
      billingCycle: 'Billing Cycle',
      nextBilling: 'Next Billing',
      cancelSubscription: 'Cancel Subscription',
      reactivateSubscription: 'Reactivate Subscription',
      // Descriptions
      upgradeDescription: 'Get unlimited access to all features',
      manageDescription: 'Manage your subscription in App Store',
      restoreDescription: 'Restore previous purchases',
      contactDescription: 'Get help and support',
      rateDescription: 'Rate us on the App Store',
      shareDescription: 'Share with friends',
      termsDescription: 'Read our terms and conditions',
      privacyDescription: 'Learn about data privacy',
      // Messages
      purchasesRestored: 'Purchases restored successfully!',
      supportSubject: 'Support Request',
      shareMessage: 'Check out this amazing food additives app!',
      avoidedAdditives: 'Avoided Additives',
      customLabels: 'Custom Labels',
    },
    subscription: {
      unlockPremium: 'Unlock Premium Features',
      unlimitedConsultations:
        'Unlimited health consultations and advanced features',
      currentUsage: 'Current Usage',
      conversations: 'Conversations',
      messagesPerConversation: 'Messages per conversation',
      premiumFeatures: 'Premium Features',
      unlimitedConversations: 'Unlimited conversations',
      unlimitedMessages: 'Unlimited messages',
      prioritySupport: 'Priority support',
      advancedFeatures: 'Advanced features',
      yearly: 'Yearly',
      monthly: 'Monthly',
      save: 'Save',
      perYear: 'per year',
      perMonth: 'per month',
      mo: 'mo',
      startSubscription: 'Start Subscription',
      restorePurchases: 'Restore Purchases',
      termsAndConditions:
        'Subscription automatically renews. Manage subscription in App Store settings to cancel.',
      purchaseError: 'Purchase Error',
      purchaseErrorMessage: 'Purchase failed. Please try again.',
      restoreError: 'Restore Error',
      noRestorablePurchases: 'No restorable purchases found.',
      restoreErrorMessage: 'Failed to restore purchases. Please try again.',
      // New paywall keys
      freeTrialTitle: 'How your free trial works',
      timelineNow: 'Now',
      timelineNowDesc: 'Get full access to all features',
      timelineDay5: 'Day 5',
      timelineDay5Desc:
        "We'll remind you with an email that your trial is ending.",
      timelineDay7: 'Day 7',
      timelineDay7Desc: 'Trial ends. Continue with premium or cancel anytime.',
      off: 'OFF',
      billedAt: 'Billed at',
      afterFreeTrial: 'after free trial.',
      getStartedFree: 'Get started for free',
      terms: 'Terms',
      privacy: 'Privacy',
    },
    scan: {
      cameraPermissionRequired: 'Camera Permission Required',
      unknownProduct: 'Unknown Product',
      noIngredientsFound: 'Product found, but no ingredients list detected.',
      cameraPermissionDeniedSettings:
        'Camera permission denied. Please enable the permission from settings.',
      galleryPermissionDeniedSettings:
        'Gallery permission denied. Please enable the permission from settings.',
      goToSettings: 'Go to Settings',
      cameraPermissionExplanation:
        'We need access to your camera to scan barcodes and text.',
      galleryPermissionRequired: 'Gallery Permission Required',
      galleryPermissionExplanation:
        'We need access to your gallery to select images for text recognition.',
      allow: 'Allow',
      continue: 'Continue',
      cancel: 'Cancel',
      cameraPermissionDenied: 'Camera permission denied',
      galleryPermissionDenied: 'Gallery permission denied',
      scanBarcode: 'Scan the barcode',
      scanText: 'Scan text containing E-codes',
      noProductOrECodeFound: 'No product or E-codes found',
      noECodeFoundInImage: 'No E-codes found in the image',
      productInfoButNoECodes:
        'Product information detected but no E-codes found. This might be a product label.',
      foundECodes: 'Found E-codes',
      analysisResults: 'Analysis Results',
      noECodesFound: 'No E-codes found',
      noECodesFoundDescription:
        'No harmful additives detected in this product.',
      close: 'Close',
      barcode: 'Barcode',
      text: 'Text',
      barcodeDescription:
        'Scan the barcode to retrieve product information and ingredients.',
      textDescription: 'Scan text to directly detect E-codes with AI.',
      barcodeMode: 'Barcode Mode',
      textMode: 'Text Mode',
      processing: 'Processing...',
      preparingCamera: 'Preparing camera...',
      errorProcessingImage:
        'An error occurred while processing the image. Please try again.',
      aiAnalysis: 'AI Analysis',
      aiUnavailable: 'AI Unavailable',
      aiProcessing: 'Processing with AI...',
      // Guidance and tips
      guidanceTitle: 'Scanning Tips',
      guidanceSubtitle: 'For better results',
      ingredientTip: 'Frame the ingredients list clearly',
      positioningTip: 'Hold phone steady and center the text',
      lightingTip: 'Scan under good lighting',
      focusTip: 'Text should be clear and readable',
      showGuidance: 'Show Tips',
      hideGuidance: 'Hide Tips',
      gotIt: 'Got It',
      skipTutorial: 'Skip',
      nextTip: 'Next',
      previousTip: 'Previous',
      tipCounter: '{{current}}/{{total}}',
      scanningTips: 'Scanning Tips',
      bestPractices: 'Best Practices',
      frameIngredients: 'Frame the ingredients section',
      holdSteady: 'Hold phone steady',
      goodLighting: 'Use good lighting',
      clearText: 'Text should be clear',
      // Visual examples for tutorial
      exampleIngredients: 'Ingredients: E200, E300...',
      exampleHoldSteady: '📱 Hold steady',
      exampleGoodLighting: '☀️ Good light',
      exampleClearText: 'Clear text',
      // Multi-image scanning
      multiImageTitle: 'Multi-Image Scanning',
      multiImageSubtitle: 'Capture 3 images for more accurate analysis',
      step1Title: 'Ingredients List',
      step1Description: "Photograph the product's ingredients list",
      step1Tip: 'Focus on the ingredients list to detect E-codes and additives',
      step2Title: 'Nutrition Facts',
      step2Description: 'Photograph the nutrition facts table',
      step2Tip:
        'Capture calories, fat, sugar, and salt information for food scoring',
      step3Title: 'General Label',
      step3Description: 'Photograph the general product label view',
      step3Tip:
        'Scan the general label for additional product information and missing details',
      stepProgress: '{current} / {total}',
      nextImage: 'Next Image',
      retakeImage: 'Retake',
      finishScanning: 'Finish Scanning',
      imageQualityGood: 'Image quality is good',
      imageQualityPoor: 'Image quality is poor',
      improveImageQuality: 'Try better lighting and focus',
      allImagesComplete: 'All images complete',
      allStepsCompleted: 'All steps completed',
      reviewImages: 'Review Images',
      startAnalysis: 'Start Analysis',
      imageContributed: 'From this image',
      fromImage: 'From image {number}',
      ingredientsImage: 'Ingredients List',
      nutritionImage: 'Nutrition Facts',
      labelImage: 'General Label',
      // Capture feedback
      capturing: 'Capturing photo...',
      captureSuccess: 'Photo captured successfully!',
      // Processing states
      processingImage: 'Processing photo...',
      processingImageMessage: 'Photo is being processed, please wait...',
      pleaseWait: 'Please wait',
      doNotTakePhotos: 'Do not take new photos until processing is complete',
      imageBeingProcessed: 'Image being processed',
      // Button actions
      takePicture: 'Take Picture',
      pickFromGallery: 'Pick from Gallery',
      // Debug mode
      debugMode: 'Debug Mode',
      debugModeDescription: 'Test different analysis methods',
      directImageAnalysis: 'Direct Image Analysis',
      textExtractionAnalysis: 'Text Extraction + AI Analysis',
      currentMode: 'Current Mode',
      // AI Processing
      aiAnalyzing: 'AI Analyzing',
      aiProcessingMessage:
        'Your images are being analyzed with AI. This may take a few seconds.',
      doNotCloseApp: 'Please do not close the app until analysis is complete',
      calculatingScore: 'Calculating Food Score',
      calculatingScoreMessage:
        'Analyzing E-codes and nutrition data to calculate overall health score.',
      // New sophisticated scanning keys
      step: 'Step',
      of: 'of',
      tip: {
        ingredients: 'Frame the ingredients list clearly',
        nutrition: 'Capture the nutrition facts table completely',
        general: 'Capture the general product label view',
      },
    },
    history: {
      loadErrorTitle: 'Load Error',
      loadErrorMessage: 'Could not load scan history.',
      clearConfirmTitle: 'Clear History',
      clearConfirmMessage: 'Are you sure you want to delete all scan history?',
      clearSuccessTitle: 'History Cleared',
      clearSuccessMessage: 'Scan history has been cleared.',
      clearErrorTitle: 'Clear Error',
      clearErrorMessage: 'Could not clear scan history.',
      empty: 'No scan history yet.',
      clearAll: 'Clear All History',
      codesFound: 'Codes',
      none: 'None',
      textScanTitle: 'Text Scan',
      today: 'Today',
      yesterday: 'Yesterday',
      older: 'Older',
      scannedImages: 'Scanned Images',
      originalImage: 'Original Image',
      barcodeImage: 'Barcode',
      ingredientImage: 'Ingredient',
      nutritionImage: 'Nutrition Facts',
      labelImage: 'General Label',
      image: 'Image',
      viewFullSize: 'View Full Size',
      imageLoadError: 'Failed to load image',
      noImagesAvailable: 'No images available',
      tapToViewFullSize: 'Tap to view full size',
      deleteItem: 'Delete',
      deleteConfirmTitle: 'Delete Item',
      deleteConfirmMessage: 'Are you sure you want to delete this scan record?',
      deleteSuccessMessage: 'Scan record deleted',
      deleteErrorTitle: 'Delete Error',
      deleteErrorMessage: 'Could not delete scan record',
      swipeToDelete: 'Swipe to delete',
      undo: 'Undo',
      deleted: 'Deleted',
      viewDetails: 'View Details',
      selectAction: 'Select an action',
    },
    food: {
      productInfo: 'Product Information',
      nutritionFacts: 'Nutrition Facts',
      energy: 'Energy',
      fat: 'Fat',
      carbohydrate: 'Carbohydrate',
    },
    foodScore: {
      overallScore: 'Overall Score',
      health: 'Health',
      quality: 'Quality',
      nutrition: 'Nutrition',
      additives: 'Additives',
      processing: 'Processing',
      recommendation: 'Recommendation',
      showBreakdown: 'Show Breakdown',
      detailedBreakdown: 'Detailed Breakdown',
      howCalculated: "How It's Calculated",
      calculationExplanation:
        'This score is calculated based on additives (40%), nutritional value (40%), and processing level (20%).',
      aiCalculationExplanation:
        'This score is calculated using AI analysis of nutrition data, additives, and ingredients to provide a comprehensive health assessment.',
      formula: 'Formula',
      aiAnalysis: 'AI Analysis',
      keyPoints: 'Key Points',
      overallScoreExplanation:
        'The overall score is a weighted average of 4 different categories: additives, nutrition, processing level, and ingredient analysis.',
      displayNote:
        'Only the main 3 categories are displayed on screen, but ingredient analysis is also included in the calculation.',
      ingredients: 'Ingredients',
    },
    community: {
      communityContributions: 'Community Contributions',
      addProductInfo: 'Add Product Info',
      reportError: 'Report Error',
      communityReviews: 'Reviews',
      addReview: 'Add Review',
      thankYou: 'Thank You',

      // Product contribution
      addProductInfoDescription:
        'You can contribute to the community database by adding missing product information.',
      barcode: 'Barcode',
      productName: 'Product Name',
      productNamePlaceholder: 'Enter product name',
      ingredients: 'Ingredients',
      ingredientsPlaceholder: 'Enter product ingredients',
      productInfoSubmitted:
        'Product information successfully submitted. Thank you for your contribution!',
      errorSubmittingProductInfo:
        'An error occurred while submitting product information. Please try again later.',
      productNameRequired: 'Product name is required',

      // Error reporting
      reportErrorDescription:
        'You can help us improve our database by reporting incorrect or missing information.',
      additive: 'Additive',
      reportType: 'Report Type',
      additiveInfo: 'Additive Information',
      productInfo: 'Product Information',
      other: 'Other',
      description: 'Description',
      descriptionPlaceholder: 'Please describe the issue in detail',
      errorReportSubmitted:
        'Error report successfully submitted. Thank you for your contribution!',
      errorSubmittingReport:
        'An error occurred while submitting the report. Please try again later.',
      descriptionRequired: 'Description is required',

      // Reviews
      rating: 'Rating',
      comment: 'Comment',
      commentPlaceholder: 'Share your thoughts about this additive',
      reviewSubmitted:
        'Review successfully submitted. Thank you for your contribution!',
      errorSubmittingReview:
        'An error occurred while submitting the review. Please try again later.',
      commentRequired: 'Comment is required',
      noReviews: 'No reviews yet',

      // Buttons
      submit: 'Submit',
      addMissingProduct: 'Add Missing Product',
      reportIssue: 'Report Issue',
    },
    rating: {
      enjoyingApp: 'Are You Enjoying the App?',
      helpUsImprove:
        'Share your experience with us and help us improve the app',
      tapToRate: 'Tap the stars to rate',
      terrible: 'Terrible',
      poor: 'Poor',
      okay: 'Okay',
      good: 'Good',
      excellent: 'Excellent',
      maybeLater: 'Maybe Later',
      thankYou: 'Thank you for your feedback!',
    },
  },
};
