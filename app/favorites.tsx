import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from './context/LocalizationContext';
import { useFavorites } from './context/FavoritesContext';
import ECodeItem from './components/ECodeItem';
import { Stack } from 'expo-router';

export default function FavoritesScreen() {
  const { t } = useLocalization();
  const { favorites } = useFavorites();
  const router = useRouter();
  const [expandedIds, setExpandedIds] = React.useState<Set<string>>(new Set());

  // Toggle expanded state for an item
  const toggleExpand = (code: string) => {
    setExpandedIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(code)) {
        newSet.delete(code);
      } else {
        newSet.add(code);
      }
      return newSet;
    });
  };

  // Convert favorites Set to Array for FlatList
  const favoritesList = Array.from(favorites);

  // Render each favorite item
  const renderItem = ({ item }: { item: string }) => (
    <ECodeItem
      code={item}
      isExpanded={expandedIds.has(item)}
      onToggle={() => toggleExpand(item)}
    />
  );

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      <Stack.Screen options={{ title: t('common.favorites') }} />

      {favoritesList.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="heart-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>{t('favorites.empty')}</Text>
          <Text style={styles.emptySubText}>
            {t('favorites.emptyDescription')}
          </Text>
          <TouchableOpacity
            style={styles.browseButton}
            onPress={() => router.push('/(tabs)/database' as any)}
          >
            <Text style={styles.browseButtonText}>
              {t('favorites.browseCodes')}
            </Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <View style={styles.headerContainer}>
            <Text style={styles.headerText}>
              {favoritesList.length} {t('favorites.saved')}
            </Text>
          </View>
          <FlatList
            data={favoritesList}
            renderItem={renderItem}
            keyExtractor={(item) => item}
            contentContainerStyle={styles.list}
            showsVerticalScrollIndicator={false}
          />
        </>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  headerContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
    backgroundColor: '#fff',
  },
  headerText: {
    fontSize: 16,
    color: '#666',
  },
  list: {
    padding: 16,
  },
  emptyContainer: {
    paddingTop: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 22,
  },
  browseButton: {
    marginTop: 24,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  browseButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
