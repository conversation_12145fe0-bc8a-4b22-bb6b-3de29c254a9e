// Health Consultation Data Models
// TypeScript interfaces and types for health consultation feature

/**
 * Represents a health consultation conversation
 */
export interface HealthConversation {
  id: string;
  title: string;
  deviceId?: string; // For anonymous users
  createdAt: number; // Unix timestamp
  updatedAt: number; // Unix timestamp
  messageCount: number;
  lastMessage?: string;
  isActive: boolean;
  isArchived?: boolean; // For archiving conversations
  metadata?: {
    topic?: string; // Quick topic if started from quick topics
    language?: 'en' | 'tr';
    tags?: string[]; // For categorization
  };
}

/**
 * Message types supported in health consultation
 */
export type HealthMessageType =
  | 'text' // Regular text message
  | 'quick_reply' // Quick reply button response
  | 'assessment' // Health assessment questionnaire
  | 'recommendation' // AI recommendation with structured data
  | 'system'; // System messages (disclaimers, etc.)

/**
 * Message role - who sent the message
 */
export type HealthMessageRole = 'user' | 'assistant' | 'system';

/**
 * Represents a single message in a health consultation
 */
export interface HealthMessage {
  id: string;
  conversationId: string;
  role: HealthMessageRole;
  content: string;
  messageType: HealthMessageType;
  timestamp: number; // Unix timestamp

  // AI Response Tracking
  aiResponseStatus?: 'pending' | 'completed' | 'failed';
  aiResponseError?: string;
  pairedMessageId?: string; // Links user message to AI response

  metadata?: {
    // For quick replies
    quickReplyOptions?: string[];
    selectedQuickReply?: string;

    // For assessments
    assessmentData?: {
      questions: AssessmentQuestion[];
      responses: AssessmentResponse[];
      score?: number;
    };

    // For recommendations
    recommendationData?: {
      category: string;
      priority: 'low' | 'medium' | 'high';
      actionItems: string[];
      followUpSuggestions?: string[];
    };

    // For system messages
    systemMessageType?: 'disclaimer' | 'warning' | 'info';

    // General metadata
    language?: 'en' | 'tr';
    processingTime?: number; // AI response time in ms
    modelUsed?: string; // Which AI model was used
    retryCount?: number; // Number of retries if any
  };
}

/**
 * Assessment question structure
 */
export interface AssessmentQuestion {
  id: string;
  question: string;
  type: 'multiple_choice' | 'scale' | 'text' | 'yes_no';
  options?: string[]; // For multiple choice
  scaleMin?: number; // For scale questions
  scaleMax?: number; // For scale questions
  required: boolean;
}

/**
 * Assessment response structure
 */
export interface AssessmentResponse {
  questionId: string;
  answer: string | number;
  timestamp: number;
}

/**
 * Quick topic configuration
 */
export interface QuickTopic {
  id: string;
  titleKey: string; // Translation key
  icon: string; // Ionicons name
  color: string; // Hex color
  systemPrompt?: string; // Custom system prompt for this topic
  quickReplies?: string[]; // Suggested quick replies
}

/**
 * Health consultation session state
 */
export interface HealthConsultationState {
  conversations: HealthConversation[];
  activeConversation: HealthConversation | null;
  messages: HealthMessage[];
  isLoading: boolean;
  typingConversations: Set<string>; // Track which conversations are typing
  error: string | null;

  // UI state
  showQuickTopics: boolean;
  selectedTopic: QuickTopic | null;

  // Pagination for messages
  messagesPagination: {
    page: number;
    hasMore: boolean;
    loading: boolean;
  };
}

/**
 * Health consultation context actions
 */
export interface HealthConsultationActions {
  // Conversation management
  createConversation: (
    title?: string,
    topic?: QuickTopic
  ) => Promise<HealthConversation>;
  loadConversation: (id: string) => Promise<void>;
  deleteConversation: (id: string) => Promise<void>;
  archiveConversation: (id: string) => Promise<void>;
  unarchiveConversation: (id: string) => Promise<void>;
  updateConversationTitle: (id: string, title: string) => Promise<void>;
  loadConversations: () => Promise<void>;

  // Message management
  sendMessage: (
    content: string,
    messageType?: HealthMessageType
  ) => Promise<void>;
  loadMessages: (conversationId: string, page?: number) => Promise<void>;
  loadMoreMessages: () => Promise<void>;
  retryMessage: (messageId: string) => Promise<void>;

  // Quick actions
  sendQuickReply: (reply: string) => Promise<void>;
  startAssessment: (assessmentType: string) => Promise<void>;

  // UI actions
  setActiveConversation: (conversation: HealthConversation | null) => void;
  setTyping: (conversationId: string, isTyping: boolean) => void;
  isConversationTyping: (conversationId: string) => boolean;
  clearError: () => void;

  // Topic actions
  selectQuickTopic: (topic: QuickTopic) => void;
  clearSelectedTopic: () => void;
}

/**
 * API request/response types
 */
export interface SendMessageRequest {
  conversationId: string;
  content: string;
  messageType: HealthMessageType;
  language: 'en' | 'tr';
  systemPrompt?: string;
}

export interface SendMessageResponse {
  message: HealthMessage;
  aiResponse: HealthMessage;
  conversationUpdated: HealthConversation;
  savedToDatabase: boolean; // Flag to indicate if messages were saved to database
  databaseError?: any; // Error details if database save failed
}

export interface CreateConversationRequest {
  title?: string;
  deviceId: string;
  topic?: string;
  language: 'en' | 'tr';
}

export interface CreateConversationResponse {
  conversation: HealthConversation;
}

/**
 * Storage keys for AsyncStorage
 */
export const HEALTH_CONSULTATION_STORAGE_KEYS = {
  CONVERSATIONS: '@health_conversations',
  MESSAGES: '@health_messages',
  ACTIVE_CONVERSATION: '@health_active_conversation',
  USER_PREFERENCES: '@health_user_preferences',
} as const;

/**
 * Helper function to get default quick topics with translations
 */
export const getDefaultQuickTopics = (t: any): QuickTopic[] => [
  {
    id: 'nutrition',
    titleKey: 'healthConsultation.quickTopics.nutrition',
    icon: 'nutrition-outline',
    color: '#4CAF50',
    quickReplies: t('healthConsultation.quickTopicReplies.nutrition'),
  },
  {
    id: 'symptoms',
    titleKey: 'healthConsultation.quickTopics.symptoms',
    icon: 'medical-outline',
    color: '#FF9800',
    quickReplies: t('healthConsultation.quickTopicReplies.symptoms'),
  },
  {
    id: 'wellness',
    titleKey: 'healthConsultation.quickTopics.wellness',
    icon: 'heart-outline',
    color: '#2196F3',
    quickReplies: t('healthConsultation.quickTopicReplies.wellness'),
  },
  {
    id: 'prevention',
    titleKey: 'healthConsultation.quickTopics.prevention',
    icon: 'shield-checkmark-outline',
    color: '#9C27B0',
    quickReplies: t('healthConsultation.quickTopicReplies.prevention'),
  },
];

/**
 * Default quick topics configuration (deprecated - use getDefaultQuickTopics)
 */
export const DEFAULT_QUICK_TOPICS: QuickTopic[] = [
  {
    id: 'nutrition',
    titleKey: 'healthConsultation.quickTopics.nutrition',
    icon: 'nutrition-outline',
    color: '#4CAF50',
    quickReplies: [
      'What should I eat for better health?',
      'How can I improve my diet?',
      'Tell me about vitamins and minerals',
    ],
  },
  {
    id: 'symptoms',
    titleKey: 'healthConsultation.quickTopics.symptoms',
    icon: 'medical-outline',
    color: '#FF9800',
    quickReplies: [
      'I have been feeling tired lately',
      'What could cause headaches?',
      'I have digestive issues',
    ],
  },
  {
    id: 'wellness',
    titleKey: 'healthConsultation.quickTopics.wellness',
    icon: 'heart-outline',
    color: '#2196F3',
    quickReplies: [
      'How can I improve my sleep?',
      'What exercises are good for me?',
      'How to manage stress?',
    ],
  },
  {
    id: 'prevention',
    titleKey: 'healthConsultation.quickTopics.prevention',
    icon: 'shield-checkmark-outline',
    color: '#9C27B0',
    quickReplies: [
      'How to prevent common illnesses?',
      'What health screenings do I need?',
      'How to boost my immune system?',
    ],
  },
];

/**
 * Utility type for creating new conversations
 */
export type CreateHealthConversationData = Omit<
  HealthConversation,
  'id' | 'createdAt' | 'updatedAt' | 'messageCount' | 'lastMessage'
>;

/**
 * Utility type for creating new messages
 */
export type CreateHealthMessageData = Omit<HealthMessage, 'id' | 'timestamp'>;

/**
 * Error types for health consultation
 */
export interface HealthConsultationError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

/**
 * Common error codes
 */
export const HEALTH_CONSULTATION_ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  API_ERROR: 'API_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  STORAGE_ERROR: 'STORAGE_ERROR',
  CONVERSATION_NOT_FOUND: 'CONVERSATION_NOT_FOUND',
  MESSAGE_SEND_FAILED: 'MESSAGE_SEND_FAILED',
  AI_SERVICE_UNAVAILABLE: 'AI_SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
} as const;
