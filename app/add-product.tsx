import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  Dimensions,
  KeyboardAvoidingView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from './context/LocalizationContext';
import {
  useCommunityContributions,
  ProductContribution,
} from './context/CommunityContributionsContext';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';

const SCREEN_HEIGHT =
  Platform.OS === 'web' ? window.innerHeight : Dimensions.get('window').height;
const SCREEN_WIDTH =
  Platform.OS === 'web' ? window.innerWidth : Dimensions.get('window').width;

export default function AddProductScreen() {
  const { barcode } = useLocalSearchParams<{ barcode?: string }>();
  const router = useRouter();
  const { t } = useLocalization();
  const { addProductContribution, isLoading } = useCommunityContributions();

  // Form state
  const [productName, setProductName] = useState('');
  const [ingredients, setIngredients] = useState('');

  // Reset form
  const resetForm = () => {
    setProductName('');
    setIngredients('');
  };

  // Handle submission
  const handleSubmit = async () => {
    if (!productName.trim()) {
      Alert.alert(t('community.error'), t('community.productNameRequired'));
      return;
    }

    const contribution: ProductContribution = {
      barcode: barcode,
      product_name: productName.trim(),
      ingredients: ingredients.trim(),
    };

    const success = await addProductContribution(contribution);

    if (success) {
      Alert.alert(
        t('community.thankYou'),
        t('community.productInfoSubmitted'),
        [
          {
            text: t('common.ok'),
            onPress: () => {
              resetForm();
              router.back();
            },
          },
        ]
      );
    } else {
      Alert.alert(
        t('community.error'),
        t('community.errorSubmittingProductInfo')
      );
    }
  };

  return (
    <>
      <StatusBar barStyle="dark-content" />
      <Stack.Screen
        options={{
          title: t('community.addProductInfo'),
          headerTitleStyle: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#333',
          },
          headerStyle: {
            backgroundColor: '#fff',
          },
        }}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.container}
      >
        <ScrollView
          style={styles.formContainer}
          contentContainerStyle={styles.formContentContainer}
        >
          <Text style={styles.description}>
            {t('community.addProductInfoDescription')}
          </Text>

          {barcode && (
            <View style={styles.infoContainer}>
              <Text style={styles.infoLabel}>{t('community.barcode')}:</Text>
              <Text style={styles.infoValue}>{barcode}</Text>
            </View>
          )}

          <Text style={styles.inputLabel}>{t('community.productName')} *</Text>
          <TextInput
            style={styles.textInput}
            value={productName}
            onChangeText={setProductName}
            placeholder={t('community.productNamePlaceholder')}
            placeholderTextColor="#999"
            autoCapitalize="words"
          />

          <Text style={styles.inputLabel}>{t('community.ingredients')}</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            value={ingredients}
            onChangeText={setIngredients}
            placeholder={t('community.ingredientsPlaceholder')}
            placeholderTextColor="#999"
            multiline
            numberOfLines={5}
            textAlignVertical="top"
          />

          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleSubmit}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.submitButtonText}>
                {t('community.submit')}
              </Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  formContainer: {
    flex: 1,
  },
  formContentContainer: {
    padding: 20,
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    lineHeight: 22,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 12,
    marginBottom: 15,
  },
  infoLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: '#555',
    marginRight: 10,
  },
  infoValue: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
  },
  inputLabel: {
    fontSize: 17,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
    marginTop: 5,
  },
  textInput: {
    backgroundColor:
      Platform.OS === 'ios' ? 'rgba(245, 245, 245, 0.8)' : '#f5f5f5',
    borderRadius: 12,
    padding: 15,
    fontSize: 16,
    color: '#333',
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  textArea: {
    minHeight: 150,
  },
  submitButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
    shadowColor: '#388E3C',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
