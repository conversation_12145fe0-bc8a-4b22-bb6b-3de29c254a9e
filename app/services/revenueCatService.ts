import Purchases, {
  LOG_LEVEL,
  PurchasesOffering,
  PurchasesPackage,
  CustomerInfo,
} from 'react-native-purchases';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getApiConfiguration } from '../lib/supabase';

// RevenueCat configuration
const REVENUECAT_OFFERING_ID = 'default'; // Use the actual offering ID from logs
const PRODUCT_IDS = {
  MONTHLY: '$rc_monthly', // Use the actual package identifier from logs
  YEARLY: '$rc_yearly', // Assuming yearly follows same pattern
} as const;

// Enhanced subscription status interface based on actual RevenueCat data
export interface SubscriptionStatus {
  isPremium: boolean;
  isActive: boolean;
  productIdentifier: string | null;
  expirationDate: string | null;
  expirationDateMillis: number | null;
  purchaseDate: string | null;
  purchaseDateMillis: number | null;
  willRenew: boolean;
  isInGracePeriod: boolean;
  isSandbox: boolean;
  periodType: 'NORMAL' | 'TRIAL' | 'INTRO' | null;
  store:
    | 'APP_STORE'
    | 'PLAY_STORE'
    | 'AMAZON'
    | 'MAC_APP_STORE'
    | 'PROMOTIONAL'
    | null;
  ownershipType: 'PURCHASED' | 'FAMILY_SHARED' | 'UNKNOWN' | null;
  billingIssuesDetectedAt: string | null;
  unsubscribeDetectedAt: string | null;
  refundedAt: string | null;
}

// Helper interface for active subscription details
export interface ActiveSubscriptionDetails {
  productIdentifier: string;
  expirationDate: string;
  expirationDateMillis: number;
  purchaseDate: string;
  purchaseDateMillis: number;
  willRenew: boolean;
  isInGracePeriod: boolean;
  isSandbox: boolean;
  periodType: 'NORMAL' | 'TRIAL' | 'INTRO';
  store:
    | 'APP_STORE'
    | 'PLAY_STORE'
    | 'AMAZON'
    | 'MAC_APP_STORE'
    | 'PROMOTIONAL';
  ownershipType: 'PURCHASED' | 'FAMILY_SHARED' | 'UNKNOWN';
  billingIssuesDetectedAt: string | null;
  unsubscribeDetectedAt: string | null;
  refundedAt: string | null;
}

// Unified device ID key
const UNIFIED_DEVICE_ID_KEY = '@unified_device_id';

/**
 * Get or create a unique user ID for RevenueCat
 */
const getOrCreateUniqueUserId = async (): Promise<string> => {
  try {
    // Try to get existing unified device ID
    let deviceId = await AsyncStorage.getItem(UNIFIED_DEVICE_ID_KEY);

    if (deviceId) {
      console.log('📱 Using existing unified device ID:', deviceId);
      return deviceId;
    }

    // Generate new unique device ID
    deviceId = `user_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 15)}`;

    // Save to AsyncStorage
    await AsyncStorage.setItem(UNIFIED_DEVICE_ID_KEY, deviceId);

    console.log('🆕 Generated new unified device ID:', deviceId);
    return deviceId;
  } catch (error) {
    console.error('❌ Failed to get/create unique user ID:', error);
    // Return temporary ID
    return `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }
};

/**
 * Initialize RevenueCat SDK and ensure user is logged in
 * Call this once when the app starts
 */
export const initializeRevenueCat = async (): Promise<string> => {
  try {
    // Get API key from Supabase
    const revenueCatConfig = await getApiConfiguration('revenuecat');
    const apiKey = revenueCatConfig?.api_key;

    if (!apiKey) {
      console.log(
        '⚠️ RevenueCat API key not found in Supabase, using fallback device ID system'
      );
      // Return fallback device ID
      const fallbackId = await getOrCreateUniqueUserId();
      return fallbackId;
    }

    // Set log level for debugging (remove in production)
    Purchases.setLogLevel(LOG_LEVEL.INFO);

    // Configure RevenueCat
    await Purchases.configure({ apiKey });

    // Generate or get unique user ID
    const uniqueUserId = await getOrCreateUniqueUserId();

    // Log in user to RevenueCat
    const { customerInfo } = await Purchases.logIn(uniqueUserId);

    console.log(
      '✅ RevenueCat initialized and user logged in:',
      customerInfo.originalAppUserId
    );
    return customerInfo.originalAppUserId;
  } catch (error) {
    console.log(
      '⚠️ RevenueCat not available, using fallback device ID:',
      error instanceof Error ? error.message : 'Unknown error'
    );
    // Return fallback device ID
    const fallbackId = await getOrCreateUniqueUserId();
    return fallbackId;
  }
};

/**
 * Get RevenueCat customer info
 * Returns null if RevenueCat is not available
 */
export const getRevenueCatCustomerInfo = async () => {
  try {
    const customerInfo = await Purchases.getCustomerInfo();
    return customerInfo;
  } catch (error) {
    console.log('⚠️ RevenueCat customer info not available:', error);
    return null;
  }
};

/**
 * Get RevenueCat user ID
 * Returns null if not available
 */
export const getRevenueCatUserId = async (): Promise<string | null> => {
  try {
    const customerInfo = await getRevenueCatCustomerInfo();
    return customerInfo?.originalAppUserId || null;
  } catch (error) {
    console.log('⚠️ RevenueCat user ID not available:', error);
    return null;
  }
};

/**
 * Set RevenueCat user ID (for login/registration)
 */
export const setRevenueCatUserId = async (userId: string): Promise<void> => {
  try {
    await Purchases.logIn(userId);
    console.log('✅ RevenueCat user ID set:', userId);
  } catch (error) {
    console.error('❌ Failed to set RevenueCat user ID:', error);
    throw error;
  }
};

/**
 * Clear RevenueCat user (for logout)
 */
export const clearRevenueCatUser = async (): Promise<void> => {
  try {
    await Purchases.logOut();
    console.log('✅ RevenueCat user cleared');
  } catch (error) {
    console.error('❌ Failed to clear RevenueCat user:', error);
    throw error;
  }
};

/**
 * Get the unified device ID (RevenueCat user ID)
 * This is the single source of truth for user identification
 */
export const getUnifiedDeviceId = async (): Promise<string> => {
  try {
    // Try to get from RevenueCat first
    const customerInfo = await getRevenueCatCustomerInfo();
    if (customerInfo?.originalAppUserId) {
      return customerInfo.originalAppUserId;
    }

    // Fallback: get from AsyncStorage
    const deviceId = await AsyncStorage.getItem(UNIFIED_DEVICE_ID_KEY);
    if (deviceId) {
      return deviceId;
    }

    // Last resort: generate new ID and initialize RevenueCat
    console.log('⚠️ No unified device ID found, initializing...');
    return await initializeRevenueCat();
  } catch (error) {
    console.error('❌ Failed to get unified device ID:', error);
    // Emergency fallback
    return `emergency_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 15)}`;
  }
};

/**
 * Get available subscription offerings from RevenueCat
 */
export const getSubscriptionOfferings =
  async (): Promise<PurchasesOffering | null> => {
    try {
      const offerings = await Purchases.getOfferings();

      // Log offerings in a more readable format
      if (offerings) {
        console.log('🛒 RevenueCat offerings received');

        if (offerings.current) {
          console.log('🛒 Current offering:', offerings.current.identifier);

          if (offerings.current.availablePackages) {
            console.log(
              `🛒 Available packages in current offering: ${offerings.current.availablePackages.length}`
            );

            offerings.current.availablePackages.forEach((pkg, index) => {
              console.log(`🛒 Package ${index + 1}:`, {
                identifier: pkg.identifier,
                packageType: pkg.packageType,
                offeringIdentifier: pkg.offeringIdentifier,
                productIdentifier: pkg.product?.identifier,
                productTitle: pkg.product?.title,
                price: pkg.product?.price,
                priceString: pkg.product?.priceString,
              });
            });
          } else {
            console.warn('🛒 Current offering has no available packages');
          }
        } else {
          console.warn('🛒 No current offering available');
        }

        if (offerings.all) {
          console.log(
            '🛒 All offerings:',
            Object.keys(offerings.all).join(', ')
          );

          // Log all packages from all offerings for debugging
          Object.keys(offerings.all).forEach((offeringId) => {
            const offering = offerings.all[offeringId];
            console.log(
              `🛒 Offering ${offeringId} packages: ${
                offering.availablePackages?.length || 0
              }`
            );

            offering.availablePackages?.forEach((pkg, index) => {
              console.log(`🛒 ${offeringId} Package ${index + 1}:`, {
                identifier: pkg.identifier,
                packageType: pkg.packageType,
                offeringIdentifier: pkg.offeringIdentifier,
                productIdentifier: pkg.storeProduct?.identifier,
                productTitle: pkg.storeProduct?.title,
                price: pkg.storeProduct?.price,
                priceString: pkg.storeProduct?.priceString,
              });
            });
          });
        }
      } else {
        console.warn('🛒 RevenueCat returned no offerings');
      }

      if (!offerings) {
        console.warn('🛒 RevenueCat returned no offerings');
        return null;
      }

      // Just return what we get without any fallback logic for now
      console.log('🛒 Returning current offering as-is for debugging');
      return offerings.current || null;
    } catch (error) {
      console.error('❌ Failed to get subscription offerings:', error);
      return null;
    }
  };

/**
 * Purchase a subscription package
 */
export const purchaseSubscription = async (
  packageToPurchase: PurchasesPackage
): Promise<CustomerInfo | null> => {
  try {
    const { customerInfo } = await Purchases.purchasePackage(packageToPurchase);
    console.log('✅ Subscription purchased successfully');
    return customerInfo;
  } catch (error) {
    console.error('❌ Failed to purchase subscription:', error);
    throw error;
  }
};

/**
 * Restore previous purchases
 */
export const restorePurchases = async (): Promise<CustomerInfo | null> => {
  try {
    const customerInfo = await Purchases.restorePurchases();
    console.log('✅ Purchases restored successfully');
    return customerInfo;
  } catch (error) {
    console.error('❌ Failed to restore purchases:', error);
    throw error;
  }
};

/**
 * Get the most recent active subscription from customerInfo
 */
const getActiveSubscriptionDetails = (
  customerInfo: CustomerInfo
): ActiveSubscriptionDetails | null => {
  try {
    // Check active subscriptions first
    if (
      customerInfo.activeSubscriptions &&
      customerInfo.activeSubscriptions.length > 0
    ) {
      const activeProductId = customerInfo.activeSubscriptions[0];
      const subscription =
        customerInfo.subscriptionsByProductIdentifier?.[activeProductId];

      if (
        subscription &&
        subscription.isActive &&
        subscription.expiresDate &&
        subscription.purchaseDate
      ) {
        return {
          productIdentifier: subscription.productIdentifier,
          expirationDate: subscription.expiresDate,
          expirationDateMillis: new Date(subscription.expiresDate).getTime(),
          purchaseDate: subscription.purchaseDate,
          purchaseDateMillis: new Date(subscription.purchaseDate).getTime(),
          willRenew: subscription.willRenew,
          isInGracePeriod: subscription.gracePeriodExpiresDate !== null,
          isSandbox: subscription.isSandbox,
          periodType: subscription.periodType as 'NORMAL' | 'TRIAL' | 'INTRO',
          store: subscription.store as any,
          ownershipType: subscription.ownershipType as any,
          billingIssuesDetectedAt: subscription.billingIssuesDetectedAt,
          unsubscribeDetectedAt: subscription.unsubscribeDetectedAt,
          refundedAt: subscription.refundedAt,
        };
      }
    }

    // Fallback: check latest expiration date
    if (customerInfo.latestExpirationDate) {
      const latestExpirationMillis = new Date(
        customerInfo.latestExpirationDate
      ).getTime();
      const now = Date.now();

      // Check if latest expiration is still valid
      if (latestExpirationMillis > now) {
        // Find the subscription with the latest expiration date
        const subscriptions =
          customerInfo.subscriptionsByProductIdentifier || {};
        for (const [, subscription] of Object.entries(subscriptions)) {
          if (
            subscription.expiresDate === customerInfo.latestExpirationDate &&
            subscription.expiresDate &&
            subscription.purchaseDate
          ) {
            return {
              productIdentifier: subscription.productIdentifier,
              expirationDate: subscription.expiresDate,
              expirationDateMillis: new Date(
                subscription.expiresDate
              ).getTime(),
              purchaseDate: subscription.purchaseDate,
              purchaseDateMillis: new Date(subscription.purchaseDate).getTime(),
              willRenew: subscription.willRenew,
              isInGracePeriod: subscription.gracePeriodExpiresDate !== null,
              isSandbox: subscription.isSandbox,
              periodType: subscription.periodType as
                | 'NORMAL'
                | 'TRIAL'
                | 'INTRO',
              store: subscription.store as any,
              ownershipType: subscription.ownershipType as any,
              billingIssuesDetectedAt: subscription.billingIssuesDetectedAt,
              unsubscribeDetectedAt: subscription.unsubscribeDetectedAt,
              refundedAt: subscription.refundedAt,
            };
          }
        }
      }
    }

    return null;
  } catch (error) {
    console.error('❌ Error getting active subscription details:', error);
    return null;
  }
};

/**
 * Check if user has active premium subscription
 */
export const hasActivePremiumSubscription = async (): Promise<boolean> => {
  try {
    const customerInfo = await getRevenueCatCustomerInfo();
    if (!customerInfo) return false;

    // Check active subscriptions array
    if (
      customerInfo.activeSubscriptions &&
      customerInfo.activeSubscriptions.length > 0
    ) {
      return true;
    }

    // Fallback: check latest expiration date
    if (customerInfo.latestExpirationDate) {
      const now = Date.now();
      const latestExpirationMillis = new Date(
        customerInfo.latestExpirationDate
      ).getTime();
      return latestExpirationMillis > now;
    }

    return false;
  } catch (error) {
    console.error('❌ Failed to check premium subscription:', error);
    return false;
  }
};

/**
 * Get comprehensive subscription status details
 */
export const getSubscriptionStatus = async (): Promise<SubscriptionStatus> => {
  try {
    const customerInfo = await getRevenueCatCustomerInfo();
    console.log('customerInfo:', JSON.stringify(customerInfo, null, 2));

    if (!customerInfo) {
      return {
        isPremium: false,
        isActive: false,
        productIdentifier: null,
        expirationDate: null,
        expirationDateMillis: null,
        purchaseDate: null,
        purchaseDateMillis: null,
        willRenew: false,
        isInGracePeriod: false,
        isSandbox: false,
        periodType: null,
        store: null,
        ownershipType: null,
        billingIssuesDetectedAt: null,
        unsubscribeDetectedAt: null,
        refundedAt: null,
      };
    }

    const activeSubscription = getActiveSubscriptionDetails(customerInfo);
    const isPremium = await hasActivePremiumSubscription();

    if (activeSubscription && isPremium) {
      return {
        isPremium: true,
        isActive: true,
        productIdentifier: activeSubscription.productIdentifier,
        expirationDate: activeSubscription.expirationDate,
        expirationDateMillis: activeSubscription.expirationDateMillis,
        purchaseDate: activeSubscription.purchaseDate,
        purchaseDateMillis: activeSubscription.purchaseDateMillis,
        willRenew: activeSubscription.willRenew,
        isInGracePeriod: activeSubscription.isInGracePeriod,
        isSandbox: activeSubscription.isSandbox,
        periodType: activeSubscription.periodType,
        store: activeSubscription.store,
        ownershipType: activeSubscription.ownershipType,
        billingIssuesDetectedAt: activeSubscription.billingIssuesDetectedAt,
        unsubscribeDetectedAt: activeSubscription.unsubscribeDetectedAt,
        refundedAt: activeSubscription.refundedAt,
      };
    }

    return {
      isPremium: false,
      isActive: false,
      productIdentifier: null,
      expirationDate: null,
      expirationDateMillis: null,
      purchaseDate: null,
      purchaseDateMillis: null,
      willRenew: false,
      isInGracePeriod: false,
      isSandbox: false,
      periodType: null,
      store: null,
      ownershipType: null,
      billingIssuesDetectedAt: null,
      unsubscribeDetectedAt: null,
      refundedAt: null,
    };
  } catch (error) {
    console.error('❌ Failed to get subscription status:', error);
    return {
      isPremium: false,
      isActive: false,
      productIdentifier: null,
      expirationDate: null,
      expirationDateMillis: null,
      purchaseDate: null,
      purchaseDateMillis: null,
      willRenew: false,
      isInGracePeriod: false,
      isSandbox: false,
      periodType: null,
      store: null,
      ownershipType: null,
      billingIssuesDetectedAt: null,
      unsubscribeDetectedAt: null,
      refundedAt: null,
    };
  }
};

/**
 * Debug function to test RevenueCat integration
 */
export const debugRevenueCat = async (): Promise<void> => {
  console.log('🧪 Testing RevenueCat integration...');

  try {
    // Test 1: Check if RevenueCat is configured
    const customerInfo = await getRevenueCatCustomerInfo();
    console.log('📊 Customer Info:', {
      originalAppUserId: customerInfo?.originalAppUserId,
      firstSeen: customerInfo?.firstSeen,
      originalPurchaseDate: customerInfo?.originalPurchaseDate,
    });

    // Test 2: Get user ID
    const userId = await getRevenueCatUserId();
    console.log('👤 RevenueCat User ID:', userId);

    // Test 3: Get unified device ID
    const unifiedId = await getUnifiedDeviceId();
    console.log('🔗 Unified Device ID:', unifiedId);

    // Test 4: Check subscription status
    const subscriptionStatus = await getSubscriptionStatus();
    console.log('💰 Subscription Status:', subscriptionStatus);

    // Test 5: Get offerings
    const offerings = await getSubscriptionOfferings();
    console.log('🛒 Available Offerings:', offerings?.identifier);

    console.log('✅ RevenueCat debug completed');
  } catch (error) {
    console.error('❌ RevenueCat debug failed:', error);
  }
};
