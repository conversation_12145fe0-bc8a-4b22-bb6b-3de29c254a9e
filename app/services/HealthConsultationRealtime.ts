import { supabase } from '../lib/supabase';
import { HealthMessage, HealthConversation } from '../types/healthConsultation';
import {
  transformMessageFromDB,
  transformConversationFromDB,
  HealthMessageDB,
  HealthConversationDB,
} from './HealthConsultationSupabase';

export type RealtimeMessageCallback = (message: HealthMessage) => void;
export type RealtimeConversationCallback = (
  conversation: HealthConversation
) => void;

class HealthConsultationRealtimeService {
  private messageSubscriptions = new Map<string, any>();
  private conversationSubscription: any = null;

  /**
   * Subscribe to real-time message updates for a specific conversation
   */
  subscribeToMessages(
    conversationId: string,
    onMessage: RealtimeMessageCallback,
    onError?: (error: any) => void,
    onTypingChange?: (conversationId: string, isTyping: boolean) => void
  ) {
    // Setting up real-time subscription for conversation

    // Remove existing subscription for this conversation if any
    this.unsubscribeFromMessages(conversationId);

    const subscription = supabase
      .channel(`health_messages_${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'health_messages',
          filter: `conversation_id=eq.${conversationId}`,
        },
        (payload) => {
          try {
            const dbMessage = payload.new as HealthMessageDB;
            if (dbMessage && dbMessage.id && dbMessage.conversation_id) {
              const message = transformMessageFromDB(dbMessage);

              // 🎯 NEW FLOW: If this is an AI response, clear typing indicator
              if (message.role === 'assistant') {
                onTypingChange?.(conversationId, false);
              }

              onMessage(message);
            }
          } catch (error) {
            console.error('❌ Error processing real-time message:', error);
            onError?.(error);
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'health_messages',
          filter: `conversation_id=eq.${conversationId}`,
        },
        (payload) => {
          try {
            const dbMessage = payload.new as HealthMessageDB;
            if (dbMessage && dbMessage.id && dbMessage.conversation_id) {
              const message = transformMessageFromDB(dbMessage);
              onMessage(message);
            }
          } catch (error) {
            console.error(
              '❌ Error processing real-time message update:',
              error
            );
            onError?.(error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'CHANNEL_ERROR') {
          // Silently handle subscription errors - they don't affect functionality
          console.warn(
            '⚠️ Message subscription error for conversation:',
            conversationId
          );
        }
      });

    this.messageSubscriptions.set(conversationId, subscription);
    return subscription;
  }

  /**
   * Unsubscribe from message updates for a specific conversation
   */
  unsubscribeFromMessages(conversationId: string) {
    const subscription = this.messageSubscriptions.get(conversationId);
    if (subscription) {
      supabase.removeChannel(subscription);
      this.messageSubscriptions.delete(conversationId);
    }
  }

  /**
   * Subscribe to real-time conversation updates
   */
  subscribeToConversations(
    deviceId: string,
    onConversation: RealtimeConversationCallback,
    onError?: (error: any) => void
  ) {
    // Setting up real-time subscription for conversations

    // Remove existing subscription if any
    this.unsubscribeFromConversations();

    this.conversationSubscription = supabase
      .channel('health_conversations')
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to INSERT, UPDATE, DELETE
          schema: 'public',
          table: 'health_conversations',
          filter: `device_id=eq.${deviceId}`,
        },
        (payload) => {
          try {
            const dbConversation = (payload.new ||
              payload.old) as HealthConversationDB;
            if (
              dbConversation &&
              dbConversation.id &&
              dbConversation.device_id
            ) {
              const conversation = transformConversationFromDB(dbConversation);
              onConversation(conversation);
            }
          } catch (error) {
            console.error('❌ Error processing real-time conversation:', error);
            onError?.(error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'CHANNEL_ERROR') {
          // Silently handle subscription errors - they don't affect functionality
          console.warn('⚠️ Conversation subscription error');
        }
      });

    return this.conversationSubscription;
  }

  /**
   * Unsubscribe from conversation updates
   */
  unsubscribeFromConversations() {
    if (this.conversationSubscription) {
      supabase.removeChannel(this.conversationSubscription);
      this.conversationSubscription = null;
    }
  }

  /**
   * Clean up all subscriptions
   */
  cleanup() {
    // Unsubscribe from all message subscriptions
    for (const [conversationId] of this.messageSubscriptions) {
      this.unsubscribeFromMessages(conversationId);
    }

    // Unsubscribe from conversation subscription
    this.unsubscribeFromConversations();
  }

  /**
   * Get active subscription count for debugging
   */
  getActiveSubscriptionCount(): number {
    let count = this.messageSubscriptions.size;
    if (this.conversationSubscription) count += 1;
    return count;
  }
}

// Export singleton instance
export const healthConsultationRealtime =
  new HealthConsultationRealtimeService();
