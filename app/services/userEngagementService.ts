import AsyncStorage from '@react-native-async-storage/async-storage';
import * as StoreReview from 'expo-store-review';
import { getUnifiedDeviceId } from './revenueCatService';
import { getHealthConversations } from './HealthConsultationSupabase';

const USER_ENGAGEMENT_KEY = '@user_engagement';
const SCAN_HISTORY_KEY = '@scanHistory'; // Match the key used in useScanLogic.ts

export interface UserEngagementData {
  appLaunches: number;
  lastLaunchDate: string;
  firstLaunchDate: string;
  hasSeenAppStoreReview: boolean;
  ratingGiven?: number;
  ratingDate?: string;
  lastRatingModalShown?: string; // Date when rating modal was last shown
  ratingModalShowCount: number; // How many times rating modal was shown
}

const defaultEngagementData: UserEngagementData = {
  appLaunches: 0,
  lastLaunchDate: new Date().toISOString(),
  firstLaunchDate: new Date().toISOString(),
  hasSeenAppStoreReview: false,
  ratingModalShowCount: 0,
};

/**
 * Get current user engagement data
 */
export const getUserEngagementData = async (): Promise<UserEngagementData> => {
  try {
    const data = await AsyncStorage.getItem(USER_ENGAGEMENT_KEY);
    if (data) {
      return { ...defaultEngagementData, ...JSON.parse(data) };
    }
    return defaultEngagementData;
  } catch (error) {
    console.error('Error getting user engagement data:', error);
    return defaultEngagementData;
  }
};

/**
 * Update user engagement data
 */
export const updateUserEngagementData = async (
  updates: Partial<UserEngagementData>
): Promise<void> => {
  try {
    const currentData = await getUserEngagementData();
    const updatedData = { ...currentData, ...updates };
    await AsyncStorage.setItem(
      USER_ENGAGEMENT_KEY,
      JSON.stringify(updatedData)
    );
  } catch (error) {
    console.error('Error updating user engagement data:', error);
  }
};

/**
 * Track app launch
 */
export const trackAppLaunch = async (): Promise<void> => {
  try {
    const data = await getUserEngagementData();
    await updateUserEngagementData({
      appLaunches: data.appLaunches + 1,
      lastLaunchDate: new Date().toISOString(),
    });

    console.log('📱 App launch tracked:', data.appLaunches + 1);
  } catch (error) {
    console.error('Error tracking app launch:', error);
  }
};

/**
 * Track conversation creation
 */
/**
 * Get actual conversation count from Supabase
 */
const getConversationCount = async (): Promise<number> => {
  try {
    const deviceId = await getUnifiedDeviceId();
    const conversations = await getHealthConversations(deviceId);
    return conversations.length;
  } catch (error) {
    console.error('Error getting conversation count:', error);
    return 0;
  }
};

/**
 * Get actual scan count from AsyncStorage
 */
const getScanCount = async (): Promise<number> => {
  try {
    const scanHistory = await AsyncStorage.getItem(SCAN_HISTORY_KEY);
    if (scanHistory) {
      const parsed = JSON.parse(scanHistory);
      return Array.isArray(parsed) ? parsed.length : 0;
    }
    return 0;
  } catch (error) {
    console.error('Error getting scan count:', error);
    return 0;
  }
};

export const trackConversationCreation = async (): Promise<void> => {
  // No need to track, we get actual count from Supabase
  console.log('💬 Conversation created (count will be fetched from Supabase)');
};

/**
 * Track scan completion
 */
export const trackScanCompletion = async (): Promise<void> => {
  // No need to track, we get actual count from AsyncStorage
  console.log('📸 Scan completed (count will be fetched from AsyncStorage)');
};

/**
 * Check if user should see rating modal
 * Conditions:
 * - App launched at least 3 times
 * - Has created at least 1 conversation
 * - Has completed at least 1 scan
 * - App was terminated and relaunched (different session)
 * - First time: Haven't seen rating modal before
 * - Subsequent times: Every 5 app launches (if App Store review available)
 */
export const shouldShowRatingModal = async (): Promise<boolean> => {
  try {
    const data = await getUserEngagementData();

    // Get actual counts from data sources
    const conversationCount = await getConversationCount();
    const scanCount = await getScanCount();

    const meetsEngagementCriteria =
      data.appLaunches >= 3 && conversationCount >= 1 && scanCount >= 1;

    if (!meetsEngagementCriteria) {
      console.log('❌ Engagement criteria not met:', {
        appLaunches: data.appLaunches,
        conversationCount,
        scanCount,
        meetsEngagementCriteria,
      });
      return false;
    }

    // Show every 5th launch (5, 10, 15, 20, etc.) if App Store review is available
    const isAppStoreReviewAvailable = await StoreReview.hasAction();
    const isEvery5thLaunch = data.appLaunches % 5 === 0 && data.appLaunches > 0;
    const shouldShow = isEvery5thLaunch && isAppStoreReviewAvailable;

    console.log('🤔 Rating modal check:', {
      appLaunches: data.appLaunches,
      conversationCount,
      scanCount,
      isAppStoreReviewAvailable,
      isEvery5thLaunch,
      shouldShow,
    });

    return shouldShow;
  } catch (error) {
    console.error('Error checking rating modal criteria:', error);
    return false;
  }
};

/**
 * Mark rating modal as shown
 */
export const markRatingModalShown = async (rating?: number): Promise<void> => {
  try {
    const data = await getUserEngagementData();
    const updates: Partial<UserEngagementData> = {
      lastRatingModalShown: new Date().toISOString(),
      ratingModalShowCount: data.ratingModalShowCount + 1,
    };

    if (rating !== undefined) {
      updates.ratingGiven = rating;
      updates.ratingDate = new Date().toISOString();
    }

    await updateUserEngagementData(updates);
    console.log(
      '⭐ Rating modal marked as shown, rating:',
      rating,
      'show count:',
      data.ratingModalShowCount + 1
    );
  } catch (error) {
    console.error('Error marking rating modal as shown:', error);
  }
};

/**
 * Show App Store review if rating is 4 or higher
 */
export const showAppStoreReviewIfEligible = async (
  rating: number
): Promise<void> => {
  try {
    if (rating >= 4) {
      const data = await getUserEngagementData();

      if (!data.hasSeenAppStoreReview) {
        // Check if store review is available
        const isAvailable = await StoreReview.hasAction();

        if (isAvailable) {
          console.log('🌟 Showing App Store review for rating:', rating);
          await StoreReview.requestReview();

          await updateUserEngagementData({
            hasSeenAppStoreReview: true,
          });
        } else {
          console.log('📱 App Store review not available on this device');
        }
      } else {
        console.log('📱 App Store review already shown before');
      }
    } else {
      console.log('⭐ Rating too low for App Store review:', rating);
    }
  } catch (error) {
    console.error('Error showing App Store review:', error);
  }
};

/**
 * Reset engagement data (for testing)
 */
export const resetEngagementData = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(USER_ENGAGEMENT_KEY);
    console.log('🔄 User engagement data reset');
  } catch (error) {
    console.error('Error resetting engagement data:', error);
  }
};
