import axios from 'axios';
import { getAPIConfiguration } from './SupabaseAPIConfig';
import { getModelForTask, getModelConfig } from './AIModelConfig';

// Helper function for API calls with retry logic
const makeAPICallWithRetry = async (
  apiConfig: any,
  requestData: any,
  maxRetries: number = 30,
  retryDelay: number = 3000
): Promise<any> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Food analysis API call attempt ${attempt}/${maxRetries}`);

      const response = await axios.post(apiConfig.api_url, requestData, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiConfig.api_key}`,
          'HTTP-Referer': 'https://additives.app',
        },
      });

      return response;
    } catch (error: any) {
      lastError = error;

      if (error.response?.status === 429 || attempt < maxRetries) {
        console.log(
          `Rate limit hit, retrying in ${retryDelay}ms... (attempt ${attempt}/${maxRetries})`
        );
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
        continue;
      }

      // If it's not a rate limit error or we've exhausted retries, throw
      throw error;
    }
  }

  throw lastError;
};

export interface AIFoodAnalysis {
  nutritionScore: number; // 0-100
  nutritionGrade: 'A' | 'B' | 'C' | 'D' | 'E' | 'F';
  nutritionSummary: string;
  healthRecommendation: string;
  keyNutritionPoints: string[];
  overallAssessment: string;
}

// Function to get system prompt for food analysis
const getFoodAnalysisSystemPrompt = (language: 'en' | 'tr') => {
  if (language === 'tr') {
    return `Sen bir beslenme uzmanısın. Sana verilen gıda ürününün besin değerleri ve katkı maddeleri bilgilerini analiz edip kapsamlı bir değerlendirme yapman gerekiyor.

ÖNEMLI KURALLAR:
1. Besin değerlerini analiz et (enerji, yağ, şeker, tuz, protein vb.)
2. Katkı maddelerinin sağlık etkilerini değerlendir
3. Genel sağlık açısından bir skor ver (0-100 arası)
4. Sağlık notu ver (A=Mükemmel, B=İyi, C=Orta, D=Kötü, E=Çok Kötü, F=Zararlı)
5. Cevabını JSON formatında ver:
{
  "nutritionScore": sayı_0_100_arası,
  "nutritionGrade": "A_B_C_D_E_F_harflerinden_biri",
  "nutritionSummary": "kısa_besin_değerleri_özeti",
  "healthRecommendation": "sağlık_önerisi",
  "keyNutritionPoints": ["önemli_nokta_1", "önemli_nokta_2", "önemli_nokta_3"],
  "overallAssessment": "genel_değerlendirme"
}

DEĞERLENDIRME KRİTERLERİ:
- Yüksek şeker, tuz, doymuş yağ = düşük skor
- Yüksek protein, lif = yüksek skor
- Zararlı katkı maddeleri = düşük skor
- Doğal içerikler = yüksek skor

Sadece JSON cevabı ver, başka açıklama yapma.`;
  } else {
    return `You are a nutrition expert. You need to analyze the nutrition values and food additives of the given food product and provide a comprehensive assessment.

IMPORTANT RULES:
1. Analyze nutrition values (energy, fat, sugar, salt, protein, etc.)
2. Evaluate health effects of food additives
3. Give an overall health score (0-100 scale)
4. Give a health grade (A=Excellent, B=Good, C=Average, D=Poor, E=Very Poor, F=Harmful)
5. Give your answer in JSON format:
{
  "nutritionScore": number_0_100_range,
  "nutritionGrade": "one_of_A_B_C_D_E_F",
  "nutritionSummary": "brief_nutrition_summary",
  "healthRecommendation": "health_recommendation",
  "keyNutritionPoints": ["key_point_1", "key_point_2", "key_point_3"],
  "overallAssessment": "overall_assessment"
}

EVALUATION CRITERIA:
- High sugar, salt, saturated fat = lower score
- High protein, fiber = higher score
- Harmful additives = lower score
- Natural ingredients = higher score

Give only the JSON response, no other explanations.`;
  }
};

// Function to get user prompt for food analysis
const getFoodAnalysisUserPrompt = (
  nutritionData: any,
  eCodes: string[],
  ingredientText: string,
  language: 'en' | 'tr'
) => {
  const nutritionText = nutritionData
    ? Object.entries(nutritionData)
        .filter(([key, value]) => value !== null && value !== undefined)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ')
    : language === 'tr'
    ? 'Besin değerleri mevcut değil'
    : 'Nutrition data not available';

  const eCodesText =
    eCodes.length > 0
      ? eCodes.join(', ')
      : language === 'tr'
      ? 'Katkı maddesi bulunamadı'
      : 'No additives found';

  if (language === 'tr') {
    return `Bu gıda ürününü analiz et:

BESIN DEĞERLERİ (100g başına):
${nutritionText}

KATKI MADDELERİ:
${eCodesText}

İÇERİK METNİ:
${ingredientText || 'Mevcut değil'}

Bu bilgilere dayanarak ürünün sağlık açısından kapsamlı analizini yap ve JSON formatında ver.`;
  } else {
    return `Analyze this food product:

NUTRITION VALUES (per 100g):
${nutritionText}

FOOD ADDITIVES:
${eCodesText}

INGREDIENT TEXT:
${ingredientText || 'Not available'}

Based on this information, provide a comprehensive health analysis of the product in JSON format.`;
  }
};

// Function to analyze food with AI
export const analyzeFood = async (
  nutritionData: any,
  eCodes: string[],
  ingredientText: string,
  language: 'en' | 'tr' = 'en'
): Promise<AIFoodAnalysis | null> => {
  try {
    // Get API configuration from Supabase
    const apiConfig = await getAPIConfiguration('openrouter');
    if (!apiConfig) {
      throw new Error('OpenRouter API configuration not found');
    }

    // Set up the prompt for food analysis
    const messages = [
      {
        role: 'system',
        content: getFoodAnalysisSystemPrompt(language),
      },
      {
        role: 'user',
        content: getFoodAnalysisUserPrompt(
          nutritionData,
          eCodes,
          ingredientText,
          language
        ),
      },
    ];

    // Get model configuration for food analysis
    const modelConfig = getModelConfig('food-analysis');
    const model = getModelForTask('food-analysis');

    // Make API call to AI service with retry logic
    const response = await makeAPICallWithRetry(apiConfig, {
      model: model,
      messages: messages,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
    });

    // Extract response text
    const responseText = response.data.choices[0].message.content.trim();

    console.log('AI food analysis response:', responseText);

    // Try to parse JSON response
    try {
      // Clean the response text - remove any markdown formatting or extra text
      let cleanedResponse = responseText.trim();

      // Remove markdown code blocks if present
      cleanedResponse = cleanedResponse.replace(/```json\s*/gi, '');
      cleanedResponse = cleanedResponse.replace(/```\s*/g, '');

      // Find JSON object in the response
      const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanedResponse = jsonMatch[0];
      }

      console.log(
        'Cleaned food analysis response for parsing:',
        cleanedResponse
      );

      const analysisData = JSON.parse(cleanedResponse);

      // Validate that it's a proper analysis object
      if (
        typeof analysisData === 'object' &&
        analysisData !== null &&
        typeof analysisData.nutritionScore === 'number' &&
        typeof analysisData.nutritionGrade === 'string'
      ) {
        return analysisData as AIFoodAnalysis;
      } else {
        console.error('Invalid food analysis data structure:', analysisData);
        return null;
      }
    } catch (parseError) {
      console.error('Error parsing food analysis JSON:', parseError);
      console.error('Raw response was:', responseText);
      return null;
    }
  } catch (error) {
    console.error('Error analyzing food with AI:', error);
    return null;
  }
};

// Function to check if AI food analysis is available
export const isAIFoodAnalysisAvailable = async (): Promise<boolean> => {
  try {
    const apiConfig = await getAPIConfiguration('openrouter');
    return !!apiConfig && !!apiConfig.api_key && apiConfig.is_active;
  } catch (error) {
    console.error('Error checking AI food analysis availability:', error);
    return false;
  }
};
