import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://qfyfarymbxpwztclgufm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFmeWZhcnltYnhwd3p0Y2xndWZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3MTQ3MzgsImV4cCI6MjA2MDI5MDczOH0.3Hu_i4h8qCrAKZBlQ5pWTKcGBTsQJ2ji2NTK764OWww';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Interface for API configuration
export interface APIConfiguration {
  id: number;
  service_name: string;
  api_key: string;
  api_url: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Cache for API configurations
const configCache = new Map<string, { config: APIConfiguration; timestamp: number }>();
const CACHE_EXPIRY = 1000 * 60 * 10; // 10 minutes

/**
 * Get API configuration for a specific service from Supabase
 * @param serviceName Name of the service (e.g., 'openrouter')
 * @returns API configuration or null if not found
 */
export const getAPIConfiguration = async (
  serviceName: string
): Promise<APIConfiguration | null> => {
  try {
    // Check cache first
    const cachedResult = configCache.get(serviceName);
    if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_EXPIRY) {
      return cachedResult.config;
    }

    // Fetch from Supabase
    const { data, error } = await supabase
      .from('api_configurations')
      .select('*')
      .eq('service_name', serviceName)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching API configuration:', error);
      return null;
    }

    if (!data) {
      console.warn(`No active API configuration found for service: ${serviceName}`);
      return null;
    }

    // Cache the result
    configCache.set(serviceName, {
      config: data,
      timestamp: Date.now(),
    });

    return data;
  } catch (error) {
    console.error('Error in getAPIConfiguration:', error);
    return null;
  }
};

/**
 * Get all active API configurations from Supabase
 * @returns Array of API configurations
 */
export const getAllAPIConfigurations = async (): Promise<APIConfiguration[]> => {
  try {
    const { data, error } = await supabase
      .from('api_configurations')
      .select('*')
      .eq('is_active', true)
      .order('service_name');

    if (error) {
      console.error('Error fetching all API configurations:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getAllAPIConfigurations:', error);
    return [];
  }
};

/**
 * Update API configuration in Supabase
 * @param serviceName Name of the service
 * @param updates Partial configuration updates
 * @returns Updated configuration or null if failed
 */
export const updateAPIConfiguration = async (
  serviceName: string,
  updates: Partial<Omit<APIConfiguration, 'id' | 'created_at' | 'updated_at'>>
): Promise<APIConfiguration | null> => {
  try {
    const { data, error } = await supabase
      .from('api_configurations')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('service_name', serviceName)
      .select()
      .single();

    if (error) {
      console.error('Error updating API configuration:', error);
      return null;
    }

    // Clear cache for this service
    configCache.delete(serviceName);

    return data;
  } catch (error) {
    console.error('Error in updateAPIConfiguration:', error);
    return null;
  }
};

/**
 * Create new API configuration in Supabase
 * @param config New API configuration
 * @returns Created configuration or null if failed
 */
export const createAPIConfiguration = async (
  config: Omit<APIConfiguration, 'id' | 'created_at' | 'updated_at'>
): Promise<APIConfiguration | null> => {
  try {
    const { data, error } = await supabase
      .from('api_configurations')
      .insert([config])
      .select()
      .single();

    if (error) {
      console.error('Error creating API configuration:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in createAPIConfiguration:', error);
    return null;
  }
};

/**
 * Clear the configuration cache
 */
export const clearConfigCache = (): void => {
  configCache.clear();
};
