import axios from 'axios';
import { getAPIConfiguration } from './SupabaseAPIConfig';
import { getModelForTask, getModelConfig } from './AIModelConfig';
import {
  HealthMessage,
  HealthConversation,
  QuickTopic,
  SendMessageRequest,
  SendMessageResponse,
  HealthConsultationError,
  HEALTH_CONSULTATION_ERROR_CODES,
} from '../types/healthConsultation';
// UUID generation is now handled by Supabase automatically
import {
  saveUserMessageWithPendingResponse,
  saveAIResponseMessage,
  markAIResponseFailed,
  markAIResponseCompleted,
  updateConversationStats,
} from './HealthConsultationSupabase';

// Helper function for API calls with retry logic
const makeAPICallWithRetry = async (
  apiConfig: any,
  requestData: any,
  maxRetries: number = 20,
  retryDelay: number = 5000
): Promise<any> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(
        `Health consultation API call attempt ${attempt}/${maxRetries}`
      );

      const response = await axios.post(apiConfig.api_url, requestData, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiConfig.api_key}`,
          'HTTP-Referer': 'https://additives.app',
          'X-Title': 'Additives App - Health Consultation',
        },
        timeout: 30000, // 30 second timeout
      });

      return response;
    } catch (error: any) {
      lastError = error;

      // Log the error for debugging
      console.log(`Health consultation API error (attempt ${attempt}):`, {
        status: error.response?.status,
        message: error.message,
        data: error.response?.data,
      });

      // Check if we should retry
      const shouldRetry =
        error.response?.status === 429 || // Rate limit
        error.response?.status >= 500 || // Server errors
        error.code === 'ECONNABORTED' || // Timeout
        error.code === 'ENOTFOUND' || // Network error
        attempt < maxRetries;

      if (shouldRetry && attempt < maxRetries) {
        const delay =
          error.response?.status === 429 ? retryDelay * 2 : retryDelay;
        console.log(
          `Retrying in ${delay}ms... (attempt ${attempt}/${maxRetries})`
        );
        await new Promise((resolve) => setTimeout(resolve, delay));
        continue;
      }

      // If it's not a retryable error or we've exhausted retries, throw
      throw error;
    }
  }

  throw lastError;
};

// Generate system prompt for health consultation
const getHealthConsultationSystemPrompt = (
  language: 'en' | 'tr',
  topic?: QuickTopic
): string => {
  const basePrompt =
    language === 'tr'
      ? `Sen deneyimli bir sağlık danışmanısın. Kullanıcılara genel sağlık konularında yardımcı oluyorsun.

ÖNEMLI KURALLAR:
- Sadece genel sağlık bilgisi ver, tıbbi teşhis yapma
- Acil durumlar için doktora başvurmasını söyle
- Kişiselleştirilmiş tıbbi tavsiye verme
- Belirsiz durumlarda sağlık profesyoneline yönlendir
- Türkçe yanıt ver
- Samimi ve anlayışlı ol
- Kısa ve net cevaplar ver (maksimum 200 kelime)

Yanıtlarını şu yapıda ver:
1. Ana bilgi/tavsiye
2. Gerekirse ek öneriler
3. Ne zaman doktora başvurulacağı`
      : `You are an experienced health consultant. You help users with general health topics.

IMPORTANT RULES:
- Only provide general health information, do not diagnose
- Advise seeing a doctor for urgent situations
- Do not give personalized medical advice
- Refer to healthcare professionals when uncertain
- Respond in English
- Be friendly and understanding
- Keep responses concise (maximum 200 words)

Structure your responses:
1. Main information/advice
2. Additional suggestions if needed
3. When to see a doctor`;

  // Add topic-specific context if provided
  if (topic) {
    const topicContext =
      language === 'tr'
        ? `\n\nBu konuşma "${topic.id}" konusu hakkında başlatıldı. Bu konuya odaklan ama diğer sağlık sorularını da yanıtla.`
        : `\n\nThis conversation was started about "${topic.id}" topic. Focus on this topic but also answer other health questions.`;

    return basePrompt + topicContext;
  }

  return basePrompt;
};

// Generate user message with context
const formatUserMessage = (
  content: string,
  conversationHistory?: HealthMessage[]
): string => {
  // For now, just return the content
  // In the future, we could add conversation context here
  return content;
};

// Parse AI response and extract structured data if needed
const parseAIResponse = (
  responseText: string,
  language: 'en' | 'tr'
): {
  content: string;
  recommendations?: string[];
  urgencyLevel?: 'low' | 'medium' | 'high';
} => {
  // For now, return the response as-is
  // In the future, we could parse structured responses
  return {
    content: responseText.trim(),
  };
};

// Main function to send a message and get AI response
export const sendHealthConsultationMessage = async (
  request: SendMessageRequest
): Promise<SendMessageResponse> => {
  const startTime = Date.now();

  try {
    // Get API configuration from Supabase
    const apiConfig = await getAPIConfiguration('openrouter');
    if (!apiConfig) {
      throw new Error('OpenRouter API configuration not found');
    }

    // Get model configuration for health consultation
    const modelConfig = getModelConfig('health-consultation');
    const model = getModelForTask('health-consultation');

    // Build messages array
    const messages = [
      {
        role: 'system',
        content: getHealthConsultationSystemPrompt(request.language, undefined), // TODO: Pass topic if available
      },
      {
        role: 'user',
        content: formatUserMessage(request.content),
      },
    ];

    // Make API call with retry logic
    const response = await makeAPICallWithRetry(apiConfig, {
      model: model,
      messages: messages,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
    });

    // Extract and parse response
    const responseText = response.data.choices[0].message.content.trim();
    const parsedResponse = parseAIResponse(responseText, request.language);

    // 🎯 NEW FLOW: Save user message first with pending AI response
    console.log('🔄 Step 1: Saving user message with pending AI response...');

    const userMessageData: Omit<HealthMessage, 'id'> = {
      conversationId: request.conversationId,
      role: 'user',
      content: request.content,
      messageType: request.messageType,
      timestamp: Date.now(),
      aiResponseStatus: 'pending', // 🎯 Set pending status for UI
      metadata: {
        language: request.language,
      },
    };

    let savedUserMessage: HealthMessage;
    try {
      savedUserMessage = await saveUserMessageWithPendingResponse(
        userMessageData
      );
      console.log('✅ Step 1 Complete: User message saved with pending status');
    } catch (userSaveError) {
      console.error(
        '❌ Step 1 Failed: Could not save user message:',
        userSaveError
      );
      throw new Error('Failed to save user message to database');
    }

    // 🎯 Step 2: AI response already received above, calculate processing time
    console.log('✅ Step 2 Complete: AI response already received');
    const processingTime = Date.now() - startTime;

    try {
      // 🎯 Step 3: Save AI response message
      console.log('🔄 Step 3: Saving AI response message...');

      const aiMessageData: Omit<HealthMessage, 'id'> = {
        conversationId: request.conversationId,
        role: 'assistant',
        content: parsedResponse.content,
        messageType: 'text',
        timestamp: Date.now(),
        metadata: {
          language: request.language,
          processingTime,
          modelUsed: model,
          recommendationData: parsedResponse.recommendations
            ? {
                category: 'general',
                priority: parsedResponse.urgencyLevel || 'low',
                actionItems: parsedResponse.recommendations,
              }
            : undefined,
        },
      };

      const savedAIMessage = await saveAIResponseMessage(
        aiMessageData,
        savedUserMessage.id
      );
      console.log(
        '✅ Step 3 Complete: AI response saved and linked to user message'
      );

      // 🎯 Step 4: Update conversation stats with real data
      console.log('🔄 Step 4: Updating conversation stats...');
      await updateConversationStats(
        request.conversationId,
        parsedResponse.content
      );
      console.log('✅ Step 4 Complete: Conversation stats updated');

      // 🎯 Step 5: Update user message status to completed
      console.log('🔄 Step 5: Updating user message status to completed...');
      await markAIResponseCompleted(savedUserMessage.id);
      console.log(
        '✅ Step 5 Complete: User message status updated to completed'
      );

      // 🎯 SUCCESS: Return saved messages
      return {
        message: savedUserMessage, // User message with real Supabase ID
        aiResponse: savedAIMessage, // AI message with real Supabase ID
        conversationUpdated: null, // Not needed in database-first approach
        savedToDatabase: true,
      };
    } catch (aiError) {
      // 🎯 Step 2/3 Failed: Mark user message as failed
      console.error('❌ AI processing failed:', aiError);

      const errorMessage =
        aiError instanceof Error ? aiError.message : 'Unknown AI error';
      await markAIResponseFailed(savedUserMessage.id, errorMessage);

      console.log('✅ User message marked as failed for retry capability');
      throw aiError;
    }
  } catch (error: any) {
    const processingTime = Date.now() - startTime;

    // Create error object
    const healthError: HealthConsultationError = {
      code: getErrorCode(error),
      message: getErrorMessage(error),
      details: {
        originalError: error.message,
        status: error.response?.status,
        processingTime,
      },
      timestamp: Date.now(),
    };

    console.error('Health consultation AI error:', healthError);
    throw healthError;
  }
};

// Helper function to determine error code
const getErrorCode = (error: any): string => {
  if (error.response?.status === 429) {
    return HEALTH_CONSULTATION_ERROR_CODES.RATE_LIMIT_EXCEEDED;
  }

  if (error.response?.status >= 500) {
    return HEALTH_CONSULTATION_ERROR_CODES.AI_SERVICE_UNAVAILABLE;
  }

  if (error.code === 'ECONNABORTED' || error.code === 'ENOTFOUND') {
    return HEALTH_CONSULTATION_ERROR_CODES.NETWORK_ERROR;
  }

  if (error.response?.status >= 400) {
    return HEALTH_CONSULTATION_ERROR_CODES.API_ERROR;
  }

  return HEALTH_CONSULTATION_ERROR_CODES.MESSAGE_SEND_FAILED;
};

// Helper function to get user-friendly error message
const getErrorMessage = (error: any): string => {
  const code = getErrorCode(error);

  switch (code) {
    case HEALTH_CONSULTATION_ERROR_CODES.RATE_LIMIT_EXCEEDED:
      return 'Too many requests. Please wait a moment and try again.';
    case HEALTH_CONSULTATION_ERROR_CODES.AI_SERVICE_UNAVAILABLE:
      return 'AI service is temporarily unavailable. Please try again later.';
    case HEALTH_CONSULTATION_ERROR_CODES.NETWORK_ERROR:
      return 'Network connection error. Please check your internet connection.';
    case HEALTH_CONSULTATION_ERROR_CODES.API_ERROR:
      return 'API error occurred. Please try again.';
    default:
      return 'Failed to send message. Please try again.';
  }
};

// Function to generate conversation title based on first message
export const generateConversationTitle = async (
  firstMessage: string,
  language: 'en' | 'tr'
): Promise<string> => {
  try {
    const apiConfig = await getAPIConfiguration('openrouter');
    if (!apiConfig) {
      return language === 'tr' ? 'Sağlık Danışmanlığı' : 'Health Consultation';
    }

    const model = getModelForTask('health-consultation');
    const prompt =
      language === 'tr'
        ? `Bu sağlık danışmanlığı mesajı için kısa bir başlık oluştur (maksimum 5 kelime): "${firstMessage}"`
        : `Create a short title for this health consultation message (maximum 5 words): "${firstMessage}"`;

    const response = await makeAPICallWithRetry(
      apiConfig,
      {
        model: model,
        messages: [
          {
            role: 'system',
            content:
              language === 'tr'
                ? 'Sen kısa ve öz başlıklar oluşturan bir asistansın. Sadece başlığı döndür, başka bir şey yazma.'
                : 'You are an assistant that creates short and concise titles. Return only the title, nothing else.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 20,
        temperature: 0.3,
      },
      3,
      2000
    ); // Fewer retries for title generation

    const title = response.data.choices[0].message.content.trim();
    return (
      title ||
      (language === 'tr' ? 'Sağlık Danışmanlığı' : 'Health Consultation')
    );
  } catch (error) {
    console.error('Error generating conversation title:', error);
    return language === 'tr' ? 'Sağlık Danışmanlığı' : 'Health Consultation';
  }
};
