// Example usage of the centralized AI Model Configuration
import { 
  getModelForTask, 
  getModelConfig, 
  updatePrimaryModel, 
  updateFallbackModel,
  getModelSummary,
  updateModelConfigs,
  getAllTasks,
  resetToDefaults
} from './AIModelConfig';

// Example 1: Get model for a specific task
export const exampleGetModel = () => {
  // Get the primary model for E-code detection
  const eCodeModel = getModelForTask('e-code-detection');
  console.log('E-code detection model:', eCodeModel);
  
  // Get the fallback model for E-code detection
  const eCodeFallbackModel = getModelForTask('e-code-detection', true);
  console.log('E-code detection fallback model:', eCodeFallbackModel);
};

// Example 2: Get complete configuration for a task
export const exampleGetConfig = () => {
  const nutritionConfig = getModelConfig('nutrition-extraction');
  console.log('Nutrition extraction config:', nutritionConfig);
  // Output: { primary: 'google/gemini-2.0-flash-exp:free', fallback: 'google/gemini-2.0-flash-001', maxTokens: 300, temperature: 0.1 }
};

// Example 3: Update models for specific tasks
export const exampleUpdateModels = () => {
  // Update primary model for food analysis
  updatePrimaryModel('food-analysis', 'anthropic/claude-3.5-sonnet');
  
  // Update fallback model for E-code detection
  updateFallbackModel('e-code-detection', 'google/gemini-pro');
  
  console.log('Models updated successfully');
};

// Example 4: Get summary of all configurations
export const exampleGetSummary = () => {
  const summary = getModelSummary();
  console.log('All model configurations:', summary);
  /*
  Output:
  {
    'e-code-detection': {
      primary: 'google/gemini-2.0-flash-exp:free',
      fallback: 'google/gemma-3-27b-it:free',
      maxTokens: 200,
      temperature: 0.1
    },
    'nutrition-extraction': {
      primary: 'google/gemini-2.0-flash-exp:free',
      fallback: 'google/gemini-2.0-flash-001',
      maxTokens: 300,
      temperature: 0.1
    },
    'food-analysis': {
      primary: 'google/gemini-2.0-flash-exp:free',
      fallback: 'google/gemini-2.0-flash-001',
      maxTokens: 500,
      temperature: 0.3
    }
  }
  */
};

// Example 5: Update multiple configurations at once
export const exampleBulkUpdate = () => {
  updateModelConfigs({
    'e-code-detection': {
      primary: 'google/gemini-pro',
      maxTokens: 250
    },
    'nutrition-extraction': {
      temperature: 0.05
    }
  });
  
  console.log('Bulk update completed');
};

// Example 6: Get all available tasks
export const exampleGetTasks = () => {
  const tasks = getAllTasks();
  console.log('Available AI tasks:', tasks);
  // Output: ['e-code-detection', 'nutrition-extraction', 'food-analysis']
};

// Example 7: Reset to default configurations
export const exampleReset = () => {
  resetToDefaults();
  console.log('All configurations reset to defaults');
};

// Example 8: Using in an AI service function
export const exampleAIServiceUsage = async (imageUri: string) => {
  try {
    // Get model configuration for E-code detection
    const modelConfig = getModelConfig('e-code-detection');
    const model = getModelForTask('e-code-detection');
    
    // Use the configuration in your API call
    const requestData = {
      model: model,
      messages: [
        { role: 'system', content: 'You are an expert...' },
        { role: 'user', content: 'Analyze this image...' }
      ],
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
    };
    
    console.log('Making API call with:', requestData);
    // Make your actual API call here...
    
  } catch (error) {
    console.error('Error in AI service:', error);
  }
};

// Example 9: Switching to fallback model on error
export const exampleFallbackUsage = async () => {
  try {
    // Try with primary model first
    const primaryModel = getModelForTask('food-analysis');
    console.log('Trying with primary model:', primaryModel);
    
    // If primary fails, use fallback
    // ... API call fails ...
    
  } catch (error) {
    console.log('Primary model failed, trying fallback...');
    
    const fallbackModel = getModelForTask('food-analysis', true);
    const config = getModelConfig('food-analysis');
    
    console.log('Using fallback model:', fallbackModel);
    // Make API call with fallback model...
  }
};

// Example 10: Dynamic model selection based on conditions
export const exampleDynamicSelection = (useHighAccuracy: boolean) => {
  if (useHighAccuracy) {
    // Use a more powerful model for high accuracy
    updatePrimaryModel('e-code-detection', 'anthropic/claude-3.5-sonnet');
    updateModelConfigs({
      'e-code-detection': {
        maxTokens: 400,
        temperature: 0.05
      }
    });
  } else {
    // Use faster, cheaper model for quick analysis
    updatePrimaryModel('e-code-detection', 'google/gemini-2.0-flash-exp:free');
    updateModelConfigs({
      'e-code-detection': {
        maxTokens: 200,
        temperature: 0.1
      }
    });
  }
  
  console.log('Model configuration updated based on accuracy requirements');
};
