import { supabase } from '../lib/supabase';
import { HealthConversation } from '../types/healthConsultation';
import { getUnifiedDeviceId } from './revenueCatService';

/**
 * Get device ID for user identification - UNIFIED REVENUECAT SYSTEM
 */
async function getDeviceId(): Promise<string> {
  return await getUnifiedDeviceId();
}

/**
 * Extract RevenueCat user ID from unified device ID
 */
function extractRevenueCatUserId(deviceId: string): string {
  // If it starts with 'rc_', extract the RevenueCat user ID
  if (deviceId.startsWith('rc_')) {
    return deviceId.substring(3); // Remove 'rc_' prefix
  }
  // Otherwise, use the device ID as is
  return deviceId;
}

/**
 * Get all health conversations for the current device
 */
export async function getHealthConversations(): Promise<HealthConversation[]> {
  try {
    const deviceId = await getDeviceId();

    const { data, error } = await supabase
      .from('health_conversations')
      .select('*')
      .eq('device_id', deviceId)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching conversations:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getHealthConversations:', error);
    throw error;
  }
}

/**
 * Get a specific health conversation by ID
 */
export async function getHealthConversation(
  conversationId: string
): Promise<HealthConversation | null> {
  try {
    const deviceId = await getDeviceId();

    const { data, error } = await supabase
      .from('health_conversations')
      .select('*')
      .eq('id', conversationId)
      .eq('device_id', deviceId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      console.error('Error fetching conversation:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getHealthConversation:', error);
    throw error;
  }
}

/**
 * Create a new health conversation
 */
export async function createHealthConversation(
  conversation: Omit<
    HealthConversation,
    'id' | 'createdAt' | 'updatedAt' | 'deviceId'
  >
): Promise<HealthConversation> {
  try {
    const deviceId = await getDeviceId();

    const { data, error } = await supabase
      .from('health_conversations')
      .insert({
        ...conversation,
        device_id: deviceId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in createHealthConversation:', error);
    throw error;
  }
}

/**
 * Update a health conversation
 */
export async function updateHealthConversation(
  conversationId: string,
  updates: Partial<Omit<HealthConversation, 'id' | 'createdAt' | 'deviceId'>>
): Promise<HealthConversation> {
  try {
    const deviceId = await getDeviceId();

    const { data, error } = await supabase
      .from('health_conversations')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', conversationId)
      .eq('device_id', deviceId)
      .select()
      .single();

    if (error) {
      console.error('Error updating conversation:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in updateHealthConversation:', error);
    throw error;
  }
}

/**
 * Delete a health conversation
 */
export async function deleteHealthConversation(
  conversationId: string
): Promise<void> {
  try {
    const deviceId = await getDeviceId();

    const { error } = await supabase
      .from('health_conversations')
      .delete()
      .eq('id', conversationId)
      .eq('device_id', deviceId);

    if (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error in deleteHealthConversation:', error);
    throw error;
  }
}

/**
 * Get health messages for a conversation
 */
export async function getHealthMessages(
  conversationId: string,
  limit: number = 50,
  offset: number = 0
) {
  try {
    const { data, error } = await supabase
      .from('health_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getHealthMessages:', error);
    throw error;
  }
}

/**
 * Update conversation statistics (message count, last message, etc.)
 */
export async function updateConversationStats(
  conversationId: string
): Promise<void> {
  try {
    // Get message count and last message
    const { data: messages, error: messagesError } = await supabase
      .from('health_messages')
      .select('content, created_at')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: false })
      .limit(1);

    if (messagesError) {
      console.error('Error fetching messages for stats:', messagesError);
      throw messagesError;
    }

    const { count, error: countError } = await supabase
      .from('health_messages')
      .select('*', { count: 'exact', head: true })
      .eq('conversation_id', conversationId);

    if (countError) {
      console.error('Error counting messages:', countError);
      throw countError;
    }

    const lastMessage = messages && messages.length > 0 ? messages[0] : null;

    // Update conversation with stats
    const { error: updateError } = await supabase
      .from('health_conversations')
      .update({
        message_count: count || 0,
        last_message: lastMessage?.content || null,
        updated_at: new Date().toISOString(),
      })
      .eq('id', conversationId);

    if (updateError) {
      console.error('Error updating conversation stats:', updateError);
      throw updateError;
    }
  } catch (error) {
    console.error('Error in updateConversationStats:', error);
    throw error;
  }
}
