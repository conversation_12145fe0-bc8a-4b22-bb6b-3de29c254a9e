// AI Model Configuration Service
// Centralized management of AI models for different tasks

// Primary and fallback model definitions
export const PRIMARY_MODEL = 'google/gemini-2.0-flash-exp:free';
export const FALLBACK_MODEL = 'google/gemini-2.0-flash-001';
// export const FALLBACK_MODEL = 'google/gemini-2.0-flash-exp:free';

export type AITask =
  | 'e-code-detection'
  | 'nutrition-extraction'
  | 'food-analysis'
  | 'health-consultation';

export interface ModelConfig {
  primary: string;
  fallback?: string;
  maxTokens: number;
  temperature: number;
}

// Model configurations for different tasks
const MODEL_CONFIGS: Record<AITask, ModelConfig> = {
  'e-code-detection': {
    primary: PRIMARY_MODEL,
    fallback: FALLBACK_MODEL,
    maxTokens: 200,
    temperature: 0.1,
  },
  'nutrition-extraction': {
    primary: PRIMARY_MODEL,
    fallback: FALLBACK_MODEL,
    maxTokens: 300,
    temperature: 0.1,
  },
  'food-analysis': {
    primary: PRIMARY_MODEL,
    fallback: FALLBACK_MODEL,
    maxTokens: 500,
    temperature: 0.3,
  },
  'health-consultation': {
    primary: PRIMARY_MODEL,
    fallback: FALLBACK_MODEL,
    maxTokens: 800,
    temperature: 0.7, // Higher temperature for more conversational responses
  },
};

/**
 * Get the model configuration for a specific AI task
 * @param task The AI task type
 * @param useFallback Whether to use the fallback model instead of primary
 * @returns Model name and configuration
 */
export const getModelForTask = (
  task: AITask,
  useFallback: boolean = false
): string => {
  const config = MODEL_CONFIGS[task];
  if (!config) {
    throw new Error(`No model configuration found for task: ${task}`);
  }

  if (useFallback && config.fallback) {
    return config.fallback;
  }

  return config.primary;
};

/**
 * Get the complete configuration for a specific AI task
 * @param task The AI task type
 * @returns Complete model configuration including tokens and temperature
 */
export const getModelConfig = (task: AITask): ModelConfig => {
  const config = MODEL_CONFIGS[task];
  if (!config) {
    throw new Error(`No model configuration found for task: ${task}`);
  }

  return config;
};

/**
 * Update the primary model for a specific task
 * @param task The AI task type
 * @param modelName The new model name
 */
export const updatePrimaryModel = (task: AITask, modelName: string): void => {
  if (!MODEL_CONFIGS[task]) {
    throw new Error(`No model configuration found for task: ${task}`);
  }

  MODEL_CONFIGS[task].primary = modelName;
};

/**
 * Update the fallback model for a specific task
 * @param task The AI task type
 * @param modelName The new fallback model name
 */
export const updateFallbackModel = (task: AITask, modelName: string): void => {
  if (!MODEL_CONFIGS[task]) {
    throw new Error(`No model configuration found for task: ${task}`);
  }

  MODEL_CONFIGS[task].fallback = modelName;
};

/**
 * Get all available models for a task (primary and fallback)
 * @param task The AI task type
 * @returns Array of available model names
 */
export const getAvailableModels = (task: AITask): string[] => {
  const config = MODEL_CONFIGS[task];
  if (!config) {
    throw new Error(`No model configuration found for task: ${task}`);
  }

  const models = [config.primary];
  if (config.fallback) {
    models.push(config.fallback);
  }

  return models;
};

/**
 * Get all configured tasks
 * @returns Array of all AI task types
 */
export const getAllTasks = (): AITask[] => {
  return Object.keys(MODEL_CONFIGS) as AITask[];
};

/**
 * Reset all models to default configuration
 */
export const resetToDefaults = (): void => {
  MODEL_CONFIGS['e-code-detection'] = {
    primary: PRIMARY_MODEL,
    fallback: FALLBACK_MODEL,
    maxTokens: 200,
    temperature: 0.1,
  };

  MODEL_CONFIGS['nutrition-extraction'] = {
    primary: PRIMARY_MODEL,
    fallback: FALLBACK_MODEL,
    maxTokens: 300,
    temperature: 0.1,
  };

  MODEL_CONFIGS['food-analysis'] = {
    primary: PRIMARY_MODEL,
    fallback: FALLBACK_MODEL,
    maxTokens: 500,
    temperature: 0.3,
  };

  MODEL_CONFIGS['health-consultation'] = {
    primary: PRIMARY_MODEL,
    fallback: FALLBACK_MODEL,
    maxTokens: 800,
    temperature: 0.7,
  };
};

/**
 * Get a summary of all current model configurations
 * @returns Object with all current configurations
 */
export const getModelSummary = () => {
  return Object.entries(MODEL_CONFIGS).reduce((summary, [task, config]) => {
    summary[task as AITask] = {
      primary: config.primary,
      fallback: config.fallback || 'None',
      maxTokens: config.maxTokens,
      temperature: config.temperature,
    };
    return summary;
  }, {} as Record<AITask, { primary: string; fallback: string; maxTokens: number; temperature: number }>);
};

/**
 * Update multiple model configurations at once
 * @param updates Object with task-specific updates
 */
export const updateModelConfigs = (
  updates: Partial<Record<AITask, Partial<ModelConfig>>>
) => {
  Object.entries(updates).forEach(([task, config]) => {
    const taskKey = task as AITask;
    if (MODEL_CONFIGS[taskKey] && config) {
      MODEL_CONFIGS[taskKey] = { ...MODEL_CONFIGS[taskKey], ...config };
    }
  });
};

// Export the configurations for direct access if needed
export { MODEL_CONFIGS };
