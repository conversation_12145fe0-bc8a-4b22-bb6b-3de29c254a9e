import AsyncStorage from '@react-native-async-storage/async-storage';
import { getUnifiedDeviceId } from './revenueCatService';
import { supabase } from '../lib/supabase';

// Storage keys
const USAGE_TRACKING_KEY = '@usage_tracking';

// Usage limits for free tier
export const USAGE_LIMITS = {
  MAX_CONVERSATIONS: 3,
  MAX_MESSAGES_PER_CONVERSATION: 2,
} as const;

// Usage tracking interface
export interface UsageData {
  deviceId: string;
  conversationCount: number;
  conversationMessageCounts: Record<string, number>; // conversationId -> messageCount
  lastUpdated: number;
}

/**
 * Get current usage data for the device
 */
export const getUsageData = async (): Promise<UsageData> => {
  try {
    const deviceId = await getUnifiedDeviceId();
    
    // Try to get from local storage first
    const localData = await AsyncStorage.getItem(USAGE_TRACKING_KEY);
    if (localData) {
      const parsed: UsageData = JSON.parse(localData);
      if (parsed.deviceId === deviceId) {
        return parsed;
      }
    }

    // If no local data or device ID mismatch, fetch from Supabase
    const { data: conversations, error } = await supabase
      .from('health_conversations')
      .select('id, message_count')
      .eq('device_id', deviceId)
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching usage data from Supabase:', error);
      // Return default data
      return {
        deviceId,
        conversationCount: 0,
        conversationMessageCounts: {},
        lastUpdated: Date.now(),
      };
    }

    // Build usage data from Supabase
    const conversationMessageCounts: Record<string, number> = {};
    conversations?.forEach((conv) => {
      conversationMessageCounts[conv.id] = conv.message_count || 0;
    });

    const usageData: UsageData = {
      deviceId,
      conversationCount: conversations?.length || 0,
      conversationMessageCounts,
      lastUpdated: Date.now(),
    };

    // Cache locally
    await AsyncStorage.setItem(USAGE_TRACKING_KEY, JSON.stringify(usageData));
    
    return usageData;
  } catch (error) {
    console.error('Error getting usage data:', error);
    const deviceId = await getUnifiedDeviceId();
    return {
      deviceId,
      conversationCount: 0,
      conversationMessageCounts: {},
      lastUpdated: Date.now(),
    };
  }
};

/**
 * Update usage data when a new conversation is created
 */
export const trackNewConversation = async (conversationId: string): Promise<void> => {
  try {
    const usageData = await getUsageData();
    
    // Update conversation count and add new conversation
    usageData.conversationCount += 1;
    usageData.conversationMessageCounts[conversationId] = 0;
    usageData.lastUpdated = Date.now();

    // Save to local storage
    await AsyncStorage.setItem(USAGE_TRACKING_KEY, JSON.stringify(usageData));
    
    console.log('📊 Tracked new conversation:', {
      conversationId,
      totalConversations: usageData.conversationCount,
    });
  } catch (error) {
    console.error('Error tracking new conversation:', error);
  }
};

/**
 * Update usage data when a new message is sent
 */
export const trackNewMessage = async (conversationId: string): Promise<void> => {
  try {
    const usageData = await getUsageData();
    
    // Update message count for this conversation
    const currentCount = usageData.conversationMessageCounts[conversationId] || 0;
    usageData.conversationMessageCounts[conversationId] = currentCount + 1;
    usageData.lastUpdated = Date.now();

    // Save to local storage
    await AsyncStorage.setItem(USAGE_TRACKING_KEY, JSON.stringify(usageData));
    
    console.log('📊 Tracked new message:', {
      conversationId,
      messageCount: usageData.conversationMessageCounts[conversationId],
    });
  } catch (error) {
    console.error('Error tracking new message:', error);
  }
};

/**
 * Check if user can create a new conversation
 */
export const canCreateNewConversation = async (): Promise<boolean> => {
  try {
    const usageData = await getUsageData();
    return usageData.conversationCount < USAGE_LIMITS.MAX_CONVERSATIONS;
  } catch (error) {
    console.error('Error checking conversation limit:', error);
    return false;
  }
};

/**
 * Check if user can send a new message in a conversation
 */
export const canSendMessage = async (conversationId: string): Promise<boolean> => {
  try {
    const usageData = await getUsageData();
    const messageCount = usageData.conversationMessageCounts[conversationId] || 0;
    return messageCount < USAGE_LIMITS.MAX_MESSAGES_PER_CONVERSATION;
  } catch (error) {
    console.error('Error checking message limit:', error);
    return false;
  }
};

/**
 * Get usage statistics for display
 */
export const getUsageStats = async () => {
  try {
    const usageData = await getUsageData();
    
    return {
      conversationsUsed: usageData.conversationCount,
      conversationsLimit: USAGE_LIMITS.MAX_CONVERSATIONS,
      conversationsRemaining: Math.max(0, USAGE_LIMITS.MAX_CONVERSATIONS - usageData.conversationCount),
      messagesPerConversation: usageData.conversationMessageCounts,
      messagesLimit: USAGE_LIMITS.MAX_MESSAGES_PER_CONVERSATION,
    };
  } catch (error) {
    console.error('Error getting usage stats:', error);
    return {
      conversationsUsed: 0,
      conversationsLimit: USAGE_LIMITS.MAX_CONVERSATIONS,
      conversationsRemaining: USAGE_LIMITS.MAX_CONVERSATIONS,
      messagesPerConversation: {},
      messagesLimit: USAGE_LIMITS.MAX_MESSAGES_PER_CONVERSATION,
    };
  }
};

/**
 * Reset usage data (for testing or when user subscribes)
 */
export const resetUsageData = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(USAGE_TRACKING_KEY);
    console.log('✅ Usage data reset');
  } catch (error) {
    console.error('Error resetting usage data:', error);
  }
};

/**
 * Check if user should see paywall
 * Returns true if user has reached limits
 */
export const shouldShowPaywall = async (
  action: 'create_conversation' | 'send_message',
  conversationId?: string
): Promise<boolean> => {
  try {
    if (action === 'create_conversation') {
      const canCreate = await canCreateNewConversation();
      return !canCreate;
    }
    
    if (action === 'send_message' && conversationId) {
      const canSend = await canSendMessage(conversationId);
      return !canSend;
    }
    
    return false;
  } catch (error) {
    console.error('Error checking paywall condition:', error);
    return false;
  }
};
