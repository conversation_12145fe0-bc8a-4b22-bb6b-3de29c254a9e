import { supabase } from '../lib/supabase';
import {
  HealthConversation,
  HealthMessage,
  CreateConversationRequest,
  CreateConversationResponse,
} from '../types/healthConsultation';

// Database interfaces matching Supabase schema
export interface HealthConversationDB {
  id: string;
  title: string;
  device_id: string | null;
  created_at: string;
  updated_at: string;
  message_count: number;
  last_message: string | null;
  is_active: boolean;
  metadata?: any;
}

export interface HealthMessageDB {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  message_type:
    | 'text'
    | 'quick_reply'
    | 'assessment'
    | 'recommendation'
    | 'system';
  metadata?: any;
  created_at: string;
  ai_response_status: 'pending' | 'completed' | 'failed';
  ai_response_error?: string;
  paired_message_id?: string;
}

// Transform database row to app format
export const transformConversationFromDB = (
  dbRow: HealthConversationDB
): HealthConversation => {
  return {
    id: dbRow.id,
    title: dbRow.title,
    deviceId: dbRow.device_id || undefined,
    createdAt: new Date(dbRow.created_at).getTime(),
    updatedAt: new Date(dbRow.updated_at).getTime(),
    messageCount: dbRow.message_count,
    lastMessage: dbRow.last_message || undefined,
    isActive: dbRow.is_active,
    metadata: dbRow.metadata,
  };
};

// Transform app format to database format
const transformConversationToDB = (
  conversation: Partial<HealthConversation>
): Partial<HealthConversationDB> => {
  const dbData: Partial<HealthConversationDB> = {
    title: conversation.title,
    device_id: conversation.deviceId || null,
    message_count: conversation.messageCount || 0,
    last_message: conversation.lastMessage || null,
    is_active:
      conversation.isActive !== undefined ? conversation.isActive : true,
    metadata: conversation.metadata,
  };

  // Note: ID is always auto-generated by Supabase, never set manually
  // This ensures consistent UUID format and prevents conflicts

  return dbData;
};

// Transform database message to app format
export const transformMessageFromDB = (
  dbRow: HealthMessageDB
): HealthMessage => {
  return {
    id: dbRow.id,
    conversationId: dbRow.conversation_id,
    role: dbRow.role,
    content: dbRow.content,
    messageType: dbRow.message_type,
    timestamp: new Date(dbRow.created_at).getTime(),
    aiResponseStatus: dbRow.ai_response_status,
    aiResponseError: dbRow.ai_response_error,
    pairedMessageId: dbRow.paired_message_id,
    metadata: dbRow.metadata,
  };
};

// Transform app message to database format
const transformMessageToDB = (
  message: Partial<HealthMessage>
): Partial<HealthMessageDB> => {
  return {
    // Note: ID is always auto-generated by Supabase, never set manually
    conversation_id: message.conversationId,
    role: message.role,
    content: message.content,
    message_type: message.messageType || 'text',
    ai_response_status: message.aiResponseStatus || 'completed',
    ai_response_error: message.aiResponseError,
    paired_message_id: message.pairedMessageId,
    metadata: message.metadata,
  };
};

/**
 * Create a new health conversation in Supabase
 */
export const createHealthConversation = async (
  request: CreateConversationRequest
): Promise<CreateConversationResponse | null> => {
  try {
    const conversationData = transformConversationToDB({
      title: request.title || 'Health Consultation',
      deviceId: request.deviceId,
      isActive: true,
      messageCount: 0,
      metadata: {
        topic: request.topic,
        language: request.language,
      },
    });

    const { data, error } = await supabase
      .from('health_conversations')
      .insert([conversationData])
      .select()
      .single();

    if (error) {
      console.error('Error creating health conversation:', error);
      return null;
    }

    const conversation = transformConversationFromDB(data);
    return { conversation };
  } catch (error) {
    console.error('Exception creating health conversation:', error);
    return null;
  }
};

/**
 * Get health conversations for a device
 */
export const getHealthConversations = async (
  deviceId: string,
  limit: number = 50,
  offset: number = 0
): Promise<HealthConversation[]> => {
  try {
    const { data, error } = await supabase
      .from('health_conversations')
      .select('*')
      .eq('device_id', deviceId)
      .eq('is_active', true)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching health conversations:', error);
      return [];
    }

    return data.map(transformConversationFromDB);
  } catch (error) {
    console.error('Exception fetching health conversations:', error);
    return [];
  }
};

/**
 * Get a specific health conversation
 */
export const getHealthConversation = async (
  conversationId: string
): Promise<HealthConversation | null> => {
  try {
    const { data, error } = await supabase
      .from('health_conversations')
      .select('*')
      .eq('id', conversationId)
      .single();

    if (error) {
      console.error('Error fetching health conversation:', error);
      return null;
    }

    return transformConversationFromDB(data);
  } catch (error) {
    console.error('Exception fetching health conversation:', error);
    return null;
  }
};

/**
 * Update a health conversation
 */
export const updateHealthConversation = async (
  conversationId: string,
  updates: Partial<HealthConversation>
): Promise<HealthConversation | null> => {
  try {
    const dbUpdates = transformConversationToDB(updates);
    dbUpdates.updated_at = new Date().toISOString();

    const { data, error } = await supabase
      .from('health_conversations')
      .update(dbUpdates)
      .eq('id', conversationId)
      .select()
      .single();

    if (error) {
      console.error('Error updating health conversation:', error);
      return null;
    }

    return transformConversationFromDB(data);
  } catch (error) {
    console.error('Exception updating health conversation:', error);
    return null;
  }
};

/**
 * Delete a health conversation (soft delete)
 */
export const deleteHealthConversation = async (
  conversationId: string
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('health_conversations')
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', conversationId);

    if (error) {
      console.error('Error deleting health conversation:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception deleting health conversation:', error);
    return false;
  }
};

/**
 * Save a health message to Supabase
 */
export const saveHealthMessage = async (
  message: HealthMessage
): Promise<HealthMessage | null> => {
  try {
    const messageData = transformMessageToDB(message);

    const { data, error } = await supabase
      .from('health_messages')
      .insert([messageData])
      .select()
      .single();

    if (error) {
      console.error('Error saving health message:', error);
      return null;
    }

    return transformMessageFromDB(data);
  } catch (error) {
    console.error('Exception saving health message:', error);
    return null;
  }
};

/**
 * Save a single user message with pending AI response status
 */
export const saveUserMessageWithPendingResponse = async (
  message: Omit<HealthMessage, 'id'>
): Promise<HealthMessage> => {
  try {
    const messageData = {
      ...message,
      aiResponseStatus: 'pending' as const,
    };

    console.log('🔄 Saving user message with pending AI response:', {
      conversationId: messageData.conversationId,
      content: messageData.content.substring(0, 50) + '...',
    });

    const messagesData = [transformMessageToDB(messageData)];

    const { data, error } = await supabase
      .from('health_messages')
      .insert(messagesData)
      .select();

    if (error) {
      console.error('❌ Error saving user message:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      throw new Error('No data returned from Supabase');
    }

    const savedMessage = transformMessageFromDB(data[0]);
    console.log('✅ User message saved with ID:', savedMessage.id);

    return savedMessage;
  } catch (error) {
    console.error('❌ Exception saving user message:', error);
    throw error;
  }
};

/**
 * Save AI response and link it to user message
 */
export const saveAIResponseMessage = async (
  aiMessage: Omit<HealthMessage, 'id'>,
  userMessageId: string
): Promise<HealthMessage> => {
  try {
    const messageData = {
      ...aiMessage,
      aiResponseStatus: 'completed' as const,
      pairedMessageId: userMessageId,
    };

    console.log('🔄 Saving AI response message:', {
      conversationId: messageData.conversationId,
      pairedWith: userMessageId,
      content: messageData.content.substring(0, 50) + '...',
    });

    const messagesData = [transformMessageToDB(messageData)];

    const { data, error } = await supabase
      .from('health_messages')
      .insert(messagesData)
      .select();

    if (error) {
      console.error('❌ Error saving AI response:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      throw new Error('No data returned from Supabase');
    }

    const savedMessage = transformMessageFromDB(data[0]);
    console.log('✅ AI response saved with ID:', savedMessage.id);

    // Update user message to link back to AI response
    await updateMessagePairing(userMessageId, savedMessage.id);

    return savedMessage;
  } catch (error) {
    console.error('❌ Exception saving AI response:', error);
    throw error;
  }
};

/**
 * Update message pairing (link user message to AI response)
 */
const updateMessagePairing = async (
  userMessageId: string,
  aiMessageId: string
): Promise<void> => {
  try {
    const { error } = await supabase
      .from('health_messages')
      .update({
        paired_message_id: aiMessageId,
        ai_response_status: 'completed',
      })
      .eq('id', userMessageId);

    if (error) {
      console.error('❌ Error updating message pairing:', error);
      throw error;
    }

    console.log('✅ Message pairing updated:', { userMessageId, aiMessageId });
  } catch (error) {
    console.error('❌ Exception updating message pairing:', error);
    throw error;
  }
};

/**
 * Mark user message AI response as failed
 */
export const markAIResponseFailed = async (
  userMessageId: string,
  errorMessage: string
): Promise<void> => {
  try {
    const { error } = await supabase
      .from('health_messages')
      .update({
        ai_response_status: 'failed',
        ai_response_error: errorMessage,
      })
      .eq('id', userMessageId);

    if (error) {
      console.error('❌ Error marking AI response as failed:', error);
      throw error;
    }

    console.log('✅ AI response marked as failed:', {
      userMessageId,
      errorMessage,
    });
  } catch (error) {
    console.error('❌ Exception marking AI response as failed:', error);
    throw error;
  }
};

/**
 * Mark user message AI response as completed
 */
export const markAIResponseCompleted = async (
  userMessageId: string
): Promise<void> => {
  try {
    const { error } = await supabase
      .from('health_messages')
      .update({
        ai_response_status: 'completed',
        ai_response_error: null, // Clear any previous error
      })
      .eq('id', userMessageId);

    if (error) {
      console.error('❌ Error marking AI response as completed:', error);
      throw error;
    }

    console.log('✅ AI response marked as completed:', {
      userMessageId,
    });
  } catch (error) {
    console.error('❌ Exception marking AI response as completed:', error);
    throw error;
  }
};

/**
 * Get failed messages for retry functionality
 */
export const getFailedMessages = async (
  conversationId: string
): Promise<HealthMessage[]> => {
  try {
    const { data, error } = await supabase
      .from('health_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .eq('ai_response_status', 'failed')
      .eq('role', 'user')
      .order('created_at', { ascending: true });

    if (error) {
      console.error('❌ Error fetching failed messages:', error);
      throw error;
    }

    return data ? data.map(transformMessageFromDB) : [];
  } catch (error) {
    console.error('❌ Exception fetching failed messages:', error);
    throw error;
  }
};

/**
 * Reset failed message for retry
 */
export const resetFailedMessage = async (
  userMessageId: string
): Promise<void> => {
  try {
    const { error } = await supabase
      .from('health_messages')
      .update({
        ai_response_status: 'pending',
        ai_response_error: null,
      })
      .eq('id', userMessageId);

    if (error) {
      console.error('❌ Error resetting failed message:', error);
      throw error;
    }

    console.log('✅ Failed message reset for retry:', userMessageId);
  } catch (error) {
    console.error('❌ Exception resetting failed message:', error);
    throw error;
  }
};

/**
 * Save multiple health messages to Supabase (legacy function)
 */
export const saveHealthMessages = async (
  messages: HealthMessage[]
): Promise<HealthMessage[]> => {
  try {
    console.log('🔄 Attempting to save messages to Supabase:', {
      messageCount: messages.length,
      messageIds: messages.map((m) => m.id),
      conversationIds: messages.map((m) => m.conversationId),
    });

    const messagesData = messages.map(transformMessageToDB);

    console.log('🔄 Transformed messages data:', messagesData);

    const { data, error } = await supabase
      .from('health_messages')
      .insert(messagesData)
      .select();

    if (error) {
      console.error('❌ Error saving health messages:', {
        error,
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint,
      });
      return [];
    }

    console.log('✅ Successfully saved messages to Supabase:', {
      savedCount: data?.length || 0,
      savedIds: data?.map((d) => d.id) || [],
    });

    return data.map(transformMessageFromDB);
  } catch (error) {
    console.error('❌ Exception saving health messages:', error);
    return [];
  }
};

/**
 * Get health messages for a conversation
 */
export const getHealthMessages = async (
  conversationId: string,
  limit: number = 50,
  offset: number = 0
): Promise<HealthMessage[]> => {
  try {
    const { data, error } = await supabase
      .from('health_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching health messages:', error);
      return [];
    }

    return data.map(transformMessageFromDB);
  } catch (error) {
    console.error('Exception fetching health messages:', error);
    return [];
  }
};

/**
 * Get actual message count for a conversation
 */
export const getConversationMessageCount = async (
  conversationId: string
): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('health_messages')
      .select('*', { count: 'exact', head: true })
      .eq('conversation_id', conversationId);

    if (error) {
      console.error('Error getting message count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Exception getting message count:', error);
    return 0;
  }
};

/**
 * Update conversation message count and last message with real data
 */
export const updateConversationStats = async (
  conversationId: string,
  lastMessage?: string
): Promise<boolean> => {
  try {
    // Get actual message count from database
    const actualMessageCount = await getConversationMessageCount(
      conversationId
    );

    // Get last message if not provided
    let finalLastMessage = lastMessage;
    if (!finalLastMessage) {
      const { data: lastMessageData } = await supabase
        .from('health_messages')
        .select('content')
        .eq('conversation_id', conversationId)
        .order('timestamp', { ascending: false })
        .limit(1)
        .single();

      finalLastMessage = lastMessageData?.content || '';
    }

    const { error } = await supabase
      .from('health_conversations')
      .update({
        message_count: actualMessageCount,
        last_message: (finalLastMessage || '').substring(0, 200), // Limit length
        updated_at: new Date().toISOString(),
      })
      .eq('id', conversationId);

    if (error) {
      console.error('Error updating conversation stats:', error);
      return false;
    }

    // Silently update conversation stats
    return true;
  } catch (error) {
    console.error('Exception updating conversation stats:', error);
    return false;
  }
};

/**
 * Fix message counts for all conversations (utility function)
 */
export const fixAllConversationMessageCounts = async (): Promise<void> => {
  try {
    console.log('🔧 Fixing message counts for all conversations...');

    // Get all conversations
    const { data: conversations, error } = await supabase
      .from('health_conversations')
      .select('id');

    if (error) {
      console.error('Error getting conversations:', error);
      return;
    }

    if (!conversations || conversations.length === 0) {
      console.log('No conversations found');
      return;
    }

    // Fix each conversation
    for (const conversation of conversations) {
      await updateConversationStats(conversation.id);
    }

    console.log(
      `🎉 Fixed message counts for ${conversations.length} conversations`
    );
  } catch (error) {
    console.error('Exception fixing conversation message counts:', error);
  }
};

/**
 * Migrate conversations from old device ID to new device ID
 * Used when user logs in with RevenueCat and we need to merge conversations
 */
export const migrateConversationsToNewDeviceId = async (
  oldDeviceId: string,
  newDeviceId: string
): Promise<void> => {
  try {
    console.log(
      '🔄 Migrating conversations from',
      oldDeviceId,
      'to',
      newDeviceId
    );

    // Check if there are conversations to migrate
    const { data: conversationsToMigrate, error: fetchError } = await supabase
      .from('health_conversations')
      .select('id, title')
      .eq('device_id', oldDeviceId);

    if (fetchError) {
      console.error('Error fetching conversations to migrate:', fetchError);
      return;
    }

    if (!conversationsToMigrate || conversationsToMigrate.length === 0) {
      console.log('No conversations found to migrate');
      return;
    }

    console.log(
      `Found ${conversationsToMigrate.length} conversations to migrate`
    );

    // Update device_id for all conversations
    const { error: updateError } = await supabase
      .from('health_conversations')
      .update({ device_id: newDeviceId })
      .eq('device_id', oldDeviceId);

    if (updateError) {
      console.error('Error migrating conversations:', updateError);
      return;
    }

    console.log('✅ Successfully migrated conversations to new device ID');
  } catch (error) {
    console.error('❌ Failed to migrate conversations:', error);
  }
};
