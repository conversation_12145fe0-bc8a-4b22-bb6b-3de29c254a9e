import axios from 'axios';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import { getAPIConfiguration } from './SupabaseAPIConfig';
import { getModelForTask, getModelConfig } from './AIModelConfig';

// Function to convert image to base64
const imageToBase64 = async (uri: string) => {
  try {
    // First compress and resize the image to reduce size
    const manipResult = await manipulateAsync(
      uri,
      [{ resize: { width: 800 } }],
      { compress: 1, format: SaveFormat.JPEG }
    );

    // Convert to base64
    const base64 = await FileSystem.readAsStringAsync(manipResult.uri, {
      encoding: FileSystem.EncodingType.Base64,
    });

    return base64;
  } catch (error) {
    console.error('Error converting image to base64:', error);
    throw new Error('Failed to process image');
  }
};

// Function to get system prompt for E-code detection
const getECodeSystemPrompt = (language: 'en' | 'tr') => {
  if (language === 'tr') {
    return `Sen bir gıda katkı maddesi uzmanısın. Sana gösterilen gıda ürünü etiketindeki görüntüden TÜM katkı maddelerini tespit etmen gerekiyor.

ÖNEMLI KURALLAR:
1. Hem E-kodlarını (E100, E200, E300 gibi) hem de katkı maddesi isimlerini ara
2. Yaygın katkı maddesi isimleri:
   - Askorbik Asit, C Vitamini → E300
   - Sitrik Asit → E330
   - Sorbik Asit → E200
   - Laktik Asit → E270
   - Monosodyum Glutamat, MSG, Çin Tuzu → E621
   - Ksantan Sakızı, Ksantan Gam, Xanthan → E415
   - Guar Sakızı, Guar Gam → E412
   - Kurkumin → E100
   - Karmin → E120
   - Sodyum Benzoat → E211
   - Potasyum Sorbat → E202
   - Maltodekstrin → E1400
   - Dekstrin → E1400
   - Propilen Glikol → E1520
   - L-Sistein → E920
3. Yazım hatalarını ve alternatif isimleri de dikkate al
4. Cevabını SADECE E-kod formatında ver, virgülle ayır (örn: E300, E200, E415)
5. Eğer hiç katkı maddesi bulamazsan "YOK" yaz
6. Başka hiçbir açıklama, yorum veya ek bilgi verme

Örnek cevap formatı: E100, E200, E300
Veya katkı maddesi yoksa: YOK`;
  } else {
    return `You are a food additive expert. You need to detect ALL food additives from the food product label image shown to you.

IMPORTANT RULES:
1. Look for both E-codes (E100, E200, E300, etc.) AND additive names
2. Common additive names:
   - Ascorbic Acid, Vitamin C → E300
   - Citric Acid → E330
   - Sorbic Acid → E200
   - Lactic Acid → E270
   - Monosodium Glutamate, MSG, Chinese Salt → E621
   - Xanthan Gum, Xanthan → E415
   - Guar Gum, Guar → E412
   - Curcumin → E100
   - Carmine → E120
   - Sodium Benzoate → E211
   - Potassium Sorbate → E202
   - Maltodextrin → E1400
   - Dextrin → E1400
   - Propylene Glycol → E1520
   - L-Cysteine → E920
3. Consider typos and alternative names
4. Give your answer ONLY in E-code format, separated by commas (e.g., E300, E200, E415)
5. If you find no additives, write "NONE"
6. Do not provide any other explanations, comments, or additional information

Example response format: E100, E200, E300
Or if no additives: NONE`;
  }
};

// Function to get user prompt for E-code detection
const getECodeUserPrompt = (language: 'en' | 'tr') => {
  if (language === 'tr') {
    return 'Bu gıda ürünü etiketinde hangi katkı maddeleri var? Hem E-kodları hem de isimlerini ara ve sadece E-kod formatında listele.';
  } else {
    return 'What food additives are in this food product label? Look for both E-codes and names, and list only in E-code format.';
  }
};

// Helper function for API calls with retry logic
const makeAPICallWithRetry = async (
  apiConfig: any,
  requestData: any,
  maxRetries: number = 30,
  retryDelay: number = 3000
): Promise<any> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`API call attempt ${attempt}/${maxRetries}`);

      const response = await axios.post(apiConfig.api_url, requestData, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiConfig.api_key}`,
          'HTTP-Referer': 'https://additives.app',
        },
      });

      return response;
    } catch (error: any) {
      lastError = error;

      if (error.response?.status === 429 || attempt < maxRetries) {
        console.log(
          `Rate limit hit, retrying in ${retryDelay}ms... (attempt ${attempt}/${maxRetries})`
        );
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
        continue;
      }

      // If it's not a rate limit error or we've exhausted retries, throw
      throw error;
    }
  }

  throw lastError;
};

// Function to analyze text for E-codes using AI (for debug mode)
export const analyzeTextForECodes = async (
  text: string,
  language: 'en' | 'tr' = 'en'
): Promise<string[]> => {
  try {
    // Get API configuration from Supabase
    const apiConfig = await getAPIConfiguration('openrouter');
    if (!apiConfig) {
      throw new Error('OpenRouter API configuration not found');
    }

    // Set up the prompt for E-code detection from text
    const messages = [
      {
        role: 'system',
        content: getECodeSystemPrompt(language),
      },
      {
        role: 'user',
        content: getECodeUserPrompt(language) + '\n\nText to analyze:\n' + text,
      },
    ];

    // Get model configuration for E-code detection
    const modelConfig = getModelConfig('e-code-detection');
    const model = getModelForTask('e-code-detection');

    // Make API call to AI service with retry logic
    const response = await makeAPICallWithRetry(apiConfig, {
      model: model,
      messages: messages,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
    });

    // Extract response text
    const responseText = response.data.choices[0].message.content
      .trim()
      .toUpperCase();

    console.log('AI E-code detection response (text mode):', responseText);

    // Parse the response to extract E-codes
    if (
      responseText === 'YOK' ||
      responseText === 'NONE' ||
      responseText === ''
    ) {
      return [];
    }

    // Extract E-codes using regex
    const eCodeRegex = /E\d{3,4}[A-Z]?/g;
    const matches = responseText.match(eCodeRegex) || [];

    // Remove duplicates and return
    return [...new Set(matches as string[])];
  } catch (error) {
    console.error('Error analyzing text with AI:', error);
    throw new Error('Failed to analyze text with AI');
  }
};

// Function to analyze image for E-codes using AI
export const analyzeImageForECodes = async (
  imageUri: string,
  language: 'en' | 'tr' = 'en'
): Promise<string[]> => {
  try {
    // Get API configuration from Supabase
    const apiConfig = await getAPIConfiguration('openrouter');
    if (!apiConfig) {
      throw new Error('OpenRouter API configuration not found');
    }

    // Convert image to base64
    const base64Image = await imageToBase64(imageUri);

    // Set up the prompt for E-code detection
    const messages = [
      {
        role: 'system',
        content: getECodeSystemPrompt(language),
      },
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: getECodeUserPrompt(language),
          },
          {
            type: 'image_url',
            image_url: {
              url: `data:image/jpeg;base64,${base64Image}`,
            },
          },
        ],
      },
    ];

    // Get model configuration for E-code detection
    const modelConfig = getModelConfig('e-code-detection');
    const model = getModelForTask('e-code-detection');

    // Make API call to AI service with retry logic
    const response = await makeAPICallWithRetry(apiConfig, {
      model: model,
      messages: messages,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
    });

    // Extract response text
    const responseText = response.data.choices[0].message.content
      .trim()
      .toUpperCase();

    console.log('AI E-code detection response:', responseText);

    // Parse the response to extract E-codes
    if (
      responseText === 'YOK' ||
      responseText === 'NONE' ||
      responseText === ''
    ) {
      return [];
    }

    // Extract E-codes using regex
    const eCodeRegex = /E\d{3,4}[A-Z]?/g;
    const matches = responseText.match(eCodeRegex) || [];

    // Remove duplicates and return
    return [...new Set(matches as string[])];
  } catch (error) {
    console.error('Error analyzing image with AI:', error);
    throw new Error('Failed to analyze image with AI');
  }
};

// Function to get system prompt for nutrition extraction
const getNutritionSystemPrompt = (language: 'en' | 'tr') => {
  if (language === 'tr') {
    return `Sen bir beslenme uzmanısın. Sana gösterilen gıda ürünü etiketindeki besin değerleri tablosundan bilgileri çıkarman gerekiyor.

ÖNEMLI KURALLAR:
1. Sadece 100g başına değerleri ara
2. Aradığın bilgiler:
   - Enerji (kJ ve kcal - her ikisini de ara)
   - Yağ (g)
   - Doymuş yağ (g)
   - Karbonhidrat (g)
   - Şeker (g)
   - Protein (g)
   - Tuz (g)
   - Lif (g)
3. Cevabını SADECE bu JSON formatında ver (başka hiçbir metin ekleme):
{"energy_kj_100g": sayı_veya_null, "energy_kcal_100g": sayı_veya_null, "fat_100g": sayı_veya_null, "saturated_fat_100g": sayı_veya_null, "carbohydrates_100g": sayı_veya_null, "sugars_100g": sayı_veya_null, "proteins_100g": sayı_veya_null, "salt_100g": sayı_veya_null, "fiber_100g": sayı_veya_null}
4. Eğer bir değer bulamazsan null yaz
5. Hem kJ hem kcal değerlerini ara - genellikle "1700 kJ / 403 kcal" şeklinde yazılır
6. Sadece JSON cevabı ver, markdown, açıklama veya başka hiçbir metin ekleme`;
  } else {
    return `You are a nutrition expert. You need to extract nutrition information from the nutrition facts table shown in the food product label image.

IMPORTANT RULES:
1. Look for values per 100g only
2. Information to extract:
   - Energy (kJ and kcal - look for both)
   - Fat (g)
   - Saturated fat (g)
   - Carbohydrates (g)
   - Sugars (g)
   - Protein (g)
   - Salt (g)
   - Fiber (g)
3. Give your answer ONLY in this JSON format (no other text):
{"energy_kj_100g": number_or_null, "energy_kcal_100g": number_or_null, "fat_100g": number_or_null, "saturated_fat_100g": number_or_null, "carbohydrates_100g": number_or_null, "sugars_100g": number_or_null, "proteins_100g": number_or_null, "salt_100g": number_or_null, "fiber_100g": number_or_null}
4. If you can't find a value, write null
5. Look for both kJ and kcal values - usually written as "1700 kJ / 403 kcal"
6. Give only the JSON response, no markdown, explanations or other text`;
  }
};

// Function to get user prompt for nutrition extraction
const getNutritionUserPrompt = (language: 'en' | 'tr') => {
  if (language === 'tr') {
    return 'Bu gıda ürünü etiketindeki besin değerleri tablosundan 100g başına değerleri çıkar. Hem kJ hem kcal değerlerini ara ve JSON formatında ver.';
  } else {
    return 'Extract the nutrition values per 100g from the nutrition facts table in this food product label. Look for both kJ and kcal energy values and provide in JSON format.';
  }
};

// Helper function to process nutrition data and handle energy conversions
const processNutritionData = (nutritionData: any) => {
  const processed = { ...nutritionData };

  // Handle energy values - convert between kJ and kcal if needed
  const energyKj = processed.energy_kj_100g;
  const energyKcal = processed.energy_kcal_100g;

  // If we have kJ but not kcal, convert (1 kcal = 4.184 kJ)
  if (energyKj && !energyKcal) {
    processed.energy_kcal_100g = Math.round(energyKj / 4.184);
  }

  // If we have kcal but not kJ, convert
  if (energyKcal && !energyKj) {
    processed.energy_kj_100g = Math.round(energyKcal * 4.184);
  }

  // For backward compatibility, set energy_100g to kcal value
  if (processed.energy_kcal_100g) {
    processed.energy_100g = processed.energy_kcal_100g;
  } else if (processed.energy_kj_100g) {
    // If only kJ is available, convert to kcal for energy_100g
    processed.energy_100g = Math.round(processed.energy_kj_100g / 4.184);
  }

  return processed;
};

// Function to analyze image for nutrition information using AI
export const analyzeImageForNutrition = async (
  imageUri: string,
  language: 'en' | 'tr' = 'en'
): Promise<any> => {
  try {
    // Get API configuration from Supabase
    const apiConfig = await getAPIConfiguration('openrouter');
    if (!apiConfig) {
      throw new Error('OpenRouter API configuration not found');
    }

    // Convert image to base64
    const base64Image = await imageToBase64(imageUri);

    // Set up the prompt for nutrition extraction
    const messages = [
      {
        role: 'system',
        content: getNutritionSystemPrompt(language),
      },
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: getNutritionUserPrompt(language),
          },
          {
            type: 'image_url',
            image_url: {
              url: `data:image/jpeg;base64,${base64Image}`,
            },
          },
        ],
      },
    ];

    // Get model configuration for nutrition extraction
    const modelConfig = getModelConfig('nutrition-extraction');
    const model = getModelForTask('nutrition-extraction');

    // Make API call to AI service with retry logic
    const response = await makeAPICallWithRetry(apiConfig, {
      model: model,
      messages: messages,
      max_tokens: modelConfig.maxTokens,
      temperature: modelConfig.temperature,
    });

    // Extract response text
    const responseText = response.data.choices[0].message.content.trim();

    console.log('AI nutrition extraction response:', responseText);

    // Try to parse JSON response
    try {
      // Clean the response text - remove any markdown formatting or extra text
      let cleanedResponse = responseText.trim();

      // Remove markdown code blocks if present
      cleanedResponse = cleanedResponse.replace(/```json\s*/gi, '');
      cleanedResponse = cleanedResponse.replace(/```\s*/g, '');

      // Find JSON object in the response
      const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanedResponse = jsonMatch[0];
      }

      console.log('Cleaned nutrition response for parsing:', cleanedResponse);

      const nutritionData = JSON.parse(cleanedResponse);

      // Validate that it's a proper nutrition object
      if (typeof nutritionData === 'object' && nutritionData !== null) {
        // Process energy values to ensure proper format
        const processedData = processNutritionData(nutritionData);
        return processedData;
      } else {
        console.error('Invalid nutrition data structure:', nutritionData);
        return null;
      }
    } catch (parseError) {
      console.error('Error parsing nutrition JSON:', parseError);
      console.error('Raw response was:', responseText);
      return null;
    }
  } catch (error) {
    console.error('Error analyzing image for nutrition with AI:', error);
    return null;
  }
};

// Function to check if AI analysis is available
export const isAIAnalysisAvailable = async (): Promise<boolean> => {
  try {
    const apiConfig = await getAPIConfiguration('openrouter');
    return !!apiConfig && !!apiConfig.api_key && apiConfig.is_active;
  } catch (error) {
    console.error('Error checking AI analysis availability:', error);
    return false;
  }
};
