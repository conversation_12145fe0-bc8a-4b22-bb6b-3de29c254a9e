import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  Dimensions,
  KeyboardAvoidingView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from './context/LocalizationContext';
import {
  useCommunityContributions,
  ErrorReport,
} from './context/CommunityContributionsContext';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';

const SCREEN_HEIGHT =
  Platform.OS === 'web' ? window.innerHeight : Dimensions.get('window').height;
const SCREEN_WIDTH =
  Platform.OS === 'web' ? window.innerWidth : Dimensions.get('window').width;

export default function ReportScreen() {
  const { additiveCode, barcode } = useLocalSearchParams<{
    additiveCode?: string;
    barcode?: string;
  }>();
  const router = useRouter();
  const { t } = useLocalization();
  const { submitErrorReport, isLoading } = useCommunityContributions();

  // Form state
  const [reportType, setReportType] = useState<
    'additive_info' | 'product_info' | 'other'
  >(additiveCode ? 'additive_info' : barcode ? 'product_info' : 'other');
  const [description, setDescription] = useState('');

  // Reset form when screen params change
  useEffect(() => {
    setReportType(
      additiveCode ? 'additive_info' : barcode ? 'product_info' : 'other'
    );
  }, [additiveCode, barcode]);

  // Reset form
  const resetForm = () => {
    setReportType(
      additiveCode ? 'additive_info' : barcode ? 'product_info' : 'other'
    );
    setDescription('');
  };

  // Handle submission
  const handleSubmit = async () => {
    if (!description.trim()) {
      Alert.alert(t('community.error'), t('community.descriptionRequired'));
      return;
    }

    const report: ErrorReport = {
      additive_code: additiveCode,
      barcode: barcode,
      report_type: reportType,
      description: description.trim(),
    };

    const success = await submitErrorReport(report);

    if (success) {
      Alert.alert(
        t('community.thankYou'),
        t('community.errorReportSubmitted'),
        [
          {
            text: t('common.ok'),
            onPress: () => {
              resetForm();
              router.back();
            },
          },
        ]
      );
    } else {
      Alert.alert(t('community.error'), t('community.errorSubmittingReport'));
    }
  };

  return (
    <>
      <StatusBar barStyle="dark-content" />
      <Stack.Screen
        options={{
          title: t('community.reportError'),
        }}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.container}
      >
        <ScrollView
          style={styles.formContainer}
          contentContainerStyle={styles.formContentContainer}
        >
          <Text style={styles.description}>
            {t('community.reportErrorDescription')}
          </Text>

          {additiveCode && (
            <View style={styles.infoContainer}>
              <Text style={styles.infoLabel}>{t('community.additive')}:</Text>
              <Text style={styles.infoValue}>{additiveCode}</Text>
            </View>
          )}

          {barcode && (
            <View style={styles.infoContainer}>
              <Text style={styles.infoLabel}>{t('community.barcode')}:</Text>
              <Text style={styles.infoValue}>{barcode}</Text>
            </View>
          )}

          <Text style={styles.inputLabel}>{t('community.reportType')}</Text>
          <View style={styles.reportTypeContainer}>
            <TouchableOpacity
              style={[
                styles.reportTypeButton,
                reportType === 'additive_info' && styles.reportTypeButtonActive,
              ]}
              onPress={() => setReportType('additive_info')}
            >
              <Text
                style={[
                  styles.reportTypeText,
                  reportType === 'additive_info' && styles.reportTypeTextActive,
                ]}
              >
                {t('community.additiveInfo')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.reportTypeButton,
                reportType === 'product_info' && styles.reportTypeButtonActive,
              ]}
              onPress={() => setReportType('product_info')}
            >
              <Text
                style={[
                  styles.reportTypeText,
                  reportType === 'product_info' && styles.reportTypeTextActive,
                ]}
              >
                {t('community.productInfo')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.reportTypeButton,
                reportType === 'other' && styles.reportTypeButtonActive,
              ]}
              onPress={() => setReportType('other')}
            >
              <Text
                style={[
                  styles.reportTypeText,
                  reportType === 'other' && styles.reportTypeTextActive,
                ]}
              >
                {t('community.other')}
              </Text>
            </TouchableOpacity>
          </View>

          <Text style={styles.inputLabel}>{t('community.description')} *</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder={t('community.descriptionPlaceholder')}
            placeholderTextColor="#999"
            multiline
            numberOfLines={5}
            textAlignVertical="top"
          />

          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleSubmit}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.submitButtonText}>
                {t('community.submit')}
              </Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  formContainer: {
    flex: 1,
  },
  formContentContainer: {
    padding: 20,
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    lineHeight: 22,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 12,
    marginBottom: 15,
  },
  infoLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: '#555',
    marginRight: 10,
  },
  infoValue: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
  },
  inputLabel: {
    fontSize: 17,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
    marginTop: 5,
  },
  reportTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  reportTypeButton: {
    flex: 1,
    padding: 8,
    borderRadius: 12,
    backgroundColor: '#f8f8f8',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  reportTypeButtonActive: {
    backgroundColor: '#2196F3',
    borderColor: '#1976D2',
    shadowColor: '#1976D2',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  reportTypeText: {
    fontSize: 14,
    color: '#555',
    fontWeight: '500',
  },
  reportTypeTextActive: {
    color: 'white',
    fontWeight: '600',
  },
  textInput: {
    backgroundColor:
      Platform.OS === 'ios' ? 'rgba(245, 245, 245, 0.8)' : '#f5f5f5',
    borderRadius: 12,
    padding: 15,
    fontSize: 16,
    color: '#333',
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  textArea: {
    minHeight: 150,
  },
  submitButton: {
    backgroundColor: '#2196F3',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
    shadowColor: '#1976D2',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
