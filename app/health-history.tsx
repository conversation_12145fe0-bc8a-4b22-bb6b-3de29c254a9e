import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';

import { useLocalization } from './context/LocalizationContext';
import { HealthConversation } from './types/healthConsultation';
import ConversationCard from './components/health/ConversationCard';
import {
  ConversationFiltersModal,
  FilterSortOptions,
} from './components/health/ConversationFiltersModal';
import {
  getHealthConversations,
  deleteHealthConversation,
  updateHealthConversation,
  getConversationMessageCount,
} from './services/HealthConsultationSupabase';

export default function HealthHistoryScreen() {
  const { t } = useLocalization();

  // Local state for conversations
  const [conversations, setConversations] = useState<HealthConversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [filterOptions, setFilterOptions] = useState<FilterSortOptions>({
    sortBy: 'date',
    filterBy: 'all',
    dateRange: { startDate: null, endDate: null },
    messageCountRange: { min: null, max: null },
  });
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showSearchSuggestions, setShowSearchSuggestions] = useState(false);
  const [messageCounts, setMessageCounts] = useState<Record<string, number>>(
    {}
  );

  // Load conversations from Supabase
  const loadConversations = async () => {
    try {
      const deviceId =
        await require('./services/revenueCatService').getUnifiedDeviceId();
      const data = await getHealthConversations(deviceId);
      setConversations(data);

      // Load message counts for each conversation
      const counts: Record<string, number> = {};
      await Promise.all(
        data.map(async (conversation) => {
          const count = await getConversationMessageCount(conversation.id);
          counts[conversation.id] = count;
        })
      );
      setMessageCounts(counts);
    } catch (error) {
      console.error('Error loading conversations:', error);
      Alert.alert(t('common.error'), t('healthConsultation.history.loadError'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConversations();
  }, []);

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);

      // Add to search history if query is not empty and not already in history
      if (searchQuery.trim() && !searchHistory.includes(searchQuery.trim())) {
        setSearchHistory((prev) => [searchQuery.trim(), ...prev.slice(0, 4)]); // Keep last 5 searches
      }
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchQuery, searchHistory]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await loadConversations();
    } catch (error) {
      console.error('Error refreshing conversations:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Filter and sort conversations
  const filteredAndSortedConversations = useMemo(() => {
    let filtered = conversations;

    // Apply search filter (using debounced query for better performance)
    if (debouncedSearchQuery.trim()) {
      const query = debouncedSearchQuery.toLowerCase();
      filtered = filtered.filter((conv) => {
        // Search in title
        if (conv.title.toLowerCase().includes(query)) {
          return true;
        }

        // Search in last message
        if (conv.lastMessage?.toLowerCase().includes(query)) {
          return true;
        }

        // Search in metadata tags
        if (
          conv.metadata?.tags?.some((tag) => tag.toLowerCase().includes(query))
        ) {
          return true;
        }

        // Search in topic
        if (conv.metadata?.topic?.toLowerCase().includes(query)) {
          return true;
        }

        return false;
      });
    }

    // Apply status filter
    if (filterOptions.filterBy !== 'all') {
      filtered = filtered.filter((conv) => {
        if (filterOptions.filterBy === 'archived') {
          return conv.isArchived === true;
        }
        return conv.isArchived !== true; // active
      });
    }

    // Apply date range filter
    if (filterOptions.dateRange.startDate || filterOptions.dateRange.endDate) {
      filtered = filtered.filter((conv) => {
        const convDate = new Date(conv.updatedAt);
        const startDate = filterOptions.dateRange.startDate;
        const endDate = filterOptions.dateRange.endDate;

        if (startDate && convDate < startDate) return false;
        if (endDate && convDate > endDate) return false;
        return true;
      });
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (filterOptions.sortBy) {
        case 'date':
          return (
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          );
        case 'created':
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case 'messages':
          return (b.messageCount || 0) - (a.messageCount || 0);
        case 'title':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

    return sorted;
  }, [conversations, debouncedSearchQuery, filterOptions]);

  const handleConversationPress = (conversation: HealthConversation) => {
    // Directly open the conversation chat
    router.push(`/health-chat?conversationId=${conversation.id}`);
  };

  const handleDeleteConversation = useCallback(
    async (conversation: HealthConversation) => {
      Alert.alert(
        t('healthConsultation.history.deleteTitle'),
        t('healthConsultation.history.deleteMessage'),
        [
          {
            text: t('common.cancel'),
            style: 'cancel',
          },
          {
            text: t('common.delete'),
            style: 'destructive',
            onPress: async () => {
              try {
                await deleteHealthConversation(conversation.id);
                // Remove from local state
                setConversations((prev) =>
                  prev.filter((c) => c.id !== conversation.id)
                );
              } catch (error) {
                console.error('Error deleting conversation:', error);
                Alert.alert(
                  t('common.error'),
                  t('healthConsultation.history.deleteError')
                );
              }
            },
          },
        ]
      );
    },
    [t]
  );

  const handleArchiveConversation = useCallback(
    async (conversation: HealthConversation) => {
      try {
        const newArchivedStatus = !conversation.isArchived;
        await updateHealthConversation(conversation.id, {
          isArchived: newArchivedStatus,
        });

        // Update local state
        setConversations((prev) =>
          prev.map((c) =>
            c.id === conversation.id
              ? { ...c, isArchived: newArchivedStatus }
              : c
          )
        );
      } catch (error) {
        console.error('Error archiving/unarchiving conversation:', error);
        Alert.alert(
          t('common.error'),
          t('healthConsultation.history.archiveError')
        );
      }
    },
    [t]
  );

  const renderConversationItem = ({
    item,
    index,
  }: {
    item: HealthConversation;
    index: number;
  }) => {
    return (
      <ConversationCard
        item={item}
        index={index}
        onPress={handleConversationPress}
        onDelete={handleDeleteConversation}
        onArchive={handleArchiveConversation}
        t={t}
        messageCounts={messageCounts}
        showContextMenu={true}
      />
    );
  };

  const renderEmptyState = () => (
    <Animated.View entering={FadeInDown} style={styles.emptyState}>
      <Ionicons name="chatbubbles-outline" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>
        {searchQuery
          ? t('healthConsultation.history.noSearchResults')
          : t('healthConsultation.history.noConversations')}
      </Text>
      <Text style={styles.emptySubtitle}>
        {searchQuery
          ? t('healthConsultation.history.tryDifferentSearch')
          : t('healthConsultation.history.startFirstConsultation')}
      </Text>
    </Animated.View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: t('healthConsultation.history.title'),
          headerRight: () => (
            <TouchableOpacity
              onPress={() => setShowFiltersModal(true)}
              style={styles.headerButton}
            >
              <Ionicons name="options-outline" size={24} color="#2196F3" />
            </TouchableOpacity>
          ),
        }}
      />

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons
          name="search"
          size={20}
          color="#666"
          style={styles.searchIcon}
        />
        <TextInput
          style={styles.searchInput}
          placeholder={t('healthConsultation.history.searchPlaceholder')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          onFocus={() => setShowSearchSuggestions(true)}
          onBlur={() => setTimeout(() => setShowSearchSuggestions(false), 200)}
          placeholderTextColor="#999"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity
            onPress={() => setSearchQuery('')}
            style={styles.clearButton}
          >
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      {/* Search Suggestions */}
      {showSearchSuggestions && searchHistory.length > 0 && (
        <Animated.View
          entering={FadeInDown}
          style={styles.suggestionsContainer}
        >
          <Text style={styles.suggestionsTitle}>
            {t('healthConsultation.history.recentSearches')}
          </Text>
          {searchHistory.map((historyItem, index) => (
            <TouchableOpacity
              key={index}
              style={styles.suggestionItem}
              onPress={() => {
                setSearchQuery(historyItem);
                setShowSearchSuggestions(false);
              }}
            >
              <Ionicons name="time-outline" size={16} color="#666" />
              <Text style={styles.suggestionText}>{historyItem}</Text>
              <TouchableOpacity
                onPress={() => {
                  setSearchHistory((prev) =>
                    prev.filter((_, i) => i !== index)
                  );
                }}
                style={styles.removeSuggestion}
              >
                <Ionicons name="close" size={14} color="#999" />
              </TouchableOpacity>
            </TouchableOpacity>
          ))}
        </Animated.View>
      )}

      {/* Active Filters Indicator */}
      {(filterOptions.filterBy !== 'all' ||
        filterOptions.sortBy !== 'date' ||
        filterOptions.dateRange.startDate ||
        filterOptions.dateRange.endDate) && (
        <Animated.View
          entering={FadeInDown}
          style={styles.activeFiltersContainer}
        >
          <View style={styles.activeFiltersContent}>
            <Ionicons name="funnel" size={16} color="#2196F3" />
            <Text style={styles.activeFiltersText}>
              {t('healthConsultation.filters.title')}
            </Text>
            <TouchableOpacity
              onPress={() => {
                setFilterOptions({
                  sortBy: 'date',
                  filterBy: 'all',
                  dateRange: { startDate: null, endDate: null },
                  messageCountRange: { min: null, max: null },
                });
              }}
              style={styles.clearFiltersButton}
            >
              <Ionicons name="close-circle" size={16} color="#666" />
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}

      {/* Conversations List */}
      <FlatList
        data={filteredAndSortedConversations}
        renderItem={renderConversationItem}
        keyExtractor={(item) => item.id}
        style={styles.flatList}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Filters Modal */}
      <ConversationFiltersModal
        visible={showFiltersModal}
        onClose={() => setShowFiltersModal(false)}
        onApply={(newOptions) => {
          setFilterOptions(newOptions);
          setShowFiltersModal(false);
        }}
        currentOptions={filterOptions}
        t={t}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  headerButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  headerButtonText: {
    color: '#2196F3',
    fontSize: 16,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    padding: 4,
  },
  suggestionsContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: -8,
    borderRadius: 12,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  suggestionsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
    paddingHorizontal: 16,
    paddingBottom: 8,
    textTransform: 'uppercase',
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  suggestionText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  removeSuggestion: {
    padding: 4,
  },
  selectionActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectionCount: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  selectionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
  },
  deleteButton: {
    backgroundColor: '#f44336',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  flatList: {
    flex: 1,
  },
  listContent: {
    padding: 16,
    paddingTop: 0,
    gap: 12,
  },

  moreButton: {
    padding: 8,
    marginLeft: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  // Conversation item styles
  conversationContainer: {
    marginBottom: 12,
  },
  activeFiltersContainer: {
    marginHorizontal: 16,
    marginBottom: 12,
  },
  activeFiltersContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f8ff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e3f2fd',
  },
  activeFiltersText: {
    fontSize: 12,
    color: '#2196F3',
    fontWeight: '500',
    marginLeft: 6,
    flex: 1,
  },
  clearFiltersButton: {
    padding: 4,
  },
});
