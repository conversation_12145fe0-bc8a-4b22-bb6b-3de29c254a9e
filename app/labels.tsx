import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  Modal,
  Alert,
  Animated,
} from 'react-native';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from './context/LocalizationContext';
import { useCustomLabels, CustomLabel } from './context/CustomLabelsContext';
import { localAdditivesService } from './utils/LocalAdditivesService';

// Predefined colors for labels
const LABEL_COLORS = [
  '#4CAF50', // Green
  '#2196F3', // Blue
  '#9C27B0', // Purple
  '#F44336', // Red
  '#FF9800', // Orange
  '#795548', // <PERSON>
  '#607D8B', // Blue Grey
  '#009688', // Teal
];

export default function LabelsScreen() {
  const { t } = useLocalization();
  const router = useRouter();
  const params = useLocalSearchParams();
  const additiveCode = params.code as string | undefined;

  const {
    labels,
    createLabel,
    updateLabel,
    deleteLabel,
    addLabelToAdditive,
    removeLabelFromAdditive,
    hasLabel,
    getLabelsForAdditive,
  } = useCustomLabels();

  const [modalVisible, setModalVisible] = useState(false);
  const [editingLabel, setEditingLabel] = useState<CustomLabel | null>(null);
  const [labelName, setLabelName] = useState('');
  const [selectedColor, setSelectedColor] = useState(LABEL_COLORS[0]);

  // Get additive info if code is provided
  const additiveInfo = additiveCode
    ? localAdditivesService.getAdditiveByCode(additiveCode)
    : null;

  // Get labels for this additive if code is provided
  const additiveLabels = additiveCode ? getLabelsForAdditive(additiveCode) : [];

  // Reset form when opening modal
  useEffect(() => {
    if (modalVisible) {
      if (editingLabel) {
        setLabelName(editingLabel.name);
        setSelectedColor(editingLabel.color);
      } else {
        setLabelName('');
        setSelectedColor(LABEL_COLORS[0]);
      }
    }
  }, [modalVisible, editingLabel]);

  // Handle saving a label (create or update)
  const handleSaveLabel = async () => {
    if (!labelName.trim()) {
      Alert.alert(t('common.error'), t('common.labelNameRequired'));
      return;
    }

    try {
      if (editingLabel) {
        await updateLabel(editingLabel.id, labelName, selectedColor);
      } else {
        await createLabel(labelName, selectedColor);
      }
      setModalVisible(false);
    } catch (error) {
      console.error('Error saving label:', error);
      Alert.alert(t('common.error'), t('common.errorSavingLabel'));
    }
  };

  // Handle deleting a label
  const handleDeleteLabel = async () => {
    if (!editingLabel) return;

    Alert.alert(t('common.deleteLabel'), t('common.confirmDelete'), [
      {
        text: t('common.cancel'),
        style: 'cancel',
      },
      {
        text: t('common.delete'),
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteLabel(editingLabel.id);
            setModalVisible(false);
          } catch (error) {
            console.error('Error deleting label:', error);
            Alert.alert(t('common.error'), t('common.errorDeletingLabel'));
          }
        },
      },
    ]);
  };

  // Open modal to add a new label
  const handleAddLabel = () => {
    setEditingLabel(null);
    setModalVisible(true);
  };

  // Open modal to edit an existing label
  const handleEditLabel = (label: CustomLabel) => {
    setEditingLabel(label);
    setModalVisible(true);
  };

  // Toggle a label for the current additive
  const toggleLabelForAdditive = async (labelId: string) => {
    if (!additiveCode) return;

    try {
      if (hasLabel(additiveCode, labelId)) {
        await removeLabelFromAdditive(additiveCode, labelId);
      } else {
        await addLabelToAdditive(additiveCode, labelId);
      }
    } catch (error) {
      console.error('Error toggling label for additive:', error);
      Alert.alert(t('common.error'), t('common.errorSavingLabel'));
    }
  };

  // Render each label item
  const renderLabelItem = useCallback(
    ({ item }: { item: CustomLabel }) => {
      // If we're in additive mode, show checkboxes
      if (additiveCode) {
        const isSelected = hasLabel(additiveCode, item.id);
        return (
          <TouchableOpacity
            style={styles.labelItem}
            onPress={() => toggleLabelForAdditive(item.id)}
          >
            <View style={styles.labelContent}>
              <View
                style={[styles.labelDot, { backgroundColor: item.color }]}
              />
              <Text style={styles.labelName}>{item.name}</Text>
            </View>
            <Ionicons
              name={
                isSelected ? 'checkmark-circle' : 'checkmark-circle-outline'
              }
              size={24}
              color={isSelected ? item.color : '#999'}
            />
          </TouchableOpacity>
        );
      }

      // Otherwise show edit button
      return (
        <TouchableOpacity
          style={styles.labelItem}
          onPress={() => handleEditLabel(item)}
        >
          <View style={styles.labelContent}>
            <View style={[styles.labelDot, { backgroundColor: item.color }]} />
            <Text style={styles.labelName}>{item.name}</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
      );
    },
    [additiveCode, hasLabel]
  );

  // Render color selection button
  const renderColorButton = (color: string) => (
    <TouchableOpacity
      key={color}
      style={[
        styles.colorButton,
        { backgroundColor: color },
        selectedColor === color && styles.selectedColorButton,
      ]}
      onPress={() => setSelectedColor(color)}
    />
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: t('settings.customLabels'),
          headerShown: true,
          headerLargeTitle: false,
          headerStyle: {
            backgroundColor: '#fff',
          },
          headerTitleStyle: {
            fontSize: 18,
            fontWeight: '600',
          },
          headerRight: () => (
            <TouchableOpacity
              style={styles.headerAddButton}
              onPress={handleAddLabel}
            >
              <Ionicons name="add" size={24} color="#2196F3" />
            </TouchableOpacity>
          ),
        }}
      />
      <StatusBar barStyle="dark-content" />

      <ScrollView
        style={styles.container}
        contentInsetAdjustmentBehavior="automatic"
        showsVerticalScrollIndicator={false}
      >
        {additiveCode && additiveInfo && (
          <View style={styles.additiveInfoContainer}>
            <View style={styles.additiveHeader}>
              <Text style={styles.additiveCode}>{additiveCode}</Text>
              <Text style={styles.additiveName}>
                {useLocalization().language === 'tr'
                  ? additiveInfo.name.TR
                  : additiveInfo.name.EN}
              </Text>
            </View>
            <Text style={styles.labelInstructions}>
              {t('common.selectLabelsForAdditive')}
            </Text>
          </View>
        )}

        {labels.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="pricetags-outline" size={64} color="#ccc" />
            <Text style={styles.emptyText}>{t('common.customLabels')}</Text>
            <Text style={styles.emptySubText}>{t('common.noResults')}</Text>
            <TouchableOpacity
              style={styles.addLabelButton}
              onPress={handleAddLabel}
            >
              <Text style={styles.addLabelButtonText}>
                {t('common.addLabel')}
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.list}>
            {labels.map((item) => renderLabelItem({ item }))}
          </View>
        )}

        {/* Label Edit Modal */}
        <Modal
          visible={modalVisible}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>
                {editingLabel ? t('common.editLabel') : t('common.addLabel')}
              </Text>

              <Text style={styles.inputLabel}>{t('common.labelName')}</Text>
              <TextInput
                style={styles.input}
                value={labelName}
                onChangeText={setLabelName}
                placeholder={t('common.labelName')}
                autoCapitalize="none"
              />

              <Text style={styles.inputLabel}>{t('common.labelColor')}</Text>
              <View style={styles.colorSelector}>
                {LABEL_COLORS.map(renderColorButton)}
              </View>

              <View style={styles.buttonRow}>
                {editingLabel && (
                  <TouchableOpacity
                    style={[styles.button, styles.deleteButton]}
                    onPress={handleDeleteLabel}
                  >
                    <Text style={styles.deleteButtonText}>
                      {t('common.delete')}
                    </Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={() => setModalVisible(false)}
                >
                  <Text style={styles.cancelButtonText}>
                    {t('common.cancel')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, styles.saveButton]}
                  onPress={handleSaveLabel}
                >
                  <Text style={styles.saveButtonText}>{t('common.save')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  headerAddButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  additiveInfoContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  additiveHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  additiveCode: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginRight: 8,
  },
  additiveName: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  labelInstructions: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },

  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },

  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
  },
  emptySubText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 30,
  },
  addLabelButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addLabelButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  list: {
    paddingBottom: 20,
  },
  labelItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  labelContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  labelDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  labelName: {
    fontSize: 16,
    color: '#333',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '85%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  colorSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  colorButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    margin: 6,
  },
  selectedColorButton: {
    borderWidth: 3,
    borderColor: '#333',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 10,
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: '600',
  },
  saveButton: {
    backgroundColor: '#2196F3',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  deleteButton: {
    backgroundColor: '#ffebee',
    marginRight: 'auto',
  },
  deleteButtonText: {
    color: '#F44336',
    fontWeight: '600',
  },
});
