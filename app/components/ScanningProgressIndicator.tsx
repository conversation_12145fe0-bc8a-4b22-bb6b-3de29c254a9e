import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated from 'react-native-reanimated';
import { MultiImageStep } from '../hooks/useMultiImageScan';
import { useLocalization } from '../context/LocalizationContext';

interface ScanningProgressIndicatorProps {
  steps: MultiImageStep[];
  currentStepNumber: number;
  totalSteps: number;
}

// Helper functions for step colors and icons
const getStepColor = (purpose: string): string => {
  switch (purpose) {
    case 'ingredients':
      return '#4CAF50';
    case 'nutrition':
      return '#FF9800';
    case 'general':
      return '#2196F3';
    default:
      return '#2196F3';
  }
};

const getStepIcon = (purpose: string): string => {
  switch (purpose) {
    case 'ingredients':
      return 'list';
    case 'nutrition':
      return 'nutrition';
    case 'general':
      return 'document-text';
    default:
      return 'document-text';
  }
};

// Helper function to get contextual tips based on step purpose
const getContextualTip = (purpose: string, t: any): string => {
  switch (purpose) {
    case 'ingredients':
      return t('scan.tip.ingredients');
    case 'nutrition':
      return t('scan.tip.nutrition');
    case 'general':
      return t('scan.tip.general');
    default:
      return t('scan.tip.general');
  }
};

export default function ScanningProgressIndicator({
  steps,
  currentStepNumber,
  totalSteps,
}: ScanningProgressIndicatorProps) {
  const { t } = useLocalization();

  const currentStep = steps.find((step) => step.step === currentStepNumber);
  const stepColor = currentStep ? getStepColor(currentStep.purpose) : '#2196F3';
  const stepIcon = currentStep
    ? getStepIcon(currentStep.purpose)
    : 'document-text';

  return (
    <View style={styles.container}>
      {/* Current Step Information */}
      {currentStep && (
        <View style={styles.currentStepContainer}>
          <View style={styles.currentStepHeader}>
            <View
              style={[styles.currentStepIcon, { backgroundColor: stepColor }]}
            >
              <Ionicons name={stepIcon as any} size={16} color="white" />
            </View>
            <View style={styles.currentStepInfo}>
              <Text style={styles.currentStepTitle}>{currentStep.title}</Text>
              <Text style={styles.currentStepNumber}>
                {t('scan.step')} {currentStep.step} {t('scan.of')} {totalSteps}
              </Text>
            </View>
          </View>

          {/* Step Description */}
          <Text style={styles.currentStepDescription}>
            {currentStep.description}
          </Text>

          {/* Contextual Tips */}
          <View style={styles.tipsContainer}>
            <Ionicons name="bulb-outline" size={14} color="#FFD700" />
            <Text style={styles.tipText}>
              {getContextualTip(currentStep.purpose, t)}
            </Text>
          </View>
        </View>
      )}

      {/* Step Progress Indicator */}
      <View style={styles.stepsContainer}>
        {steps.map((step, index) => {
          const isActive = step.step === currentStepNumber;
          const isCompleted = step.completed;
          const stepColor = getStepColor(step.purpose);
          const isLast = index === steps.length - 1;

          return (
            <React.Fragment key={step.step}>
              {/* Step Wrapper */}
              <View style={styles.stepWrapper}>
                {/* Step Indicator Circle */}
                <Animated.View
                  style={[
                    styles.stepIndicator,
                    {
                      backgroundColor: isCompleted
                        ? stepColor
                        : isActive
                        ? 'rgba(255, 255, 255, 0.9)'
                        : 'rgba(255, 255, 255, 0.3)',
                      borderColor: isActive ? stepColor : 'transparent',
                      borderWidth: isActive ? 2 : 0,
                    },
                  ]}
                >
                  {isCompleted ? (
                    <Ionicons name="checkmark" size={16} color="white" />
                  ) : (
                    <Ionicons
                      name={getStepIcon(step.purpose) as any}
                      size={14}
                      color={isActive ? stepColor : 'rgba(255, 255, 255, 0.6)'}
                    />
                  )}
                </Animated.View>

                {/* Step Label */}
                <Text
                  style={[
                    styles.stepLabel,
                    {
                      color:
                        isActive || isCompleted
                          ? 'white'
                          : 'rgba(255, 255, 255, 0.6)',
                      fontWeight: isActive ? '600' : '500',
                    },
                  ]}
                >
                  {step.title.split(' ')[0]} {/* First word only for space */}
                </Text>
              </View>

              {/* Connection Line between steps */}
              {!isLast && (
                <View style={styles.connectionLineWrapper}>
                  <View
                    style={[
                      styles.connectionLine,
                      {
                        backgroundColor: isCompleted
                          ? stepColor
                          : 'rgba(255, 255, 255, 0.3)',
                      },
                    ]}
                  />
                </View>
              )}
            </React.Fragment>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 16,
    marginHorizontal: 20,
  },
  currentStepContainer: {
    marginBottom: 20,
  },
  currentStepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  currentStepIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  currentStepInfo: {
    flex: 1,
  },
  currentStepTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 2,
  },
  currentStepNumber: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    fontWeight: '500',
  },
  currentStepDescription: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 8,
    marginLeft: 44, // Align with step info
  },
  tipsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.15)',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 8,
    marginLeft: 44, // Align with step info
  },
  tipText: {
    color: '#FFD700',
    fontSize: 11,
    fontWeight: '500',
    flex: 1,
    marginLeft: 6,
  },
  stepsContainer: {
    flexDirection: 'row',
    alignItems: 'center',

    minHeight: 60, // Ensure consistent height
  },
  stepWrapper: {
    alignItems: 'center',
    minWidth: 70, // Fixed width for each step
  },
  connectionLineWrapper: {
    flex: 1,
    height: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -24, // Align with step indicator center (32px height / 2 + 8px margin)
  },
  connectionLine: {
    height: 2,
    width: '100%',
    borderRadius: 1,
  },
  stepIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  stepLabel: {
    fontSize: 12,
    textAlign: 'center',
    maxWidth: 70,
  },
});
