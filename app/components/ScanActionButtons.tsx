import { TouchableOpacity, StyleSheet, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ScanActionButtonsProps {
  scanMode: 'barcode' | 'text';
  onTakePicture: () => void;
  onPickImage: () => void;
}

export default function ScanActionButtons({
  scanMode,
  onTakePicture,
  onPickImage,
}: ScanActionButtonsProps) {
  return (
    <View style={styles.actionButtons}>
      <TouchableOpacity
        style={[
          styles.actionButton,
          {
            backgroundColor: '#2196F3',
          },
        ]}
        onPress={onTakePicture}
      >
        <Ionicons name="camera" size={30} color="white" />
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.actionButton,
          {
            backgroundColor: '#2196F3',
          },
        ]}
        onPress={onPickImage}
      >
        <Ionicons name="images" size={30} color="white" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  actionButtons: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 110,
    gap: 20,
  },
  actionButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
