import { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { FoodScore } from '../utils/FoodScoreCalculator';
import { StoredImageInfo } from '../utils/ImageStorage';

interface Props {
  foundECodes: string[];
  expandedCodes: Set<string>;
  product: any;
  foodScore: FoodScore | null;
  images?: StoredImageInfo[];
  toggleExpand: (code: string) => void;
  closeModal: () => void;
  onImagePress?: (images: StoredImageInfo[], initialIndex: number) => void;
}

const ECodeModal = ({
  foundECodes,
  expandedCodes,
  product,
  foodScore,
  images,
}: Props) => {
  const router = useRouter();

  // Check if modal should be shown
  const shouldShow =
    foundECodes.length > 0 ||
    !!foodScore ||
    !!product ||
    (images && images.length > 0);

  useEffect(() => {
    if (shouldShow) {
      // Navigate to modal stack instead of showing bottom sheet
      router.push({
        pathname: '/modal-stack/ecode-modal' as any,
        params: {
          foundECodes: JSON.stringify(foundECodes),
          expandedCodes: JSON.stringify(Array.from(expandedCodes)),
          product: product ? JSON.stringify(product) : undefined,
          foodScore: foodScore ? JSON.stringify(foodScore) : undefined,
          images: images ? JSON.stringify(images) : undefined,
        },
      });
    }
  }, [
    shouldShow,
    foundECodes,
    expandedCodes,
    product,
    foodScore,
    images,
    router,
  ]);

  // Don't render anything - we're using navigation
  return null;
};

export default ECodeModal;
