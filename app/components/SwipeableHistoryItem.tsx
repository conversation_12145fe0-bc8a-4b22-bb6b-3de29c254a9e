import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  runOnJS,
  withSpring,
  withTiming,
  interpolate,
  interpolateColor,
} from 'react-native-reanimated';
import {
  PanGestureHandler,
  PanGestureHandlerGestureEvent,
} from 'react-native-gesture-handler';
import { useLocalization } from '../context/LocalizationContext';
import HistoryItem from './HistoryItem';
import { StoredImageInfo } from '../utils/ImageStorage';
import { FoodScore } from '../utils/FoodScoreCalculator';
import { AIFoodAnalysis } from '../services/AIFoodAnalyzer';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const DELETE_THRESHOLD = SCREEN_WIDTH * 0.25; // 25% of screen width
const SWIPE_VELOCITY_THRESHOLD = 600; // Velocity threshold
const MIN_SWIPE_DISTANCE = 20; // Minimum distance to consider as swipe
const SWIPE_ANGLE_THRESHOLD = 30; // Maximum angle in degrees for horizontal swipe
const EDGE_ZONE_WIDTH = 60; // Width of the right edge zone for easier swipe initiation

type HistoryEntry = {
  id: string;
  timestamp: number;
  codes: string[];
  productName?: string | null;
  images?: StoredImageInfo[];
  foodScore?: FoodScore | null;
  aiAnalysis?: AIFoodAnalysis | null;
  nutritionData?: any;
};

type SwipeableHistoryItemProps = {
  item: HistoryEntry;
  onPress: (item: HistoryEntry) => void;
  onImagePress: (images: StoredImageInfo[], initialIndex: number) => void;
  onDelete: (item: HistoryEntry) => void;
  isFirstItem?: boolean;
};

const SwipeableHistoryItem: React.FC<SwipeableHistoryItemProps> = ({
  item,
  onPress,
  onImagePress,
  onDelete,
  isFirstItem = false,
}) => {
  const { t } = useLocalization();
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);
  const scale = useSharedValue(1);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = () => {
    Alert.alert(
      t('history.deleteConfirmTitle'),
      t('history.deleteConfirmMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
          onPress: () => {
            // Reset position
            translateX.value = withSpring(0);
          },
        },
        {
          text: t('history.deleteItem'),
          style: 'destructive',
          onPress: () => {
            setIsDeleting(true);
            // Animate out
            opacity.value = withTiming(0, { duration: 300 });
            scale.value = withTiming(0.8, { duration: 300 });
            translateX.value = withTiming(
              -SCREEN_WIDTH,
              { duration: 300 },
              () => {
                runOnJS(onDelete)(item);
              }
            );
          },
        },
      ]
    );
  };

  const gestureHandler = useAnimatedGestureHandler<
    PanGestureHandlerGestureEvent,
    {
      startX: number;
      startTouchX: number;
      isSwipeGesture: boolean;
      hasDecided: boolean;
      isInEdgeZone: boolean;
    }
  >({
    onStart: (event, context) => {
      context.startX = translateX.value;
      context.startTouchX = event.absoluteX;
      context.isSwipeGesture = false;
      context.hasDecided = false;

      // Check if gesture started in the right edge zone (easier swipe area)
      context.isInEdgeZone = event.absoluteX > SCREEN_WIDTH - EDGE_ZONE_WIDTH;
    },
    onActive: (event, context) => {
      // Only make decision once we have enough movement
      if (
        !context.hasDecided &&
        (Math.abs(event.translationX) > 10 || Math.abs(event.translationY) > 10)
      ) {
        const absX = Math.abs(event.translationX);
        const absY = Math.abs(event.translationY);

        // Calculate swipe angle in degrees
        const angle = Math.atan2(absY, absX) * (180 / Math.PI);

        // Determine if this should be a swipe gesture based on:
        // 1. Angle is within horizontal threshold (< 30 degrees)
        // 2. Minimum distance is met
        // 3. Movement is primarily leftward (for delete)
        // 4. OR started in edge zone (more lenient)
        const isHorizontalAngle = angle < SWIPE_ANGLE_THRESHOLD;
        const hasMinDistance = absX > MIN_SWIPE_DISTANCE;
        const isLeftward = event.translationX < 0;

        if (isHorizontalAngle && hasMinDistance && isLeftward) {
          context.isSwipeGesture = true;
        } else if (context.isInEdgeZone && isLeftward && absX > absY) {
          // More lenient for edge zone - just needs to be more horizontal than vertical
          context.isSwipeGesture = true;
        }

        context.hasDecided = true;
      }

      // Only process swipe if it's determined to be one
      if (context.isSwipeGesture) {
        // Only allow left swipe (negative translation)
        const newTranslateX = context.startX + event.translationX;
        translateX.value = Math.min(0, newTranslateX);
      }
    },
    onEnd: (event, context) => {
      // Only handle delete if this was a swipe gesture
      if (context.isSwipeGesture) {
        const distance = Math.abs(translateX.value);
        const velocity = Math.abs(event.velocityX);

        const shouldDelete =
          distance > DELETE_THRESHOLD ||
          (velocity > SWIPE_VELOCITY_THRESHOLD && event.translationX < 0);

        if (shouldDelete && translateX.value < 0) {
          // Show delete confirmation
          runOnJS(handleDelete)();
        } else {
          // Reset position
          translateX.value = withSpring(0);
        }
      } else {
        // Reset position if it wasn't a swipe gesture
        translateX.value = withSpring(0);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }, { scale: scale.value }],
      opacity: opacity.value,
    };
  });

  const deleteButtonStyle = useAnimatedStyle(() => {
    const progress = Math.min(Math.abs(translateX.value) / DELETE_THRESHOLD, 1);

    return {
      opacity: interpolate(progress, [0, 0.5, 1], [0, 0.8, 1]),
      transform: [
        {
          scale: interpolate(progress, [0, 0.5, 1], [0.8, 0.9, 1]),
        },
      ],
    };
  });

  const deleteBackgroundStyle = useAnimatedStyle(() => {
    const progress = Math.min(Math.abs(translateX.value) / DELETE_THRESHOLD, 1);

    return {
      backgroundColor: interpolateColor(
        progress,
        [0, 0.5, 1],
        ['#FF6B6B', '#FF5252', '#F44336']
      ),
    };
  });

  if (isDeleting) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Delete Background */}
      <Animated.View style={[styles.deleteBackground, deleteBackgroundStyle]}>
        <Animated.View style={[styles.deleteButton, deleteButtonStyle]}>
          <Ionicons name="trash" size={24} color="white" />
          <Text style={styles.deleteText}>{t('history.deleteItem')}</Text>
        </Animated.View>
      </Animated.View>

      {/* Swipeable Content */}
      <PanGestureHandler
        onGestureEvent={gestureHandler}
        activeOffsetX={[-10, 10]} // Lower threshold for activation
        shouldCancelWhenOutside={true}
        minPointers={1}
        maxPointers={1}
      >
        <Animated.View style={animatedStyle}>
          <HistoryItem
            item={item}
            onPress={onPress}
            onImagePress={onImagePress}
            isFirstItem={isFirstItem}
          />
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  deleteBackground: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingRight: 20,
    borderRadius: 16,
    marginBottom: 12,
    marginHorizontal: 16,
  },
  deleteButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  deleteText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
});

export default SwipeableHistoryItem;
