import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withRepeat,
  withSequence,
  interpolate,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../context/LocalizationContext';
import { MultiImageStep } from '../hooks/useMultiImageScan';

interface MultiImageCameraOverlayProps {
  currentStep: MultiImageStep;
  totalSteps: number;
  completedCount: number;
  isProcessing: boolean;
  showGuidance: boolean;
  isCapturing?: boolean;
  captureSuccess?: boolean;
}

const { width } = Dimensions.get('window');
const SCAN_AREA_SIZE = Math.min(width * 0.8, 300);

// Worklet functions for animated styles
const getStepColor = (purpose: string): string => {
  'worklet';
  switch (purpose) {
    case 'ingredients':
      return '#4CAF50'; // Green for ingredients
    case 'nutrition':
      return '#FF9800'; // Orange for nutrition
    case 'general':
      return '#2196F3'; // Blue for general
    default:
      return '#2196F3';
  }
};

const getStepIcon = (purpose: string): string => {
  'worklet';
  switch (purpose) {
    case 'ingredients':
      return 'list-outline';
    case 'nutrition':
      return 'nutrition-outline';
    case 'general':
      return 'document-text-outline';
    default:
      return 'document-text-outline';
  }
};

export default function MultiImageCameraOverlay({
  currentStep,
  totalSteps,
  completedCount,
  isProcessing,
  showGuidance,
  isCapturing = false,
  captureSuccess = false,
}: MultiImageCameraOverlayProps) {
  const { t } = useLocalization();

  // Animation values
  const pulseAnimation = useSharedValue(0);
  const scanLineAnimation = useSharedValue(0);
  const guidanceAnimation = useSharedValue(0);
  const progressAnimation = useSharedValue(0);

  useEffect(() => {
    if (!isProcessing) {
      // Pulse animation for corners
      pulseAnimation.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 1500 }),
          withTiming(0, { duration: 1500 })
        ),
        -1,
        true
      );

      // Scanning line animation - smooth up and down movement
      scanLineAnimation.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 2000 }),
          withTiming(0, { duration: 2000 })
        ),
        -1,
        false
      );
    } else {
      pulseAnimation.value = withTiming(0, { duration: 300 });
      scanLineAnimation.value = withTiming(0, { duration: 300 });
    }
  }, [isProcessing]);

  useEffect(() => {
    if (showGuidance) {
      guidanceAnimation.value = withTiming(1, { duration: 500 });
    } else {
      guidanceAnimation.value = withTiming(0, { duration: 300 });
    }
  }, [showGuidance]);

  useEffect(() => {
    progressAnimation.value = withTiming(completedCount / totalSteps, {
      duration: 500,
    });
  }, [completedCount, totalSteps]);

  const cornerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(pulseAnimation.value, [0, 1], [0.7, 1]),
    borderColor: getStepColor(currentStep.purpose),
  }));

  const scanLineAnimatedStyle = useAnimatedStyle(() => ({
    backgroundColor: getStepColor(currentStep.purpose),
    transform: [
      {
        translateY: interpolate(
          scanLineAnimation.value,
          [0, 1],
          [-SCAN_AREA_SIZE * 0.35, SCAN_AREA_SIZE * 0.35]
        ),
      },
    ],
  }));

  const guidanceAnimatedStyle = useAnimatedStyle(() => ({
    opacity: guidanceAnimation.value,
    transform: [
      {
        translateY: interpolate(guidanceAnimation.value, [0, 1], [10, 0]),
      },
    ],
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${interpolate(progressAnimation.value, [0, 1], [0, 100])}%`,
  }));

  return (
    <View style={styles.overlay}>
      {/* Minimal progress indicator at top */}
      {(completedCount > 0 || currentStep.step > 1) && (
        <View style={styles.minimalProgressContainer}>
          <View style={styles.progressDots}>
            {Array.from({ length: totalSteps }, (_, index) => (
              <View
                key={index}
                style={[
                  styles.progressDot,
                  {
                    backgroundColor:
                      index < completedCount
                        ? getStepColor(currentStep.purpose)
                        : 'rgba(255, 255, 255, 0.3)',
                  },
                ]}
              />
            ))}
          </View>
        </View>
      )}

      {/* Corner indicators */}
      <Animated.View
        style={[styles.corner, styles.cornerTL, cornerAnimatedStyle]}
      />
      <Animated.View
        style={[styles.corner, styles.cornerTR, cornerAnimatedStyle]}
      />
      <Animated.View
        style={[styles.corner, styles.cornerBL, cornerAnimatedStyle]}
      />
      <Animated.View
        style={[styles.corner, styles.cornerBR, cornerAnimatedStyle]}
      />

      {/* Scanning line */}
      {!isProcessing && (
        <Animated.View style={[styles.scanLine, scanLineAnimatedStyle]} />
      )}

      {/* Minimal center guidance - camera icon removed */}

      {/* Capture feedback overlay */}
      {(isCapturing || captureSuccess) && (
        <View style={styles.feedbackOverlay}>
          <View style={styles.feedbackContainer}>
            {isCapturing ? (
              <>
                <Ionicons name="camera" size={32} color="white" />
                <Text style={styles.feedbackText}>{t('scan.capturing')}</Text>
              </>
            ) : captureSuccess ? (
              <>
                <Ionicons name="checkmark-circle" size={32} color="#4CAF50" />
                <Text style={[styles.feedbackText, { color: '#4CAF50' }]}>
                  {t('scan.captureSuccess')}
                </Text>
              </>
            ) : null}
          </View>
        </View>
      )}

      {/* Processing indicator - only show during final analysis */}
      {isProcessing && (
        <View style={styles.processingOverlay}>
          <View style={styles.processingContainer}>
            <Text style={styles.processingText}>{t('scan.aiProcessing')}</Text>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  progressContainer: {
    position: 'absolute',
    top: 60,
    left: 20,
    right: 20,
    alignItems: 'center',
  },
  progressBackground: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  stepInfo: {
    position: 'absolute',
    top: 120,
    left: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    padding: 16,
  },
  stepIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepTextContainer: {
    flex: 1,
  },
  stepTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  stepDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  minimalProgressContainer: {
    position: 'absolute',
    top: 20,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 10,
    pointerEvents: 'auto',
  },
  progressDots: {
    flexDirection: 'row',
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  minimalCenterGuidance: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    alignItems: 'center',
    justifyContent: 'center',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderWidth: 3,
  },
  cornerTL: {
    top: '50%',
    left: '50%',
    marginTop: -SCAN_AREA_SIZE / 2,
    marginLeft: -SCAN_AREA_SIZE / 2,
    borderTopWidth: 3,
    borderLeftWidth: 3,
    borderTopLeftRadius: 8,
  },
  cornerTR: {
    top: '50%',
    right: '50%',
    marginTop: -SCAN_AREA_SIZE / 2,
    marginRight: -SCAN_AREA_SIZE / 2,
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderTopRightRadius: 8,
  },
  cornerBL: {
    bottom: '50%',
    left: '50%',
    marginBottom: -SCAN_AREA_SIZE / 2,
    marginLeft: -SCAN_AREA_SIZE / 2,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderBottomLeftRadius: 8,
  },
  cornerBR: {
    bottom: '50%',
    right: '50%',
    marginBottom: -SCAN_AREA_SIZE / 2,
    marginRight: -SCAN_AREA_SIZE / 2,
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderBottomRightRadius: 8,
  },
  scanLine: {
    position: 'absolute',
    width: SCAN_AREA_SIZE * 0.8,
    height: 2,
    borderRadius: 1,
  },
  centerGuidance: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    padding: 12,
  },
  centerGuidanceText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  guidanceTip: {
    position: 'absolute',
    bottom: 200,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 12,
    padding: 16,
  },
  guidanceTipText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  feedbackOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 15,
  },
  feedbackContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    minWidth: 120,
  },
  feedbackText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 8,
  },
  processingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 12,
    padding: 20,
  },
  processingText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
