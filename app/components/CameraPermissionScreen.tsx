import {
  View,
  Text,
  TouchableOpacity,
  Linking,
  StyleSheet,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { useLocalization } from '../context/LocalizationContext';
import Camera from '../../assets/images/Camera';
import CameraLock from '../../assets/images/CameraLock';

interface Props {
  cameraPermission: any;
  requestCameraPermission: () => void;
}

const { width } = Dimensions.get('window');

const CameraPermissionScreen = ({
  cameraPermission,
  requestCameraPermission,
}: Props) => {
  const { t } = useLocalization();

  if (!cameraPermission) {
    return <SafeAreaView style={styles.loadingContainer} />;
  }

  if (cameraPermission.status === 'denied' && !cameraPermission.canAskAgain) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.contentContainer}>
          <View style={styles.iconContainer}>
            <CameraLock />
          </View>

          <Text style={styles.title}>{t('scan.cameraPermissionRequired')}</Text>

          <Text style={styles.message}>
            {t('scan.cameraPermissionDeniedSettings')}
          </Text>

          <TouchableOpacity
            style={styles.primaryButton}
            onPress={() => Linking.openSettings()}
            activeOpacity={0.8}
          >
            <Text style={styles.primaryButtonText}>
              {t('scan.goToSettings')}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (cameraPermission.status === 'undetermined') {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.contentContainer}>
          <View style={styles.iconContainer}>
            <Camera />
          </View>

          <Text style={styles.title}>{t('scan.cameraPermissionRequired')}</Text>

          <Text style={styles.message}>
            {t('scan.cameraPermissionExplanation')}
          </Text>

          <TouchableOpacity
            style={styles.primaryButton}
            onPress={requestCameraPermission}
            activeOpacity={0.8}
          >
            <Text style={styles.primaryButtonText}>{t('scan.continue')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return null;
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    backgroundColor: '#121212',
  },
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    color: '#ffffff',
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 17,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 36,
    maxWidth: width * 0.85,
  },
  primaryButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    width: '100%',
    maxWidth: 320,
    alignItems: 'center',
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
    marginBottom: 16,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 17,
    fontWeight: '600',
  },
  secondaryButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    width: '100%',
    maxWidth: 320,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default CameraPermissionScreen;
