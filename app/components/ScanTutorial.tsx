import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Modal,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { useLocalization } from '../context/LocalizationContext';

interface ScanTutorialProps {
  visible: boolean;
  onClose: () => void;
}

interface TutorialStep {
  icon: string;
  title: string;
  description: string;
  tip: string;
}

const { height } = Dimensions.get('window');

export default function ScanTutorial({ visible, onClose }: ScanTutorialProps) {
  const { t } = useLocalization();
  const [currentStep, setCurrentStep] = useState(0);

  // Animation values
  const fadeAnimation = useSharedValue(0);
  const slideAnimation = useSharedValue(0);
  const stepAnimation = useSharedValue(0);

  const tutorialSteps: TutorialStep[] = [
    {
      icon: 'document-text-outline',
      title: t('scan.frameIngredients'),
      description: t('scan.ingredientTip'),
      tip: 'Position the ingredients list within the scanning frame for best results.',
    },
    {
      icon: 'hand-left-outline',
      title: t('scan.holdSteady'),
      description: t('scan.positioningTip'),
      tip: 'Keep your phone steady and avoid shaking while scanning.',
    },
    {
      icon: 'sunny-outline',
      title: t('scan.goodLighting'),
      description: t('scan.lightingTip'),
      tip: 'Good lighting helps the AI read text more accurately.',
    },
    {
      icon: 'eye-outline',
      title: t('scan.clearText'),
      description: t('scan.focusTip'),
      tip: 'Make sure the text is in focus and clearly visible.',
    },
  ];

  useEffect(() => {
    if (visible) {
      fadeAnimation.value = withTiming(1, { duration: 300 });
      slideAnimation.value = withTiming(1, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      });
      stepAnimation.value = withTiming(1, { duration: 500 });
    } else {
      fadeAnimation.value = withTiming(0, { duration: 200 });
      slideAnimation.value = withTiming(0, { duration: 200 });
      stepAnimation.value = 0;
    }
  }, [visible]);

  useEffect(() => {
    if (visible) {
      stepAnimation.value = 0;
      stepAnimation.value = withTiming(1, { duration: 400 });
    }
  }, [currentStep, visible]);

  const overlayAnimatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: interpolate(slideAnimation.value, [0, 1], [100, 0]),
      },
    ],
    opacity: fadeAnimation.value,
  }));

  const stepAnimatedStyle = useAnimatedStyle(() => ({
    opacity: stepAnimation.value,
    transform: [
      {
        translateX: interpolate(stepAnimation.value, [0, 1], [30, 0]),
      },
    ],
  }));

  const handleNext = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onClose();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    onClose();
  };

  if (!visible) {
    return null;
  }

  const currentTutorialStep = tutorialSteps[currentStep];

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <Animated.View style={[styles.overlay, overlayAnimatedStyle]}>
        <BlurView
          intensity={30}
          style={StyleSheet.absoluteFillObject}
          tint="dark"
        />

        <Animated.View style={[styles.container, contentAnimatedStyle]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>{t('scan.bestPractices')}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            {tutorialSteps.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.progressDot,
                  index === currentStep && styles.progressDotActive,
                  index < currentStep && styles.progressDotCompleted,
                ]}
              />
            ))}
          </View>

          {/* Step Content */}
          <Animated.View style={[styles.stepContent, stepAnimatedStyle]}>
            <View style={styles.iconContainer}>
              <Ionicons
                name={currentTutorialStep.icon as any}
                size={48}
                color="#2196F3"
              />
            </View>

            <Text style={styles.stepTitle}>{currentTutorialStep.title}</Text>
            <Text style={styles.stepDescription}>
              {currentTutorialStep.description}
            </Text>
            <Text style={styles.stepTip}>{currentTutorialStep.tip}</Text>

            {/* Visual Example */}
            <View style={styles.exampleContainer}>
              <View style={styles.phoneFrame}>
                <View style={styles.phoneScreen}>
                  <View style={styles.scanFrame}>
                    <Text style={styles.exampleText}>
                      {currentStep === 0 && t('scan.exampleIngredients')}
                      {currentStep === 1 && t('scan.exampleHoldSteady')}
                      {currentStep === 2 && t('scan.exampleGoodLighting')}
                      {currentStep === 3 && t('scan.exampleClearText')}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </Animated.View>

          {/* Navigation */}
          <View style={styles.navigation}>
            <View style={styles.stepCounter}>
              <Text style={styles.stepCounterText}>
                {t('scan.tipCounter', {
                  current: currentStep + 1,
                  total: tutorialSteps.length,
                })}
              </Text>
            </View>

            <View style={styles.navigationButtons}>
              {currentStep > 0 && (
                <TouchableOpacity
                  style={[styles.navButton, styles.secondaryButton]}
                  onPress={handlePrevious}
                >
                  <Ionicons name="chevron-back" size={20} color="#666" />
                  <Text style={styles.secondaryButtonText}>
                    {t('scan.previousTip')}
                  </Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={[styles.navButton, styles.skipButton]}
                onPress={handleSkip}
              >
                <Text style={styles.skipButtonText}>
                  {t('scan.skipTutorial')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.navButton, styles.primaryButton]}
                onPress={handleNext}
              >
                <Text style={styles.primaryButtonText}>
                  {currentStep === tutorialSteps.length - 1
                    ? t('scan.gotIt')
                    : t('scan.nextTip')}
                </Text>
                {currentStep < tutorialSteps.length - 1 && (
                  <Ionicons name="chevron-forward" size={20} color="white" />
                )}
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 20,
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    maxHeight: height * 0.8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#e0e0e0',
  },
  progressDotActive: {
    backgroundColor: '#2196F3',
    width: 24,
  },
  progressDotCompleted: {
    backgroundColor: '#4CAF50',
  },
  stepContent: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 22,
  },
  stepTip: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    fontStyle: 'italic',
    marginBottom: 20,
  },
  exampleContainer: {
    alignItems: 'center',
  },
  phoneFrame: {
    width: 120,
    height: 200,
    backgroundColor: '#333',
    borderRadius: 20,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  phoneScreen: {
    flex: 1,
    width: '100%',
    backgroundColor: '#000',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 80,
    height: 60,
    borderWidth: 2,
    borderColor: '#2196F3',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  exampleText: {
    color: 'white',
    fontSize: 8,
    textAlign: 'center',
  },
  navigation: {
    gap: 16,
  },
  stepCounter: {
    alignItems: 'center',
  },
  stepCounterText: {
    fontSize: 14,
    color: '#888',
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 8,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 4,
  },
  primaryButton: {
    backgroundColor: '#2196F3',
    flex: 1,
    justifyContent: 'center',
  },
  secondaryButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  skipButton: {
    backgroundColor: 'transparent',
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '600',
  },
  skipButtonText: {
    color: '#888',
    fontSize: 14,
  },
});
