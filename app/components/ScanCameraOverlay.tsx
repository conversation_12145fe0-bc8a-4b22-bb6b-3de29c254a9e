import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withRepeat,
  withSequence,
  interpolate,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../context/LocalizationContext';

interface ScanCameraOverlayProps {
  scanMode: 'barcode' | 'text';
  isProcessing: boolean;
  showGuidance: boolean;
}

const { width } = Dimensions.get('window');
const SCAN_AREA_SIZE = Math.min(width * 0.8, 300);

export default function ScanCameraOverlay({
  scanMode,
  isProcessing,
  showGuidance,
}: ScanCameraOverlayProps) {
  const { t } = useLocalization();

  // Animation values
  const pulseAnimation = useSharedValue(0);
  const scanLineAnimation = useSharedValue(0);
  const guidanceAnimation = useSharedValue(0);

  useEffect(() => {
    if (scanMode === 'text' && !isProcessing) {
      // Pulse animation for corners
      pulseAnimation.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 1500 }),
          withTiming(0, { duration: 1500 })
        ),
        -1,
        true
      );

      // Scanning line animation for text mode
      scanLineAnimation.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 2000 }),
          withTiming(0, { duration: 100 })
        ),
        -1,
        false
      );
    } else {
      pulseAnimation.value = withTiming(0, { duration: 300 });
      scanLineAnimation.value = withTiming(0, { duration: 300 });
    }
  }, [scanMode, isProcessing]);

  useEffect(() => {
    if (showGuidance && scanMode === 'text') {
      guidanceAnimation.value = withTiming(1, { duration: 500 });
    } else {
      guidanceAnimation.value = withTiming(0, { duration: 300 });
    }
  }, [showGuidance, scanMode]);

  const cornerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(pulseAnimation.value, [0, 1], [0.7, 1]),
    borderColor: scanMode === 'text' ? '#2196F3' : '#4CAF50',
  }));

  const scanLineAnimatedStyle = useAnimatedStyle(() => ({
    opacity: scanLineAnimation.value,
    transform: [
      {
        translateY: interpolate(
          scanLineAnimation.value,
          [0, 1],
          [-SCAN_AREA_SIZE * 0.35, SCAN_AREA_SIZE * 0.35]
        ),
      },
    ],
  }));

  const guidanceAnimatedStyle = useAnimatedStyle(() => ({
    opacity: guidanceAnimation.value,
    transform: [
      {
        translateY: interpolate(guidanceAnimation.value, [0, 1], [10, 0]),
      },
    ],
  }));

  return (
    <View style={styles.overlay}>
      {/* Corner indicators */}
      <Animated.View
        style={[styles.corner, styles.cornerTL, cornerAnimatedStyle]}
      />
      <Animated.View
        style={[styles.corner, styles.cornerTR, cornerAnimatedStyle]}
      />
      <Animated.View
        style={[styles.corner, styles.cornerBL, cornerAnimatedStyle]}
      />
      <Animated.View
        style={[styles.corner, styles.cornerBR, cornerAnimatedStyle]}
      />

      {/* Scanning line for text mode */}
      {scanMode === 'text' && !isProcessing && (
        <Animated.View style={[styles.scanLine, scanLineAnimatedStyle]} />
      )}

      {/* Real-time guidance hints */}
      {showGuidance && scanMode === 'text' && (
        <Animated.View style={[styles.guidanceHints, guidanceAnimatedStyle]}>
          <View style={styles.hintItem}>
            <Ionicons name="crop-outline" size={16} color="#2196F3" />
            <Text style={styles.hintText}>{t('scan.frameIngredients')}</Text>
          </View>
        </Animated.View>
      )}

      {/* Processing indicator */}
      {isProcessing && (
        <View style={styles.processingIndicator}>
          <View style={styles.processingDot} />
          <Text style={styles.processingText}>{t('scan.aiProcessing')}</Text>
        </View>
      )}

      {/* Mode-specific guidance */}
      {!isProcessing && (
        <View style={styles.modeGuidance}>
          <View style={styles.textGuidance}>
            <Ionicons name="document-text-outline" size={20} color="#2196F3" />
            <Text style={styles.modeText}>{t('scan.scanText')}</Text>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  corner: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderWidth: 4,
  },
  cornerTL: {
    top: -2,
    left: -2,
    borderBottomWidth: 0,
    borderRightWidth: 0,
    borderTopLeftRadius: 8,
  },
  cornerTR: {
    top: -2,
    right: -2,
    borderBottomWidth: 0,
    borderLeftWidth: 0,
    borderTopRightRadius: 8,
  },
  cornerBL: {
    bottom: -2,
    left: -2,
    borderTopWidth: 0,
    borderRightWidth: 0,
    borderBottomLeftRadius: 8,
  },
  cornerBR: {
    bottom: -2,
    right: -2,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderBottomRightRadius: 8,
  },
  scanLine: {
    position: 'absolute',
    left: 10,
    right: 10,
    height: 2,
    backgroundColor: '#2196F3',
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 4,
  },
  guidanceHints: {
    position: 'absolute',
    bottom: -60,
    left: -20,
    right: -20,
    alignItems: 'center',
  },
  hintItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  hintText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  processingIndicator: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 8,
  },
  processingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#2196F3',
  },
  processingText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  modeGuidance: {
    position: 'absolute',
    top: -50,
    alignItems: 'center',
  },
  barcodeGuidance: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  textGuidance: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(33, 150, 243, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  modeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
});
