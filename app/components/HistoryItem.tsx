import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { useLocalization } from '../context/LocalizationContext';
import { StoredImageInfo, loadImageFromStorage } from '../utils/ImageStorage';
import { FoodScore } from '../utils/FoodScoreCalculator';
import FoodScoreDisplay from './FoodScoreDisplay';

type HistoryEntry = {
  id: string;
  timestamp: number;
  codes: string[];
  productName?: string | null;
  images?: StoredImageInfo[];
  foodScore?: FoodScore | null;
  nutritionData?: any;
};

type HistoryItemProps = {
  item: HistoryEntry;
  onPress: (item: HistoryEntry) => void;
  onImagePress: (images: StoredImageInfo[], initialIndex: number) => void;
  isFirstItem?: boolean;
};

// Component for displaying the image thumbnail
const ImageThumbnail: React.FC<{
  imageInfo: StoredImageInfo;
  onImagePress: (images: StoredImageInfo[], initialIndex: number) => void;
  scanConfig: any;
  allImages: StoredImageInfo[];
  currentIndex: number;
}> = ({ imageInfo, onImagePress, scanConfig, allImages, currentIndex }) => {
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    loadImageFromStorage(imageInfo.filename)
      .then((uri) => {
        if (uri) {
          setImageUri(uri);
        } else {
          setError(true);
        }
      })
      .catch(() => {
        setError(true);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [imageInfo.filename]);

  const handlePress = () => {
    if (imageUri && !error) {
      onImagePress(allImages, currentIndex);
    }
  };

  return (
    <TouchableOpacity
      style={styles.imageThumbnail}
      onPress={handlePress}
      activeOpacity={0.8}
      disabled={loading || error}
    >
      {loading && (
        <View style={styles.imageLoading}>
          <ActivityIndicator size="small" color="#666" />
        </View>
      )}

      {error && (
        <View style={styles.imageError}>
          <Ionicons
            name={scanConfig.icon}
            size={24}
            color={scanConfig.gradientColors[0]}
          />
        </View>
      )}

      {!loading && !error && imageUri && (
        <>
          <Image
            source={{ uri: imageUri }}
            style={styles.thumbnailImage}
            resizeMode="cover"
          />
          <View style={styles.imageOverlay}>
            <View
              style={[
                styles.typeIndicator,
                { backgroundColor: scanConfig.gradientColors[0] },
              ]}
            >
              <Ionicons name={scanConfig.icon} size={12} color="white" />
            </View>
          </View>
        </>
      )}
    </TouchableOpacity>
  );
};

const HistoryItem: React.FC<HistoryItemProps> = ({
  item,
  onPress,
  onImagePress,
  isFirstItem = false,
}) => {
  const { t } = useLocalization();
  const dateString = new Date(item.timestamp).toLocaleString();
  const isProductScan = !!item.productName;
  const hasImage = item.images && item.images.length > 0;
  const primaryImage = hasImage ? item.images![0] : null;
  const isMultiImage = hasImage && item.images!.length > 1;

  // Animation values - Initialize with proper starting values
  const [isExpandedState, setIsExpandedState] = useState(
    isFirstItem && !!item.foodScore
  );
  const [contentHeight, setContentHeight] = useState(0);
  const animatedHeight = useSharedValue(
    isFirstItem && !!item.foodScore ? 1 : 0
  );
  const chevronRotation = useSharedValue(
    isFirstItem && !!item.foodScore ? 180 : 0
  );

  // Get scan type styling
  const getScanTypeConfig = () => {
    if (isProductScan) {
      return {
        icon: 'barcode-outline' as const,
        gradientColors: ['#007AFF', '#0056CC'],
        bgColor: '#E3F2FD',
      };
    } else {
      return {
        icon: 'document-text-outline' as const,
        gradientColors: ['#4CAF50', '#2E7D32'],
        bgColor: '#E8F5E8',
      };
    }
  };

  const scanConfig = getScanTypeConfig();

  // Toggle expansion animation
  const toggleExpansion = () => {
    const newExpandedState = !isExpandedState;

    // Update state first to prevent race conditions
    setIsExpandedState(newExpandedState);

    // Animate chevron rotation with consistent timing
    const targetRotation = newExpandedState ? 180 : 0;
    chevronRotation.value = withTiming(targetRotation, {
      duration: 250,
    });

    // Animate height with spring
    animatedHeight.value = withSpring(newExpandedState ? 1 : 0, {
      damping: 16,
      stiffness: 120,
      mass: 1,
    });
  };

  // Animated styles
  const animatedContainerStyle = useAnimatedStyle(() => {
    const height = interpolate(
      animatedHeight.value,
      [0, 1],
      [0, contentHeight || 120], // Use dynamic content height
      'clamp'
    );

    return {
      height,
      opacity: interpolate(
        animatedHeight.value,
        [0, 0.5, 1],
        [0, 0.5, 1],
        'clamp'
      ),
    };
  });

  const animatedChevronStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          rotate: `${chevronRotation.value}deg`,
        },
      ],
    };
  }, []);

  // Measure content height
  const onContentLayout = (event: any) => {
    const { height } = event.nativeEvent.layout;
    if (height > 0 && height !== contentHeight) {
      setContentHeight(height);
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#FFFFFF', '#F8F9FA']}
        style={styles.cardGradient}
      >
        <View style={styles.cardContent}>
          {/* Top section - Image, Date, Codes - Clickable */}
          <TouchableOpacity
            style={styles.topSection}
            onPress={() => onPress(item)}
            activeOpacity={0.7}
          >
            {/* Left side - Image */}
            {hasImage && primaryImage && (
              <View style={styles.imageContainer}>
                <ImageThumbnail
                  imageInfo={primaryImage}
                  onImagePress={onImagePress}
                  scanConfig={scanConfig}
                  allImages={item.images!}
                  currentIndex={0}
                />
                {/* Multi-image indicator */}
                {isMultiImage && (
                  <View style={styles.multiImageIndicator}>
                    <Ionicons name="images" size={12} color="white" />
                    <Text style={styles.multiImageCount}>
                      {item.images!.length}
                    </Text>
                  </View>
                )}
              </View>
            )}

            {/* Right side - Content */}
            <View
              style={[
                styles.textContent,
                !hasImage && styles.textContentFullWidth,
              ]}
            >
              {/* Header */}
              <View style={styles.header}>
                {!hasImage && (
                  <View
                    style={[
                      styles.iconContainer,
                      { backgroundColor: scanConfig.bgColor },
                    ]}
                  >
                    <Ionicons
                      name={scanConfig.icon}
                      size={20}
                      color={scanConfig.gradientColors[0]}
                    />
                  </View>
                )}
                <View style={styles.headerContent}>
                  <Text style={styles.timestamp}>{dateString}</Text>
                </View>
                <MaterialIcons name="chevron-right" size={24} color="#B0BEC5" />
              </View>

              {/* Title - only show if it's a product scan */}
              {isProductScan && (
                <Text style={styles.title} numberOfLines={2}>
                  {item.productName}
                </Text>
              )}

              {/* E-codes display */}
              <View style={styles.codesContainer}>
                <Text style={styles.codesLabel}>
                  {t('history.codesFound')}:
                </Text>
                {item.codes.length > 0 ? (
                  <View style={styles.codesWrapper}>
                    {item.codes.slice(0, 3).map((code) => (
                      <View key={code} style={styles.codeChip}>
                        <Text style={styles.codeText}>{code}</Text>
                      </View>
                    ))}
                    {item.codes.length > 3 && (
                      <View style={styles.moreCodesChip}>
                        <Text style={styles.moreCodesText}>
                          +{item.codes.length - 3}
                        </Text>
                      </View>
                    )}
                  </View>
                ) : (
                  <Text style={styles.noCodesText}>{t('history.none')}</Text>
                )}
              </View>
            </View>
          </TouchableOpacity>

          {/* Expandable Bottom Section Toggle */}
          {item.foodScore && (
            <>
              <TouchableOpacity
                style={styles.expandToggle}
                onPress={toggleExpansion}
                activeOpacity={0.7}
              >
                <View style={styles.expandToggleContent}>
                  <Text style={styles.expandToggleText}>
                    {t('foodScore.nutrition')}
                  </Text>
                  <Animated.View style={animatedChevronStyle}>
                    <Ionicons name="chevron-down" size={20} color="#666" />
                  </Animated.View>
                </View>
              </TouchableOpacity>

              {/* Hidden content for measuring height */}
              {contentHeight === 0 && (
                <View
                  style={[
                    styles.bottomSectionContent,
                    styles.hiddenMeasurement,
                  ]}
                  onLayout={onContentLayout}
                >
                  <FoodScoreDisplay score={item.foodScore} compact={true} />
                </View>
              )}

              {/* Animated Bottom Section - Food Score Display */}
              <Animated.View
                style={[styles.animatedBottomSection, animatedContainerStyle]}
              >
                <View style={styles.bottomSectionContent}>
                  <FoodScoreDisplay score={item.foodScore} compact={true} />
                </View>
              </Animated.View>
            </>
          )}
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
    marginHorizontal: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  cardGradient: {
    padding: 16,
  },
  cardContent: {
    flexDirection: 'column',
  },
  topSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingBottom: 0,
  },
  // New animated bottom section styles
  expandToggle: {
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    marginTop: 8,
  },
  expandToggleContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  expandToggleText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  animatedBottomSection: {
    overflow: 'hidden',
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    marginTop: 4,
  },
  bottomSectionContent: {
    padding: 12,
  },
  hiddenMeasurement: {
    position: 'absolute',
    opacity: 0,
    zIndex: -1,
  },
  // Image container and thumbnail styles
  imageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  imageThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    overflow: 'hidden',
    position: 'relative',
    borderWidth: 2,
    borderColor: '#E9ECEF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  multiImageIndicator: {
    position: 'absolute',
    bottom: 4,
    left: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  multiImageCount: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  imageLoading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageError: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  imageOverlay: {
    position: 'absolute',
    top: 6,
    right: 6,
  },
  typeIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  // Text content styles
  textContent: {
    flex: 1,
  },
  textContentFullWidth: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  headerContent: {
    flex: 1,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 8,
    lineHeight: 22,
  },
  codesContainer: {
    marginBottom: 4,
  },
  codesLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
    marginBottom: 6,
  },
  codesWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  codeChip: {
    backgroundColor: '#F0F4FF',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E3F2FD',
  },
  codeText: {
    fontSize: 11,
    color: '#1976D2',
    fontWeight: '600',
  },
  moreCodesChip: {
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  moreCodesText: {
    fontSize: 11,
    color: '#666',
    fontWeight: '500',
  },
  noCodesText: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
});

export default HistoryItem;
