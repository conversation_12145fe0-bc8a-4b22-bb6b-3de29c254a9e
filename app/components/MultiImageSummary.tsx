import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useLocalization } from '../context/LocalizationContext';
import { MultiImageStep } from '../hooks/useMultiImageScan';

interface MultiImageSummaryProps {
  visible: boolean;
  steps: MultiImageStep[];
  onClose: () => void;
  onRetakeStep: (stepNumber: number) => void;
  onStartAnalysis: () => void;
  isAnalyzing: boolean;
}

const { width } = Dimensions.get('window');

export default function MultiImageSummary({
  visible,
  steps,
  onClose,
  onRetakeStep,
  onStartAnalysis,
  isAnalyzing,
}: MultiImageSummaryProps) {
  const { t } = useLocalization();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const slideAnimation = useSharedValue(0);
  const fadeAnimation = useSharedValue(0);

  React.useEffect(() => {
    if (visible) {
      slideAnimation.value = withSpring(1, { damping: 20 });
      fadeAnimation.value = withTiming(1, { duration: 300 });
    } else {
      slideAnimation.value = withTiming(0, { duration: 200 });
      fadeAnimation.value = withTiming(0, { duration: 200 });
    }
  }, [visible]);

  const containerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: (1 - slideAnimation.value) * 100,
      },
    ],
    opacity: fadeAnimation.value,
  }));

  const completedSteps = steps.filter((step) => step.completed);

  const getStepColor = (purpose: string): string => {
    switch (purpose) {
      case 'ingredients':
        return '#4CAF50';
      case 'nutrition':
        return '#FF9800';
      case 'general':
        return '#2196F3';
      default:
        return '#2196F3';
    }
  };

  const getStepIcon = (purpose: string): string => {
    switch (purpose) {
      case 'ingredients':
        return 'list-outline';
      case 'nutrition':
        return 'nutrition-outline';
      case 'general':
        return 'document-text-outline';
      default:
        return 'document-text-outline';
    }
  };

  const getQualityIcon = (quality?: 'good' | 'poor'): string => {
    return quality === 'good' ? 'checkmark-circle' : 'warning';
  };

  const getQualityColor = (quality?: 'good' | 'poor'): string => {
    return quality === 'good' ? '#4CAF50' : '#FF9800';
  };

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={onClose}
        />

        <Animated.View style={[styles.container, containerAnimatedStyle]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>{t('scan.reviewImages')}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={26} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Progress Summary */}
          <View style={styles.progressSummary}>
            <Text style={styles.progressText}>
              {t('scan.allImagesComplete')} ({completedSteps.length}/3)
            </Text>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${(completedSteps.length / 3) * 100}%` },
                ]}
              />
            </View>
          </View>

          {/* Images List */}
          <ScrollView
            style={styles.imagesList}
            showsVerticalScrollIndicator={false}
          >
            {completedSteps.map((step) => (
              <View key={step.step} style={styles.imageItem}>
                <View style={styles.imageHeader}>
                  <View style={styles.stepInfo}>
                    <View
                      style={[
                        styles.stepIcon,
                        { backgroundColor: getStepColor(step.purpose) },
                      ]}
                    >
                      <Ionicons
                        name={getStepIcon(step.purpose) as any}
                        size={20}
                        color="white"
                      />
                    </View>
                    <View style={styles.stepText}>
                      <Text style={styles.stepTitle}>{step.title}</Text>
                      <Text style={styles.stepDescription}>
                        {step.description}
                      </Text>
                    </View>
                  </View>

                  {/* Quality Indicator */}
                  <View style={styles.qualityIndicator}>
                    <Ionicons
                      name={getQualityIcon(step.quality) as any}
                      size={16}
                      color={getQualityColor(step.quality)}
                    />
                    <Text
                      style={[
                        styles.qualityText,
                        { color: getQualityColor(step.quality) },
                      ]}
                    >
                      {step.quality === 'good'
                        ? t('scan.imageQualityGood')
                        : t('scan.imageQualityPoor')}
                    </Text>
                  </View>
                </View>

                {/* Image Preview */}
                <View style={styles.imagePreview}>
                  <TouchableOpacity
                    onPress={() => setSelectedImage(step.imageUri!)}
                    style={styles.imageContainer}
                  >
                    <Image
                      source={{ uri: step.imageUri }}
                      style={styles.image}
                      resizeMode="cover"
                    />
                    <View style={styles.imageOverlay}>
                      <Ionicons name="expand" size={20} color="white" />
                    </View>
                  </TouchableOpacity>

                  {/* Retake Button */}
                  <TouchableOpacity
                    style={styles.retakeButton}
                    onPress={() => onRetakeStep(step.step)}
                  >
                    <Ionicons name="refresh" size={16} color="#FF6347" />
                    <Text style={styles.retakeButtonText}>
                      {t('scan.retakeImage')}
                    </Text>
                  </TouchableOpacity>
                </View>

                {/* Image Status */}
                <View style={styles.imageStatus}>
                  <Text style={styles.imageStatusText}>
                    {t('scan.imageContributed')} - {t('scan.imageQualityGood')}
                  </Text>
                </View>
              </View>
            ))}
          </ScrollView>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, styles.startAnalysisButton]}
              onPress={onStartAnalysis}
              disabled={isAnalyzing}
            >
              {isAnalyzing ? (
                <>
                  <Text style={styles.actionButtonText}>
                    {t('scan.processing')}
                  </Text>
                </>
              ) : (
                <>
                  <Text style={styles.actionButtonText}>
                    {t('scan.startAnalysis')}
                  </Text>
                  <Ionicons name="arrow-forward" size={20} color="white" />
                </>
              )}
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Full Size Image Modal */}
        {selectedImage && (
          <Modal
            transparent
            visible={!!selectedImage}
            onRequestClose={() => setSelectedImage(null)}
          >
            <View style={styles.fullImageOverlay}>
              <TouchableOpacity
                style={styles.fullImageBackdrop}
                onPress={() => setSelectedImage(null)}
              />
              <View style={styles.fullImageContainer}>
                <Image
                  source={{ uri: selectedImage }}
                  style={styles.fullImage}
                  resizeMode="contain"
                />
                <TouchableOpacity
                  style={styles.fullImageCloseButton}
                  onPress={() => setSelectedImage(null)}
                >
                  <Ionicons name="close" size={24} color="white" />
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        )}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  container: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingTop: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  progressSummary: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  progressText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 2,
  },
  imagesList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  imageItem: {
    marginBottom: 20,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
  },
  imageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  stepInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  stepIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepText: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  stepDescription: {
    fontSize: 14,
    color: '#666',
  },
  qualityIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  qualityText: {
    fontSize: 12,
    fontWeight: '500',
  },
  imagePreview: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 12,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  retakeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: 'white',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#FF6347',
    gap: 4,
  },
  retakeButtonText: {
    color: '#FF6347',
    fontSize: 12,
    fontWeight: '500',
  },
  imageStatus: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
  },
  imageStatusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
    textAlign: 'center',
  },
  actionButtons: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  actionButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  startAnalysisButton: {
    backgroundColor: '#4CAF50',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  fullImageOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImageBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  fullImageContainer: {
    width: '90%',
    height: '80%',
    position: 'relative',
  },
  fullImage: {
    width: '100%',
    height: '100%',
  },
  fullImageCloseButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 8,
  },
});
