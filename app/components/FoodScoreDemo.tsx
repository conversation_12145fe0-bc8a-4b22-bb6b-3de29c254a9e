import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import FoodScoreDisplay from './FoodScoreDisplay';
import {
  FoodScoreCalculator,
  ProductNutrition,
} from '../utils/FoodScoreCalculator';

// Mock additives data for demo
const mockAdditives = [
  {
    code: 'E200',
    name: 'Sorbic acid',
    safety_level: 'safe',
    category: 'Preservative',
    description: 'Safe preservative',
    usage_areas: ['Food preservation'],
    halal_status: 'halal',
    slugs: ['sorbic-acid'],
    title_en: 'Sorbic acid',
    title_tr: 'Sorbik asit',
  },
  {
    code: 'E621',
    name: 'Monosodium glutamate',
    safety_level: 'moderate',
    category: 'Flavor enhancer',
    description: 'Moderate risk flavor enhancer',
    usage_areas: ['Flavor enhancement'],
    halal_status: 'halal',
    slugs: ['msg', 'monosodium-glutamate'],
    title_en: 'Monosodium glutamate',
    title_tr: 'Mononatriyum glutamat',
  },
] as any[];

const FoodScoreDemo: React.FC = () => {
  // Example 1: Healthy product with minimal additives
  const healthyNutrition: ProductNutrition = {
    energy_100g: 180,
    proteins_100g: 12,
    carbohydrates_100g: 15,
    sugars_100g: 3,
    fat_100g: 8,
    saturated_fat_100g: 1.5,
    fiber_100g: 4,
    sodium_100g: 200,
  };

  const healthyScore = FoodScoreCalculator.calculateFoodScore(
    ['E200'], // Only one safe additive
    mockAdditives,
    healthyNutrition,
    true // Organic
  );

  // Example 2: Unhealthy product with multiple additives
  const unhealthyNutrition: ProductNutrition = {
    energy_100g: 450,
    proteins_100g: 3,
    carbohydrates_100g: 65,
    sugars_100g: 35,
    fat_100g: 18,
    saturated_fat_100g: 12,
    fiber_100g: 1,
    sodium_100g: 800,
  };

  const unhealthyScore = FoodScoreCalculator.calculateFoodScore(
    ['E200', 'E621'], // Multiple additives including moderate risk
    mockAdditives,
    unhealthyNutrition,
    false // Not organic
  );

  // Example 3: Product with only additives (no nutrition data)
  const additivesOnlyScore = FoodScoreCalculator.calculateFoodScore(
    ['E621'],
    mockAdditives
  );

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Food Scoring System Demo</Text>

      <Text style={styles.sectionTitle}>
        Example 1: Healthy Organic Product
      </Text>
      <Text style={styles.description}>
        Low calories, good protein, minimal sugar, one safe additive, organic
        certification
      </Text>
      <FoodScoreDisplay score={healthyScore} compact={false} />

      <Text style={styles.sectionTitle}>
        Example 2: Processed Unhealthy Product
      </Text>
      <Text style={styles.description}>
        High calories, high sugar, high sodium, multiple additives, not organic
      </Text>
      <FoodScoreDisplay score={unhealthyScore} compact={false} />

      <Text style={styles.sectionTitle}>
        Example 3: Additives Only (No Nutrition Data)
      </Text>
      <Text style={styles.description}>
        When only E-codes are detected without nutrition information
      </Text>
      <FoodScoreDisplay score={additivesOnlyScore} compact={true} />

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          The scoring system evaluates products based on:
        </Text>
        <Text style={styles.bulletPoint}>• Additives safety (30%)</Text>
        <Text style={styles.bulletPoint}>• Nutritional quality (30%)</Text>
        <Text style={styles.bulletPoint}>• Processing level (20%)</Text>
        <Text style={styles.bulletPoint}>• Ingredients quality (20%)</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 20,
    marginBottom: 8,
    color: '#333',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    fontStyle: 'italic',
  },
  footer: {
    marginTop: 30,
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 20,
  },
  footerText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  bulletPoint: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    marginBottom: 4,
  },
});

export default FoodScoreDemo;
