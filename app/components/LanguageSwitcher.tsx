import React, { useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
  Animated,
} from 'react-native';
import { useLocalization } from '../context/LocalizationContext';

export function LanguageSwitcher() {
  const { language, setLanguage } = useLocalization();
  const slideAnim = useRef(
    new Animated.Value(language === 'tr' ? 0 : 1)
  ).current;

  // Update animation when language changes
  useEffect(() => {
    Animated.spring(slideAnim, {
      toValue: language === 'tr' ? 0 : 1,
      useNativeDriver: true,
      friction: 8,
      tension: 60,
    }).start();
  }, [language, slideAnim]);

  // Calculate the translation for the sliding indicator
  const slideTranslate = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 40], // Width of one button
  });

  return (
    <View style={styles.container}>
      {/* Animated slider */}
      <Animated.View
        style={[styles.slider, { transform: [{ translateX: slideTranslate }] }]}
      />

      {/* TR Button */}
      <TouchableOpacity
        activeOpacity={0.7}
        style={styles.button}
        onPress={() => setLanguage('tr')}
      >
        <Text style={[styles.text, language === 'tr' && styles.activeText]}>
          TR
        </Text>
      </TouchableOpacity>

      {/* EN Button */}
      <TouchableOpacity
        activeOpacity={0.7}
        style={styles.button}
        onPress={() => setLanguage('en')}
      >
        <Text style={[styles.text, language === 'en' && styles.activeText]}>
          EN
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 2,
    position: 'relative',
    width: 84, // Fixed width to match original size (2 buttons at 40px + 4px padding)
    height: 32, // Fixed height
  },
  slider: {
    position: 'absolute',
    width: 40,
    height: '100%',
    backgroundColor: '#2196F3',
    borderRadius: 6,
    top: 2,
    left: 2,
    bottom: 2,
    zIndex: 0,
  },
  button: {
    borderRadius: 6,
    width: 40,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  text: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  activeText: {
    color: '#fff',
  },
});
