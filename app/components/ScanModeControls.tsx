import React from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Text,
  View,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useMemo, useRef, useEffect } from 'react';

type ScanMode = 'barcode' | 'text';

interface ScanModeControlsProps {
  scanMode: ScanMode;
  onModeChange: (mode: ScanMode) => void;
  t: (key: string) => string; // Çeviri fonksiyonu
  showGuidanceButton?: boolean;
  onShowGuidance?: () => void;
}

export default function ScanModeControls({
  scanMode,
  onModeChange,
  t,
  showGuidanceButton = false,
  onShowGuidance,
}: ScanModeControlsProps) {
  // Animation for slider
  const slideAnim = useRef(
    new Animated.Value(scanMode === 'text' ? 0 : 1)
  ).current;

  // Get screen width for responsive design
  const screenWidth = Dimensions.get('window').width;

  // Calculate button width for the slider animation
  const buttonWidth = useMemo(() => (screenWidth - 80) / 2, [screenWidth]);

  // Update animation when scan mode changes
  useEffect(() => {
    Animated.spring(slideAnim, {
      toValue: scanMode === 'text' ? 0 : 1,
      useNativeDriver: true,
      friction: 8,
      tension: 50,
    }).start();
  }, [scanMode, slideAnim]);

  // Calculate the translation for the sliding indicator
  const slideTranslate = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, buttonWidth],
  });

  return (
    <View style={styles.container}>
      {/* Guidance Button */}
      {showGuidanceButton && scanMode === 'text' && onShowGuidance && (
        <TouchableOpacity
          style={styles.guidanceButton}
          onPress={() => {
            console.log('Guidance button pressed');
            onShowGuidance();
          }}
          activeOpacity={0.7}
        >
          <Ionicons name="help-circle-outline" size={20} color="#2196F3" />
          <Text style={styles.guidanceButtonText}>
            {t('scan.showGuidance')}
          </Text>
        </TouchableOpacity>
      )}

      <View style={styles.controls}>
        {/* Animated slider background */}
        <Animated.View
          style={[
            styles.slider,
            {
              width: buttonWidth,
              transform: [{ translateX: slideTranslate }],
            },
          ]}
        />

        {/* Text scanning button */}
        <TouchableOpacity
          style={[styles.modeButton, { width: buttonWidth }]}
          onPress={() => onModeChange('text')}
          activeOpacity={0.7}
        >
          <Ionicons
            name="text-outline"
            size={20}
            color={scanMode === 'text' ? '#fff' : '#666'}
            style={styles.icon}
          />
          <Text
            style={[
              styles.modeButtonText,
              scanMode === 'text' && styles.activeModeButtonText,
            ]}
          >
            {t('scan.text')}
          </Text>
        </TouchableOpacity>

        {/* Barcode button */}
        <TouchableOpacity
          style={[styles.modeButton, { width: buttonWidth }]}
          onPress={() => onModeChange('barcode')}
          activeOpacity={0.7}
        >
          <Ionicons
            name="barcode-outline"
            size={20}
            color={scanMode === 'barcode' ? '#fff' : '#666'}
            style={styles.icon}
          />
          <Text
            style={[
              styles.modeButtonText,
              scanMode === 'barcode' && styles.activeModeButtonText,
            ]}
          >
            {t('scan.barcode')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginTop: 20,
    paddingHorizontal: 20,
  },
  guidanceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 12,
    gap: 6,
  },
  guidanceButtonText: {
    color: '#2196F3',
    fontSize: 12,
    fontWeight: '600',
  },
  controls: {
    flexDirection: 'row',
    backgroundColor: 'rgba(240, 240, 240, 0.95)',
    borderRadius: 30,
    padding: 4,
    position: 'relative',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    width: '100%',
  },
  slider: {
    position: 'absolute',
    height: '100%',
    backgroundColor: '#2196F3',
    borderRadius: 26,
    top: 4,
    left: 4,
    bottom: 4,
    zIndex: 0,
  },
  modeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 26,
    zIndex: 1,
  },
  icon: {
    marginRight: 8,
  },
  modeButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#666',
  },
  activeModeButtonText: {
    color: '#fff',
  },
  modeDescription: {
    marginTop: 12,
    textAlign: 'center',
    color: 'rgba(0, 0, 0, 0.6)',
    fontSize: 13,
    maxWidth: '85%',
  },
});
