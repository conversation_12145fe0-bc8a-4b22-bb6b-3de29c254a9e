import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../context/LocalizationContext';
import CircularProgress from './CircularProgress';
import { FoodScore } from '../utils/FoodScoreCalculator';

interface FoodScoreDisplayProps {
  score: FoodScore;
  onShowBreakdown?: () => void;
  compact?: boolean;
}

const FoodScoreDisplay: React.FC<FoodScoreDisplayProps> = ({
  score,
  onShowBreakdown,
  compact = false,
}) => {
  const { t } = useLocalization();
  const [showScoreInfo, setShowScoreInfo] = useState(false);

  const getGradeColor = (grade: string): string => {
    switch (grade) {
      case 'A':
      case 'B':
        return '#4CAF50';
      case 'C':
        return '#FFC107';
      case 'D':
        return '#FF9800';
      case 'E':
      case 'F':
        return '#F44336';
      default:
        return '#666';
    }
  };

  if (compact) {
    return (
      <View style={styles.compactContainer}>
        <View style={styles.compactHeader}>
          <View style={styles.compactGradeContainer}>
            <Text
              style={[
                styles.compactGradeText,
                { color: getGradeColor(score.grade) },
              ]}
            >
              {score.grade}
            </Text>
            <Text style={styles.compactGradeSubtext}>{score.overall}/100</Text>
          </View>
          <View style={styles.compactScores}>
            <CircularProgress
              percentage={score.breakdown.additives.score}
              size={50}
              width={4}
              label={t('foodScore.additives')}
            />
            <CircularProgress
              percentage={score.nutrition}
              size={50}
              width={4}
              label={t('foodScore.nutrition')}
            />
            <CircularProgress
              percentage={score.breakdown.processing.score}
              size={50}
              width={4}
              label={t('foodScore.processing')}
            />
          </View>
        </View>
        <Text style={styles.compactRecommendation}>{score.recommendation}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header with Overall Score */}
      <View style={styles.header}>
        <View style={styles.overallScoreContainer}>
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>
              {t('foodScore.overallScore')}
            </Text>
            <TouchableOpacity
              style={styles.infoButton}
              onPress={() => setShowScoreInfo(true)}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons
                name="information-circle-outline"
                size={18}
                color="#666"
              />
            </TouchableOpacity>
          </View>
          <View style={styles.gradeDisplay}>
            <Text
              style={[
                styles.gradeLetter,
                { color: getGradeColor(score.grade) },
              ]}
            >
              {score.grade}
            </Text>
            <Text style={styles.scoreNumber}>{score.overall}/100</Text>
          </View>
        </View>
      </View>

      {/* Three Circular Progress Indicators */}
      <View style={styles.scoresContainer}>
        <CircularProgress
          percentage={score.breakdown.additives.score}
          size={80}
          width={8}
          label={t('foodScore.additives')}
          animated={true}
        />
        <CircularProgress
          percentage={score.nutrition}
          size={80}
          width={8}
          label={t('foodScore.nutrition')}
          animated={true}
        />
        <CircularProgress
          percentage={score.breakdown.processing.score}
          size={80}
          width={8}
          label={t('foodScore.processing')}
          animated={true}
        />
      </View>

      {/* Recommendation */}
      <View style={styles.recommendationContainer}>
        <View style={styles.recommendationHeader}>
          <Ionicons name="information-circle-outline" size={20} color="#666" />
          <Text style={styles.recommendationTitle}>
            {t('foodScore.recommendation')}
          </Text>
        </View>
        <Text style={styles.recommendationText}>{score.recommendation}</Text>
      </View>

      {/* Show Breakdown Button */}
      {onShowBreakdown && (
        <TouchableOpacity
          style={styles.breakdownButton}
          onPress={onShowBreakdown}
          activeOpacity={0.7}
        >
          <Text style={styles.breakdownButtonText}>
            {t('foodScore.showBreakdown')}
          </Text>
          <Ionicons name="chevron-forward" size={20} color="#2196F3" />
        </TouchableOpacity>
      )}

      {/* Score Info Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={showScoreInfo}
        onRequestClose={() => setShowScoreInfo(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {t('foodScore.howCalculated')}
              </Text>
              <TouchableOpacity
                onPress={() => setShowScoreInfo(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <Text style={styles.modalDescription}>
              {t('foodScore.overallScoreExplanation')}
            </Text>

            <View style={styles.formulaContainer}>
              <Text style={styles.formulaTitle}>{t('foodScore.formula')}:</Text>
              <View style={styles.formulaBreakdown}>
                <Text style={styles.formulaItem}>
                  • {t('foodScore.additives')}: 30%
                </Text>
                <Text style={styles.formulaItem}>
                  • {t('foodScore.nutrition')}: 30%
                </Text>
                <Text style={styles.formulaItem}>
                  • {t('foodScore.processing')}: 20%
                </Text>
                <Text style={styles.formulaItem}>
                  • {t('foodScore.ingredients')}: 20%
                </Text>
              </View>
            </View>

            <Text style={styles.modalNote}>{t('foodScore.displayNote')}</Text>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  compactContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  overallScoreContainer: {
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoButton: {
    marginLeft: 6,
    padding: 2,
  },
  gradeDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  gradeLetter: {
    fontSize: 48,
    fontWeight: 'bold',
  },
  scoreNumber: {
    fontSize: 20,
    fontWeight: '500',
    color: '#666',
  },
  scoresContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: 20,
  },
  recommendationContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  recommendationText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  breakdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e3f2fd',
  },
  breakdownButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2196F3',
  },
  compactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  gradeContainer: {
    alignItems: 'center',
    minWidth: 60,
  },
  gradeText: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  gradeSubtext: {
    fontSize: 11,
    color: '#666',
    fontWeight: '500',
  },
  compactGradeContainer: {
    alignItems: 'center',
    marginRight: 12,
  },
  compactGradeText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  compactGradeSubtext: {
    fontSize: 10,
    color: '#666',
    fontWeight: '500',
  },
  compactScores: {
    flexDirection: 'row',
    gap: 6,
    flex: 1,
    justifyContent: 'space-between',
  },
  compactRecommendation: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 350,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  modalCloseButton: {
    padding: 4,
  },
  modalDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 16,
  },
  formulaContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  formulaTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  formulaBreakdown: {
    gap: 4,
  },
  formulaItem: {
    fontSize: 13,
    color: '#555',
    lineHeight: 18,
  },
  modalNote: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
    lineHeight: 16,
  },
});

export default FoodScoreDisplay;
