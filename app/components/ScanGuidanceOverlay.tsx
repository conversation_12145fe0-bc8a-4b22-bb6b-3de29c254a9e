import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Modal,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withRepeat,
  withSequence,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { useLocalization } from '../context/LocalizationContext';

interface ScanGuidanceOverlayProps {
  visible: boolean;
  scanMode: 'barcode' | 'text';
  onClose: () => void;
  onShowTutorial: () => void;
}

const { width, height } = Dimensions.get('window');
const SCAN_AREA_SIZE = Math.min(width * 0.8, 300);

export default function ScanGuidanceOverlay({
  visible,
  scanMode,
  onClose,
  onShowTutorial,
}: ScanGuidanceOverlayProps) {
  const { t } = useLocalization();
  const [showQuickTips, setShowQuickTips] = useState(false);

  // Animation values
  const pulseAnimation = useSharedValue(0);
  const fadeAnimation = useSharedValue(0);
  const slideAnimation = useSharedValue(0);

  useEffect(() => {
    if (visible && scanMode === 'text') {
      // Start animations when overlay becomes visible
      fadeAnimation.value = withTiming(1, { duration: 300 });
      slideAnimation.value = withTiming(1, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      });

      // Start pulse animation for guidance elements
      pulseAnimation.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 1000 }),
          withTiming(0, { duration: 1000 })
        ),
        -1,
        true
      );
    } else {
      fadeAnimation.value = withTiming(0, { duration: 200 });
      slideAnimation.value = withTiming(0, { duration: 200 });
      pulseAnimation.value = 0;
    }
  }, [visible, scanMode]);

  const overlayAnimatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: interpolate(slideAnimation.value, [0, 1], [50, 0]),
      },
    ],
    opacity: fadeAnimation.value,
  }));

  const pulseAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(pulseAnimation.value, [0, 1], [0.6, 1]),
    transform: [
      {
        scale: interpolate(pulseAnimation.value, [0, 1], [0.98, 1.02]),
      },
    ],
  }));

  if (!visible || scanMode !== 'text') {
    return null;
  }

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <Animated.View style={[styles.overlay, overlayAnimatedStyle]}>
        <BlurView
          intensity={20}
          style={StyleSheet.absoluteFillObject}
          tint="dark"
        />

        {/* Guidance Frame */}
        <View style={styles.guidanceContainer}>
          <Animated.View style={[styles.guidanceFrame, pulseAnimatedStyle]}>
            {/* Corner indicators */}
            <View style={[styles.corner, styles.cornerTL]} />
            <View style={[styles.corner, styles.cornerTR]} />
            <View style={[styles.corner, styles.cornerBL]} />
            <View style={[styles.corner, styles.cornerBR]} />

            {/* Center guidance text */}
            <View style={styles.centerGuidance}>
              <Ionicons
                name="document-text-outline"
                size={32}
                color="#2196F3"
              />
              <Text style={styles.centerGuidanceText}>
                {t('scan.frameIngredients')}
              </Text>
            </View>
          </Animated.View>
        </View>

        {/* Quick Tips Panel */}
        <Animated.View style={[styles.tipsContainer, contentAnimatedStyle]}>
          <View style={styles.tipsHeader}>
            <Text style={styles.tipsTitle}>{t('scan.scanningTips')}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.tipsList}>
            <View style={styles.tipItem}>
              <Ionicons name="crop-outline" size={16} color="#2196F3" />
              <Text style={styles.tipText}>{t('scan.frameIngredients')}</Text>
            </View>
            <View style={styles.tipItem}>
              <Ionicons name="hand-left-outline" size={16} color="#2196F3" />
              <Text style={styles.tipText}>{t('scan.holdSteady')}</Text>
            </View>
            <View style={styles.tipItem}>
              <Ionicons name="sunny-outline" size={16} color="#2196F3" />
              <Text style={styles.tipText}>{t('scan.goodLighting')}</Text>
            </View>
            <View style={styles.tipItem}>
              <Ionicons name="eye-outline" size={16} color="#2196F3" />
              <Text style={styles.tipText}>{t('scan.clearText')}</Text>
            </View>
          </View>

          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.button, styles.secondaryButton]}
              onPress={onShowTutorial}
            >
              <Text style={styles.secondaryButtonText}>
                {t('scan.bestPractices')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={onClose}
            >
              <Text style={styles.primaryButtonText}>{t('scan.gotIt')}</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  guidanceContainer: {
    position: 'absolute',
    top: height * 0.25,
    alignItems: 'center',
  },
  guidanceFrame: {
    width: SCAN_AREA_SIZE,
    height: SCAN_AREA_SIZE * 0.7,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  corner: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderColor: '#2196F3',
    borderWidth: 3,
  },
  cornerTL: {
    top: -2,
    left: -2,
    borderBottomWidth: 0,
    borderRightWidth: 0,
    borderTopLeftRadius: 8,
  },
  cornerTR: {
    top: -2,
    right: -2,
    borderBottomWidth: 0,
    borderLeftWidth: 0,
    borderTopRightRadius: 8,
  },
  cornerBL: {
    bottom: -2,
    left: -2,
    borderTopWidth: 0,
    borderRightWidth: 0,
    borderBottomLeftRadius: 8,
  },
  cornerBR: {
    bottom: -2,
    right: -2,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderBottomRightRadius: 8,
  },
  centerGuidance: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(33, 150, 243, 0.3)',
  },
  centerGuidanceText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  tipsContainer: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  tipsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tipsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  tipsList: {
    marginBottom: 20,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: '#555',
    marginLeft: 12,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: '#2196F3',
  },
  secondaryButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
});
