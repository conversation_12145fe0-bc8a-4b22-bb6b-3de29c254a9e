import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Switch } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Constants from 'expo-constants';
import { useLocalization } from '../context/LocalizationContext';
import { useDebugSettings, ImageAnalysisMode } from '../hooks/useDebugSettings';

interface DebugModeToggleProps {
  visible?: boolean;
}

export function DebugModeToggle({ visible = true }: DebugModeToggleProps) {
  const { t } = useLocalization();
  const {
    settings,
    toggleDebugMode,
    setImageAnalysisMode,
    getCurrentModeDisplayName,
  } = useDebugSettings();

  // Only show in development mode
  const isDevelopment =
    __DEV__ ||
    Constants.expoConfig?.extra?.environment === 'development' ||
    process.env.EXPO_PUBLIC_ENVIRONMENT !== 'production';

  if (!visible || !isDevelopment) return null;

  const handleModeChange = (mode: ImageAnalysisMode) => {
    setImageAnalysisMode(mode);
  };

  return (
    <View style={styles.container}>
      {/* Debug Mode Toggle */}
      <View style={styles.debugToggleContainer}>
        <View style={styles.debugToggleHeader}>
          <MaterialIcons name="bug-report" size={20} color="#2196F3" />
          <Text style={styles.debugToggleTitle}>{t('scan.debugMode')}</Text>
          <Switch
            value={settings.debugModeEnabled}
            onValueChange={toggleDebugMode}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={settings.debugModeEnabled ? '#2196F3' : '#f4f3f4'}
          />
        </View>

        {settings.debugModeEnabled && (
          <Text style={styles.debugDescription}>
            {t('scan.debugModeDescription')}
          </Text>
        )}
      </View>

      {/* Analysis Mode Selection */}
      {settings.debugModeEnabled && (
        <View style={styles.modeSelectionContainer}>
          <Text style={styles.modeSelectionTitle}>
            {t('scan.currentMode')}: {getCurrentModeDisplayName(t)}
          </Text>

          <View style={styles.modeButtons}>
            <TouchableOpacity
              style={[
                styles.modeButton,
                settings.imageAnalysisMode === 'direct' &&
                  styles.activeModeButton,
              ]}
              onPress={() => handleModeChange('direct')}
            >
              <MaterialIcons
                name="image"
                size={16}
                color={
                  settings.imageAnalysisMode === 'direct' ? '#fff' : '#666'
                }
              />
              <Text
                style={[
                  styles.modeButtonText,
                  settings.imageAnalysisMode === 'direct' &&
                    styles.activeModeButtonText,
                ]}
              >
                {t('scan.directImageAnalysis')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.modeButton,
                settings.imageAnalysisMode === 'textExtraction' &&
                  styles.activeModeButton,
              ]}
              onPress={() => handleModeChange('textExtraction')}
            >
              <MaterialIcons
                name="text-fields"
                size={16}
                color={
                  settings.imageAnalysisMode === 'textExtraction'
                    ? '#fff'
                    : '#666'
                }
              />
              <Text
                style={[
                  styles.modeButtonText,
                  settings.imageAnalysisMode === 'textExtraction' &&
                    styles.activeModeButtonText,
                ]}
              >
                {t('scan.textExtractionAnalysis')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    position: 'absolute',
    top: 20,
  },
  debugToggleContainer: {
    marginBottom: 12,
  },
  debugToggleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  debugToggleTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginLeft: 8,
  },
  debugDescription: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    marginTop: 8,
    marginLeft: 28,
  },
  modeSelectionContainer: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
    paddingTop: 12,
  },
  modeSelectionTitle: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 12,
  },
  modeButtons: {
    gap: 8,
  },
  modeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  activeModeButton: {
    backgroundColor: '#2196F3',
    borderColor: '#2196F3',
  },
  modeButtonText: {
    color: '#ccc',
    fontSize: 13,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  activeModeButtonText: {
    color: 'white',
  },
});
