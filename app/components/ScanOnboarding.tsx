import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Modal,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withDelay,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { useLocalization } from '../context/LocalizationContext';

interface ScanOnboardingProps {
  visible: boolean;
  onComplete: () => void;
}

const { width, height } = Dimensions.get('window');

export default function ScanOnboarding({ visible, onComplete }: ScanOnboardingProps) {
  const { t } = useLocalization();

  // Animation values
  const fadeAnimation = useSharedValue(0);
  const slideAnimation = useSharedValue(0);
  const iconAnimation = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      fadeAnimation.value = withTiming(1, { duration: 400 });
      slideAnimation.value = withTiming(1, { 
        duration: 600, 
        easing: Easing.out(Easing.cubic) 
      });
      iconAnimation.value = withDelay(
        300,
        withTiming(1, { duration: 800, easing: Easing.elastic(1.2) })
      );
    } else {
      fadeAnimation.value = withTiming(0, { duration: 200 });
      slideAnimation.value = withTiming(0, { duration: 200 });
      iconAnimation.value = 0;
    }
  }, [visible]);

  const overlayAnimatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: interpolate(slideAnimation.value, [0, 1], [100, 0]),
      },
    ],
    opacity: fadeAnimation.value,
  }));

  const iconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        scale: interpolate(iconAnimation.value, [0, 1], [0.5, 1]),
      },
    ],
    opacity: iconAnimation.value,
  }));

  if (!visible) {
    return null;
  }

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onComplete}
    >
      <Animated.View style={[styles.overlay, overlayAnimatedStyle]}>
        <BlurView intensity={40} style={StyleSheet.absoluteFillObject} tint="dark" />
        
        <Animated.View style={[styles.container, contentAnimatedStyle]}>
          {/* Icon */}
          <Animated.View style={[styles.iconContainer, iconAnimatedStyle]}>
            <Ionicons name="scan" size={64} color="#2196F3" />
          </Animated.View>

          {/* Content */}
          <Text style={styles.title}>{t('scan.guidanceTitle')}</Text>
          <Text style={styles.subtitle}>{t('scan.guidanceSubtitle')}</Text>

          {/* Features */}
          <View style={styles.featuresContainer}>
            <View style={styles.feature}>
              <Ionicons name="camera-outline" size={24} color="#2196F3" />
              <Text style={styles.featureText}>
                {t('scan.ingredientTip')}
              </Text>
            </View>
            
            <View style={styles.feature}>
              <Ionicons name="bulb-outline" size={24} color="#2196F3" />
              <Text style={styles.featureText}>
                {t('scan.lightingTip')}
              </Text>
            </View>
            
            <View style={styles.feature}>
              <Ionicons name="checkmark-circle-outline" size={24} color="#2196F3" />
              <Text style={styles.featureText}>
                {t('scan.focusTip')}
              </Text>
            </View>
          </View>

          {/* Action Button */}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={onComplete}
            activeOpacity={0.8}
          >
            <Text style={styles.actionButtonText}>{t('scan.gotIt')}</Text>
            <Ionicons name="arrow-forward" size={20} color="white" />
          </TouchableOpacity>

          {/* Skip Button */}
          <TouchableOpacity
            style={styles.skipButton}
            onPress={onComplete}
            activeOpacity={0.7}
          >
            <Text style={styles.skipButtonText}>{t('scan.skipTutorial')}</Text>
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 20,
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 24,
    padding: 32,
    width: '100%',
    maxWidth: 380,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 22,
  },
  featuresContainer: {
    width: '100%',
    marginBottom: 32,
    gap: 16,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  featureText: {
    flex: 1,
    fontSize: 16,
    color: '#555',
    lineHeight: 22,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2196F3',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    marginBottom: 16,
    gap: 8,
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  skipButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  skipButtonText: {
    color: '#888',
    fontSize: 16,
  },
});
