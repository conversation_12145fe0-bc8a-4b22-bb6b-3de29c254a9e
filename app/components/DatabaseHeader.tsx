import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../context/LocalizationContext';
import { useFilter } from '../context/FilterContext';

export function DatabaseHeader() {
  const { t } = useLocalization();
  const { filterVisible, setFilterVisible } = useFilter();

  return (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>{t('common.ecodeDatabase')}</Text>
      <TouchableOpacity
        style={styles.filterButton}
        onPress={() => setFilterVisible(!filterVisible)}
      >
        <Ionicons
          name={filterVisible ? 'options' : 'options-outline'}
          size={22}
          color="#555"
        />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },

  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    letterSpacing: 0.5,
  },

  filterButton: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: '#f8fafc',
    justifyContent: 'center',
    alignItems: 'center',
    width: 40,
    height: 40,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
});
