import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
} from 'react-native-reanimated';
import { useLocalization } from '../context/LocalizationContext';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface RatingModalProps {
  visible: boolean;
  onClose: () => void;
  onRating: (rating: number) => void;
}

export const RatingModal: React.FC<RatingModalProps> = ({
  visible,
  onClose,
  onRating,
}) => {
  const { t } = useLocalization();
  const [selectedRating, setSelectedRating] = useState<number>(0);
  const [hoveredStar, setHoveredStar] = useState<number>(0);

  const handleStarPress = (rating: number) => {
    setSelectedRating(rating);

    // Add a small delay for visual feedback
    setTimeout(() => {
      onRating(rating);
    }, 300);
  };

  const renderStar = (index: number) => {
    const isFilled = index <= (hoveredStar || selectedRating);
    const scale = useSharedValue(1);

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: scale.value }],
    }));

    const handlePress = () => {
      scale.value = withSequence(
        withSpring(1.3, { duration: 150 }),
        withSpring(1, { duration: 150 })
      );
      handleStarPress(index);
    };

    return (
      <TouchableOpacity
        key={index}
        onPress={handlePress}
        onPressIn={() => setHoveredStar(index)}
        onPressOut={() => setHoveredStar(0)}
        style={styles.starButton}
        activeOpacity={0.7}
      >
        <Animated.View style={animatedStyle}>
          <Ionicons
            name={isFilled ? 'star' : 'star-outline'}
            size={40}
            color={isFilled ? '#FFD700' : '#E0E0E0'}
          />
        </Animated.View>
      </TouchableOpacity>
    );
  };

  const getRatingText = (rating: number) => {
    switch (rating) {
      case 1:
        return t('rating.terrible');
      case 2:
        return t('rating.poor');
      case 3:
        return t('rating.okay');
      case 4:
        return t('rating.good');
      case 5:
        return t('rating.excellent');
      default:
        return t('rating.tapToRate');
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
    >
      <Animated.View
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(200)}
        style={styles.overlay}
      >
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={onClose}
        />

        <Animated.View
          entering={SlideInDown.duration(400).springify()}
          exiting={SlideOutDown.duration(300)}
          style={styles.modalContainer}
        >
          {/* Doctor Image */}
          <View style={styles.imageContainer}>
            <Image
              source={require('../../assets/images/doctor.png')}
              style={styles.doctorImage}
              resizeMode="contain"
            />
          </View>

          {/* Content */}
          <View style={styles.content}>
            <Text style={styles.title}>{t('rating.enjoyingApp')}</Text>

            <Text style={styles.subtitle}>{t('rating.helpUsImprove')}</Text>

            {/* Star Rating */}
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map(renderStar)}
            </View>

            {/* Buttons */}
            <View style={styles.buttonsContainer}>
              <TouchableOpacity
                style={styles.laterButton}
                onPress={onClose}
                activeOpacity={0.7}
              >
                <Text style={styles.laterButtonText}>
                  {t('rating.maybeLater')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    width: SCREEN_WIDTH - 40,
    maxWidth: 400,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
  },
  imageContainer: {
    alignItems: 'center',
    paddingTop: 30,
    paddingBottom: 20,
    backgroundColor: '#F8F9FA',
  },
  doctorImage: {
    width: 120,
    height: 120,
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A1A1A',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 30,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    gap: 8,
  },
  starButton: {
    padding: 0,
  },
  ratingText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2196F3',
    textAlign: 'center',
    marginBottom: 30,
    minHeight: 25,
  },
  buttonsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  laterButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  laterButtonText: {
    fontSize: 16,
    color: '#999999',
    fontWeight: '500',
  },
});
