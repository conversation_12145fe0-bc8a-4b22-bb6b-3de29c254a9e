import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { useLocalization } from '../context/LocalizationContext';
import { MultiImageStep } from '../hooks/useMultiImageScan';

interface ScanningGuidanceHeaderProps {
  currentStep: MultiImageStep;
  totalSteps: number;
  completedCount: number;
  isProcessing?: boolean;
}

const { width } = Dimensions.get('window');

// Helper functions for step colors and icons
const getStepColor = (purpose: string): string => {
  switch (purpose) {
    case 'ingredients':
      return '#4CAF50';
    case 'nutrition':
      return '#FF9800';
    case 'general':
      return '#2196F3';
    default:
      return '#2196F3';
  }
};

const getStepIcon = (purpose: string): string => {
  switch (purpose) {
    case 'ingredients':
      return 'list-outline';
    case 'nutrition':
      return 'nutrition-outline';
    case 'general':
      return 'document-text-outline';
    default:
      return 'document-text-outline';
  }
};

export default function ScanningGuidanceHeader({
  currentStep,
  totalSteps,
  completedCount,
  isProcessing = false,
}: ScanningGuidanceHeaderProps) {
  const { t } = useLocalization();

  const stepColor = getStepColor(currentStep.purpose);
  const stepIcon = getStepIcon(currentStep.purpose);

  return (
    <View style={styles.container}>
      {/* Step Progress Indicator */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${(completedCount / totalSteps) * 100}%`,
                backgroundColor: stepColor,
              },
            ]}
          />
        </View>
        <Text style={styles.progressText}>
          {completedCount}/{totalSteps}
        </Text>
      </View>

      {/* Current Step Information */}
      <View style={styles.stepContainer}>
        <View style={styles.stepHeader}>
          <View style={[styles.stepIcon, { backgroundColor: stepColor }]}>
            <Ionicons name={stepIcon as any} size={20} color="white" />
          </View>
          <View style={styles.stepInfo}>
            <Text style={styles.stepTitle}>{currentStep.title}</Text>
            <Text style={styles.stepNumber}>
              {t('scan.step')} {currentStep.step} {t('scan.of')} {totalSteps}
            </Text>
          </View>
        </View>
        
        {/* Step Description */}
        <Text style={styles.stepDescription}>
          {currentStep.description}
        </Text>

        {/* Contextual Tips */}
        <View style={styles.tipsContainer}>
          <Ionicons name="bulb-outline" size={16} color="#FFD700" />
          <Text style={styles.tipText}>
            {getContextualTip(currentStep.purpose, t)}
          </Text>
        </View>
      </View>
    </View>
  );
}

// Helper function to get contextual tips based on step purpose
function getContextualTip(purpose: string, t: any): string {
  switch (purpose) {
    case 'ingredients':
      return t('scan.tip.ingredients');
    case 'nutrition':
      return t('scan.tip.nutrition');
    case 'general':
      return t('scan.tip.general');
    default:
      return t('scan.tip.general');
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
    paddingTop: 10,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 12,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    minWidth: 30,
  },
  stepContainer: {
    gap: 12,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  stepIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepInfo: {
    flex: 1,
  },
  stepTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 2,
  },
  stepNumber: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontWeight: '500',
  },
  stepDescription: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 15,
    lineHeight: 22,
    marginLeft: 52, // Align with step info
  },
  tipsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.15)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    marginLeft: 52, // Align with step info
    gap: 8,
  },
  tipText: {
    color: '#FFD700',
    fontSize: 13,
    fontWeight: '500',
    flex: 1,
  },
});
