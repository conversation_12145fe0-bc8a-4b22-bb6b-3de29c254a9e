import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withRepeat,
  withSequence,
  interpolate,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../context/LocalizationContext';
import { MultiImageStep } from '../hooks/useMultiImageScan';

interface CameraScanningOverlayProps {
  currentStep: MultiImageStep;
  totalSteps: number;
  completedCount: number;
  isProcessing: boolean;
  showGuidance: boolean;
  isCapturing?: boolean;
  captureSuccess?: boolean;
}

const { width } = Dimensions.get('window');
const SCAN_AREA_SIZE = Math.min(width * 0.85, 320);

// Helper function for step colors
const getStepColor = (purpose: string): string => {
  switch (purpose) {
    case 'ingredients':
      return '#4CAF50';
    case 'nutrition':
      return '#FF9800';
    case 'general':
      return '#2196F3';
    default:
      return '#2196F3';
  }
};

export default function CameraScanningOverlay({
  currentStep,
  totalSteps,
  completedCount,
  isProcessing,
  showGuidance,
  isCapturing = false,
  captureSuccess = false,
}: CameraScanningOverlayProps) {
  const { t } = useLocalization();

  // Animation values
  const cornerPulse = useSharedValue(0);
  const scanLineAnimation = useSharedValue(0);
  const captureAnimation = useSharedValue(0);
  const successAnimation = useSharedValue(0);

  const stepColor = getStepColor(currentStep.purpose);

  useEffect(() => {
    if (!isProcessing && !isCapturing) {
      // Elegant corner pulse animation
      cornerPulse.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 2000 }),
          withTiming(0, { duration: 2000 })
        ),
        -1,
        false
      );

      // Smooth scanning line animation
      scanLineAnimation.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 3000 }),
          withTiming(0, { duration: 3000 })
        ),
        -1,
        false
      );
    } else {
      cornerPulse.value = withTiming(0, { duration: 300 });
      scanLineAnimation.value = withTiming(0, { duration: 300 });
    }
  }, [isProcessing, isCapturing]);

  useEffect(() => {
    if (isCapturing) {
      captureAnimation.value = withTiming(1, { duration: 200 });
    } else {
      captureAnimation.value = withTiming(0, { duration: 200 });
    }
  }, [isCapturing]);

  useEffect(() => {
    if (captureSuccess) {
      successAnimation.value = withSequence(
        withTiming(1, { duration: 300 }),
        withTiming(0, { duration: 300 })
      );
    }
  }, [captureSuccess]);

  const cornerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(cornerPulse.value, [0, 1], [0.6, 1]),
    borderColor: stepColor,
    shadowColor: stepColor,
    shadowOpacity: interpolate(cornerPulse.value, [0, 1], [0.3, 0.8]),
  }));

  const scanLineAnimatedStyle = useAnimatedStyle(() => ({
    backgroundColor: stepColor,
    opacity: interpolate(
      scanLineAnimation.value,
      [0, 0.2, 0.8, 1],
      [0, 1, 1, 0]
    ),
    transform: [
      {
        translateY: interpolate(
          scanLineAnimation.value,
          [0, 1],
          [-SCAN_AREA_SIZE * 0.4, SCAN_AREA_SIZE * 0.4]
        ),
      },
    ],
    shadowColor: stepColor,
    shadowOpacity: interpolate(
      scanLineAnimation.value,
      [0, 0.5, 1],
      [0, 0.8, 0]
    ),
  }));

  const captureOverlayStyle = useAnimatedStyle(() => ({
    opacity: captureAnimation.value,
    backgroundColor: `rgba(255, 255, 255, ${captureAnimation.value * 0.3})`,
  }));

  const successOverlayStyle = useAnimatedStyle(() => ({
    opacity: successAnimation.value,
    transform: [
      { scale: interpolate(successAnimation.value, [0, 1], [0.8, 1]) },
    ],
  }));

  return (
    <View style={styles.overlay}>
      {/* Elegant Corner Indicators */}
      <Animated.View
        style={[styles.corner, styles.cornerTL, cornerAnimatedStyle]}
      />
      <Animated.View
        style={[styles.corner, styles.cornerTR, cornerAnimatedStyle]}
      />
      <Animated.View
        style={[styles.corner, styles.cornerBL, cornerAnimatedStyle]}
      />
      <Animated.View
        style={[styles.corner, styles.cornerBR, cornerAnimatedStyle]}
      />

      {/* Sophisticated Scanning Line */}
      {!isProcessing && !isCapturing && (
        <Animated.View style={[styles.scanLine, scanLineAnimatedStyle]} />
      )}

      {/* Capture Feedback Overlay */}
      {isCapturing && (
        <Animated.View style={[styles.captureOverlay, captureOverlayStyle]} />
      )}

      {/* Success Feedback */}
      {captureSuccess && (
        <Animated.View style={[styles.successOverlay, successOverlayStyle]}>
          <View style={styles.successIndicator}>
            <Ionicons name="checkmark-circle" size={48} color="#4CAF50" />
            <Text style={styles.successText}>{t('scan.captureSuccess')}</Text>
          </View>
        </Animated.View>
      )}

      {/* Processing Indicator */}
      {(isProcessing || isCapturing) && (
        <View style={styles.processingOverlay}>
          <View style={styles.processingIndicator}>
            <View style={styles.processingDots}>
              {[0, 1, 2].map((index) => (
                <ProcessingDot
                  key={index}
                  delay={index * 200}
                  color={stepColor}
                />
              ))}
            </View>
            <Text style={styles.processingText}>
              {isCapturing ? t('scan.capturing') : t('scan.processingImage')}
            </Text>
            <Text style={styles.processingSubtext}>
              {isCapturing
                ? t('scan.pleaseWait')
                : t('scan.processingImageMessage')}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
}

// Animated processing dot component
function ProcessingDot({ delay, color }: { delay: number; color: string }) {
  const dotAnimation = useSharedValue(0);

  useEffect(() => {
    dotAnimation.value = withRepeat(
      withSequence(
        withTiming(0, { duration: delay }),
        withTiming(1, { duration: 400 }),
        withTiming(0, { duration: 400 })
      ),
      -1,
      false
    );
  }, [delay]);

  const dotAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(dotAnimation.value, [0, 1], [0.3, 1]),
    transform: [{ scale: interpolate(dotAnimation.value, [0, 1], [0.8, 1.2]) }],
  }));

  return (
    <Animated.View
      style={[
        styles.processingDot,
        { backgroundColor: color },
        dotAnimatedStyle,
      ]}
    />
  );
}

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  corner: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderWidth: 3,
    shadowOffset: { width: 0, height: 0 },
    shadowRadius: 8,
    elevation: 8,
  },
  cornerTL: {
    top: 16,
    left: 16,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    borderTopLeftRadius: 8,
  },
  cornerTR: {
    top: 16,
    right: 16,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
    borderTopRightRadius: 8,
  },
  cornerBL: {
    bottom: 16,
    left: 16,
    borderRightWidth: 0,
    borderTopWidth: 0,
    borderBottomLeftRadius: 8,
  },
  cornerBR: {
    bottom: 16,
    right: 16,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderBottomRightRadius: 8,
  },
  scanLine: {
    position: 'absolute',
    left: 20,
    right: 20,
    height: 3,
    borderRadius: 1.5,
    shadowOffset: { width: 0, height: 0 },
    shadowRadius: 8,
    elevation: 8,
  },
  captureOverlay: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 8,
  },
  successOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
  },
  successIndicator: {
    alignItems: 'center',
    gap: 8,
  },
  successText: {
    color: '#4CAF50',
    fontSize: 16,
    fontWeight: '600',
  },
  processingOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 8,
  },
  processingIndicator: {
    alignItems: 'center',
    gap: 12,
  },
  processingDots: {
    flexDirection: 'row',
    gap: 8,
  },
  processingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  processingText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  processingSubtext: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 4,
  },
});
