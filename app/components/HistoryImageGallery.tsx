import React, { useState, useEffect } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  StyleSheet,
  Text,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withSpring,
} from 'react-native-reanimated';
import { useLocalization } from '../context/LocalizationContext';
import { loadImageFromStorage, StoredImageInfo } from '../utils/ImageStorage';

interface HistoryImageGalleryProps {
  images: StoredImageInfo[];
  onImagePress?: (images: StoredImageInfo[], initialIndex: number) => void;
}

interface ImageThumbnailProps {
  imageInfo: StoredImageInfo;
  onPress: (images: StoredImageInfo[], initialIndex: number) => void;
  index: number;
  allImages: StoredImageInfo[];
}

const ImageThumbnail: React.FC<ImageThumbnailProps> = ({
  imageInfo,
  onPress,
  index,
  allImages,
}) => {
  const { t } = useLocalization();
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // Animation values
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);

  useEffect(() => {
    // Animate in with a slight delay based on index
    const delay = index * 100;
    setTimeout(() => {
      scale.value = withSpring(1, { damping: 15, stiffness: 150 });
      opacity.value = withTiming(1, { duration: 300 });
    }, delay);

    // Load the image
    loadImageFromStorage(imageInfo.filename)
      .then((uri) => {
        if (uri) {
          setImageUri(uri);
        } else {
          setError(true);
        }
      })
      .catch(() => {
        setError(true);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [imageInfo.filename, index, scale, opacity]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePress = () => {
    if (imageUri && !error) {
      // Add press animation
      scale.value = withTiming(0.95, { duration: 100 }, () => {
        scale.value = withSpring(1, { damping: 15, stiffness: 150 });
      });
      onPress(allImages, index);
    }
  };

  const getImageTypeIcon = () => {
    switch (imageInfo.type) {
      case 'barcode':
        return 'barcode-outline';
      case 'ingredient':
        return 'list-outline';
      case 'nutrition':
        return 'nutrition-outline';
      case 'label':
        return 'document-text-outline';
      default:
        return 'document-text-outline';
    }
  };

  const getImageTypeColor = () => {
    switch (imageInfo.type) {
      case 'barcode':
        return '#007AFF';
      case 'ingredient':
        return '#4CAF50';
      case 'nutrition':
        return '#FF9800';
      case 'label':
        return '#2196F3';
      default:
        return '#4CAF50';
    }
  };

  return (
    <Animated.View style={[styles.thumbnailContainer, animatedStyle]}>
      <TouchableOpacity
        style={styles.thumbnail}
        onPress={handlePress}
        activeOpacity={0.8}
        disabled={loading || error}
      >
        {loading && (
          <View style={styles.thumbnailLoading}>
            <ActivityIndicator size="small" color="#666" />
          </View>
        )}

        {error && (
          <View style={styles.thumbnailError}>
            <Ionicons name="image-outline" size={24} color="#999" />
          </View>
        )}

        {!loading && !error && imageUri && (
          <>
            <Image
              source={{ uri: imageUri }}
              style={styles.thumbnailImage}
              resizeMode="cover"
            />
            <View style={styles.thumbnailOverlay}>
              <View
                style={[
                  styles.typeIndicator,
                  { backgroundColor: getImageTypeColor() },
                ]}
              >
                <Ionicons name={getImageTypeIcon()} size={12} color="white" />
              </View>
              {/* Step indicator for multi-image scans */}
              {imageInfo.step && (
                <View style={styles.stepIndicator}>
                  <Text style={styles.stepText}>{imageInfo.step}</Text>
                </View>
              )}
            </View>
          </>
        )}
      </TouchableOpacity>

      {/* Image type label */}
      <Text style={styles.typeLabel} numberOfLines={1}>
        {imageInfo.type === 'barcode'
          ? t('history.barcodeImage')
          : t('history.ingredientImage')}
      </Text>
    </Animated.View>
  );
};

const HistoryImageGallery: React.FC<HistoryImageGalleryProps> = ({
  images,
  onImagePress,
}) => {
  const { t } = useLocalization();

  if (!images || images.length === 0) {
    return (
      <View style={styles.noImagesContainer}>
        <Ionicons name="images-outline" size={20} color="#999" />
        <Text style={styles.noImagesText}>
          {t('history.noImagesAvailable')}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.imageGrid}>
        {images.map((imageInfo, index) => (
          <ImageThumbnail
            key={`${imageInfo.filename}-${index}`}
            imageInfo={imageInfo}
            onPress={onImagePress || (() => {})}
            index={index}
            allImages={images}
          />
        ))}
      </View>
      <Text style={styles.tapHint}>{t('history.tapToViewFullSize')}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: '#666',
    marginBottom: 12,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  thumbnailContainer: {
    alignItems: 'center',
  },
  thumbnail: {
    width: 80,
    height: 80,
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    overflow: 'hidden',
    position: 'relative',
    borderWidth: 2,
    borderColor: '#E9ECEF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  thumbnailLoading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  thumbnailError: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
  thumbnailOverlay: {
    position: 'absolute',
    top: 4,
    right: 4,
    flexDirection: 'column',
    gap: 4,
  },
  typeIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  stepIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  typeLabel: {
    fontSize: 9,
    color: '#666',
    marginTop: 6,
    textAlign: 'center',
    width: 80,
    fontWeight: '500',
    lineHeight: 12,
  },
  noImagesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  noImagesText: {
    fontSize: 12,
    color: '#999',
    marginLeft: 6,
    fontStyle: 'italic',
  },
  tapHint: {
    fontSize: 11,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
});

export default HistoryImageGallery;
