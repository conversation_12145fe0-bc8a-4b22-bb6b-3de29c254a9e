import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Modal,
  Animated,
  Dimensions,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useLocalization } from '../context/LocalizationContext';
import { useAvoidedAdditives } from '../context/AvoidedAdditivesContext';
import { useCustomLabels } from '../context/CustomLabelsContext';
import { useFavorites } from '../context/FavoritesContext';

interface LanguageDrawerProps {
  visible: boolean;
  onClose: () => void;
}

const { width } = Dimensions.get('window');
const DRAWER_WIDTH = Math.min(width * 0.8, 300);

export function LanguageDrawer({ visible, onClose }: LanguageDrawerProps) {
  const { language, setLanguage, t } = useLocalization();
  const router = useRouter();
  const { avoidedAdditives } = useAvoidedAdditives();
  const { labels } = useCustomLabels();
  const { favorites } = useFavorites();
  const [slideAnim] = React.useState(new Animated.Value(-DRAWER_WIDTH));
  const [backdropOpacity] = React.useState(new Animated.Value(0));

  React.useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: -DRAWER_WIDTH,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, slideAnim, backdropOpacity]);

  const changeLanguage = (lang: 'tr' | 'en') => {
    setLanguage(lang);
    onClose();
  };

  const handleNavigation = (path: string) => {
    router.push(path as any);
    onClose();
  };

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <Animated.View
          style={[styles.backdrop, { opacity: backdropOpacity }]}
          onTouchEnd={onClose}
        />
        <Animated.View
          style={[styles.drawer, { transform: [{ translateX: slideAnim }] }]}
        >
          <SafeAreaView style={styles.safeArea}>
            <View style={styles.header}>
              <Text style={styles.title}>{t('common.settings')}</Text>
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.content}
              showsVerticalScrollIndicator={false}
            >
              {/* Language Section */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>{t('common.language')}</Text>

                <View style={styles.languageOptions}>
                  <TouchableOpacity
                    style={[
                      styles.languageOption,
                      language === 'tr' && styles.activeOption,
                    ]}
                    onPress={() => changeLanguage('tr')}
                  >
                    <Text
                      style={[
                        styles.languageText,
                        language === 'tr' && styles.activeText,
                      ]}
                    >
                      Türkçe
                    </Text>
                    {language === 'tr' && (
                      <Ionicons name="checkmark" size={20} color="#2196F3" />
                    )}
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.languageOption,
                      language === 'en' && styles.activeOption,
                    ]}
                    onPress={() => changeLanguage('en')}
                  >
                    <Text
                      style={[
                        styles.languageText,
                        language === 'en' && styles.activeText,
                      ]}
                    >
                      English
                    </Text>
                    {language === 'en' && (
                      <Ionicons name="checkmark" size={20} color="#2196F3" />
                    )}
                  </TouchableOpacity>
                </View>
              </View>

              {/* Personalization Section */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>
                  {t('common.personalization')}
                </Text>

                {/* Favorites */}
                <TouchableOpacity
                  style={styles.personalizationItem}
                  onPress={() => handleNavigation('/favorites')}
                  activeOpacity={0.7}
                >
                  <View
                    style={[
                      styles.iconContainer,
                      { backgroundColor: '#fef2f2' },
                    ]}
                  >
                    <Ionicons name="heart" size={20} color="#dc2626" />
                  </View>
                  <View style={styles.itemContent}>
                    <Text style={styles.itemTitle}>
                      {t('favorites.viewFavorites')}
                    </Text>
                    <Text style={styles.itemSubtitle}>
                      {favorites.size} {t('common.items')}
                    </Text>
                  </View>
                  <Ionicons name="chevron-forward" size={18} color="#94a3b8" />
                </TouchableOpacity>

                {/* Custom Labels */}
                <TouchableOpacity
                  style={styles.personalizationItem}
                  onPress={() => handleNavigation('/labels')}
                  activeOpacity={0.7}
                >
                  <View
                    style={[
                      styles.iconContainer,
                      { backgroundColor: '#eff6ff' },
                    ]}
                  >
                    <Ionicons name="pricetags" size={20} color="#3b82f6" />
                  </View>
                  <View style={styles.itemContent}>
                    <Text style={styles.itemTitle}>
                      {t('common.customLabels')}
                    </Text>
                    <Text style={styles.itemSubtitle}>
                      {labels.length} {t('common.labels')}
                    </Text>
                  </View>
                  <Ionicons name="chevron-forward" size={18} color="#94a3b8" />
                </TouchableOpacity>

                {/* Avoided Additives */}
                <TouchableOpacity
                  style={styles.personalizationItem}
                  onPress={() => handleNavigation('/avoided')}
                  activeOpacity={0.7}
                >
                  <View
                    style={[
                      styles.iconContainer,
                      { backgroundColor: '#fef2f2' },
                    ]}
                  >
                    <Ionicons name="close-circle" size={20} color="#dc2626" />
                  </View>
                  <View style={styles.itemContent}>
                    <Text style={styles.itemTitle}>{t('common.avoided')}</Text>
                    <Text style={styles.itemSubtitle}>
                      {avoidedAdditives.size} {t('common.additives')}
                    </Text>
                  </View>
                  <Ionicons name="chevron-forward" size={18} color="#94a3b8" />
                </TouchableOpacity>
              </View>

              {/* Footer */}
              <View style={styles.footer}>
                <Text style={styles.footerText}>
                  {t('common.personalizationFooter')}
                </Text>
              </View>
            </ScrollView>
          </SafeAreaView>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000',
  },
  drawer: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    width: DRAWER_WIDTH,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 5,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    color: '#64748b',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  languageOptions: {
    marginTop: 8,
  },
  languageOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#f8fafc',
  },
  activeOption: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(33, 150, 243, 0.3)',
  },
  languageText: {
    fontSize: 16,
    color: '#333',
  },
  activeText: {
    fontWeight: '600',
    color: '#2196F3',
  },
  personalizationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 2,
  },
  itemSubtitle: {
    fontSize: 12,
    color: '#64748b',
  },
  footer: {
    padding: 20,
    backgroundColor: '#f8fafc',
    marginTop: 8,
  },
  footerText: {
    fontSize: 11,
    color: '#94a3b8',
    textAlign: 'center',
    lineHeight: 16,
  },
});
