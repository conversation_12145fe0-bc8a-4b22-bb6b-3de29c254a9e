import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../context/LocalizationContext';
import { useCommunityContributions, Review } from '../context/CommunityContributionsContext';

interface ReviewsSectionProps {
  additiveCode: string;
}

const ReviewsSection: React.FC<ReviewsSectionProps> = ({ additiveCode }) => {
  const { t } = useLocalization();
  const { submitReview, getReviewsForAdditive, isLoading } = useCommunityContributions();
  
  const [reviews, setReviews] = useState<Review[]>([]);
  const [showAddReview, setShowAddReview] = useState(false);
  const [comment, setComment] = useState('');
  const [rating, setRating] = useState(0);
  const [loadingReviews, setLoadingReviews] = useState(true);
  
  // Load reviews
  useEffect(() => {
    loadReviews();
  }, [additiveCode]);
  
  const loadReviews = async () => {
    setLoadingReviews(true);
    const fetchedReviews = await getReviewsForAdditive(additiveCode);
    setReviews(fetchedReviews);
    setLoadingReviews(false);
  };
  
  // Reset form
  const resetForm = () => {
    setComment('');
    setRating(0);
    setShowAddReview(false);
  };
  
  // Handle submission
  const handleSubmitReview = async () => {
    if (!comment.trim()) {
      Alert.alert(
        t('community.error'),
        t('community.commentRequired')
      );
      return;
    }
    
    const review: Review = {
      additive_code: additiveCode,
      rating: rating > 0 ? rating : undefined,
      comment: comment.trim(),
    };
    
    const success = await submitReview(review);
    
    if (success) {
      Alert.alert(
        t('community.thankYou'),
        t('community.reviewSubmitted'),
        [{ text: t('common.ok'), onPress: () => {
          resetForm();
          loadReviews(); // Reload reviews
        }}]
      );
    } else {
      Alert.alert(
        t('community.error'),
        t('community.errorSubmittingReview')
      );
    }
  };
  
  // Render star rating
  const renderStarRating = (value: number, interactive = false) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => interactive && setRating(star)}
            disabled={!interactive}
          >
            <Ionicons
              name={star <= value ? 'star' : 'star-outline'}
              size={interactive ? 28 : 16}
              color={star <= value ? '#FFC107' : '#ccc'}
              style={{ marginRight: 4 }}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };
  
  // Render a review item
  const renderReviewItem = ({ item }: { item: Review }) => {
    // Format date
    const date = item.created_at 
      ? new Date(item.created_at).toLocaleDateString() 
      : '';
    
    return (
      <View style={styles.reviewItem}>
        {item.rating && (
          <View style={styles.reviewRating}>
            {renderStarRating(item.rating)}
          </View>
        )}
        <Text style={styles.reviewComment}>{item.comment}</Text>
        <Text style={styles.reviewDate}>{date}</Text>
      </View>
    );
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{t('community.communityReviews')}</Text>
        {!showAddReview && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowAddReview(true)}
          >
            <Ionicons name="add" size={20} color="#2196F3" />
            <Text style={styles.addButtonText}>{t('community.addReview')}</Text>
          </TouchableOpacity>
        )}
      </View>
      
      {showAddReview && (
        <View style={styles.addReviewContainer}>
          <Text style={styles.ratingLabel}>{t('community.rating')}</Text>
          {renderStarRating(rating, true)}
          
          <Text style={styles.commentLabel}>{t('community.comment')} *</Text>
          <TextInput
            style={styles.commentInput}
            value={comment}
            onChangeText={setComment}
            placeholder={t('community.commentPlaceholder')}
            placeholderTextColor="#999"
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={resetForm}
            >
              <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleSubmitReview}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.submitButtonText}>{t('community.submit')}</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      )}
      
      {loadingReviews ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#2196F3" />
          <Text style={styles.loadingText}>{t('common.loading')}</Text>
        </View>
      ) : reviews.length > 0 ? (
        <FlatList
          data={reviews}
          renderItem={renderReviewItem}
          keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
          style={styles.reviewsList}
          contentContainerStyle={styles.reviewsListContent}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="chatbubble-outline" size={24} color="#ccc" />
          <Text style={styles.emptyText}>{t('community.noReviews')}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 6,
  },
  addButtonText: {
    color: '#2196F3',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  addReviewContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 15,
  },
  ratingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  commentLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  commentInput: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    minHeight: 80,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
  },
  submitButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 15,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
  },
  reviewsList: {
    maxHeight: 300,
  },
  reviewsListContent: {
    paddingBottom: 10,
  },
  reviewItem: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
  },
  reviewRating: {
    marginBottom: 8,
  },
  reviewComment: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
    lineHeight: 20,
  },
  reviewDate: {
    fontSize: 12,
    color: '#888',
    textAlign: 'right',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    marginTop: 10,
    color: '#666',
  },
});

export default ReviewsSection;
