import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useMultiImageScan } from '../hooks/useMultiImageScan';
import { useLocalization } from '../context/LocalizationContext';

export default function MultiImageTest() {
  const { t } = useLocalization();
  const multiImageScan = useMultiImageScan();

  const handleTestCapture = async () => {
    // Simulate capturing an image for testing
    const mockImageUri = 'test://image.jpg';
    const success = await multiImageScan.captureStepImage(
      mockImageUri,
      multiImageScan.currentStep
    );
    
    console.log('Test capture result:', success);
    console.log('Current step:', multiImageScan.currentStep);
    console.log('Completed steps:', multiImageScan.getCompletedSteps().length);
  };

  const currentStep = multiImageScan.getCurrentStep();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Multi-Image Scan Test</Text>
      
      <Text style={styles.info}>
        Current Step: {multiImageScan.currentStep} / {multiImageScan.totalSteps}
      </Text>
      
      <Text style={styles.info}>
        Completed: {multiImageScan.completedCount}
      </Text>
      
      {currentStep && (
        <View style={styles.stepInfo}>
          <Text style={styles.stepTitle}>{currentStep.title}</Text>
          <Text style={styles.stepDescription}>{currentStep.description}</Text>
          <Text style={styles.stepTip}>{currentStep.tip}</Text>
        </View>
      )}
      
      <TouchableOpacity style={styles.button} onPress={handleTestCapture}>
        <Text style={styles.buttonText}>Test Capture</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.button} onPress={multiImageScan.nextStep}>
        <Text style={styles.buttonText}>Next Step</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.button} onPress={multiImageScan.resetScan}>
        <Text style={styles.buttonText}>Reset</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  info: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666',
  },
  stepInfo: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginVertical: 12,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  stepTip: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 12,
    borderRadius: 8,
    marginVertical: 4,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
  },
});
