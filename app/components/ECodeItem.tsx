import { useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { useLocalization } from '../context/LocalizationContext';
import { localAdditivesService } from '../utils/LocalAdditivesService';
import { useFavorites } from '../context/FavoritesContext';
import { useAvoidedAdditives } from '../context/AvoidedAdditivesContext';
import { useCustomLabels } from '../context/CustomLabelsContext';
import { useRouter } from 'expo-router';
import { useToast } from '../context/ToastContext';
import ReviewsSection from './ReviewsSection';

// No longer needed
// const SCREEN_HEIGHT = Platform.OS === 'web' ? window.innerHeight : Dimensions.get('window').height;

const ECodeItem = ({
  code,
  isExpanded,
  onToggle,
}: {
  code: string;
  isExpanded: boolean;
  onToggle: () => void;
}) => {
  const router = useRouter();
  const { language, t } = useLocalization();
  const { toggleFavorite, isFavorite } = useFavorites();
  const { toggleAvoidedAdditive, isAvoided } = useAvoidedAdditives();
  const { getLabelsForAdditive } = useCustomLabels();
  const { showToast } = useToast();

  // Get the specific eCodeInfo from the local additives service
  const eCodeInfo = localAdditivesService.getAdditiveByCode(code);

  // Check if this code is in favorites and avoided list
  const isFavorited = isFavorite(code);
  const isAvoidedAdditive = isAvoided(code);

  // Get custom labels for this additive
  const customLabels = getLabelsForAdditive(code);

  const animation = useSharedValue(isExpanded ? 1 : 0);
  const cardScale = useSharedValue(1);

  useEffect(() => {
    animation.value = withSpring(isExpanded ? 1 : 0, {
      damping: 18,
      stiffness: 120,
    });
  }, [isExpanded]);

  const handlePress = () => {
    // Add subtle press animation
    cardScale.value = withTiming(0.98, { duration: 100 }, () => {
      cardScale.value = withTiming(1, { duration: 200 });
    });
    onToggle();
  };

  // Handle favorite button press
  const handleFavoritePress = async (event: any) => {
    event.stopPropagation(); // Prevent triggering the parent TouchableOpacity

    try {
      // Store current state before toggling
      const wasInFavorites = isFavorited;

      // Toggle favorite status
      await toggleFavorite(code);

      // Show toast notification
      showToast(
        wasInFavorites
          ? t('common.removedFromFavorites')
          : t('common.addedToFavorites'),
        wasInFavorites ? 'info' : 'success',
        2000
      );
    } catch (error) {
      console.error('Error toggling favorite:', error);
      showToast(t('common.error'), 'error', 2000);
    }
  };

  // Handle avoided button press
  const handleAvoidedPress = async (event: any) => {
    event.stopPropagation(); // Prevent triggering the parent TouchableOpacity

    try {
      // Store current state before toggling
      const wasAvoided = isAvoidedAdditive;

      // Toggle avoided status
      await toggleAvoidedAdditive(code);

      // Show toast notification
      showToast(
        wasAvoided
          ? t('common.removedFromAvoided')
          : t('common.addedToAvoided'),
        wasAvoided ? 'info' : 'error',
        2000
      );
    } catch (error) {
      console.error('Error toggling avoided status:', error);
      showToast(t('common.error'), 'error', 2000);
    }
  };

  const animatedStyle = useAnimatedStyle(() => {
    const rotate = interpolate(animation.value, [0, 1], [0, 180]);
    return {
      transform: [{ rotate: `${rotate}deg` }],
    };
  });

  const cardAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: cardScale.value }],
    };
  });

  const contentStyle = useAnimatedStyle(() => {
    return {
      maxHeight: withSpring(isExpanded ? 1000 : 0, {
        damping: 18,
        stiffness: 120,
      }),
      opacity: withTiming(isExpanded ? 1 : 0, { duration: 250 }),
    };
  });

  if (!eCodeInfo) return null;

  const safetyLevel = eCodeInfo.safety;
  const safetyColor = getSafetyColor(safetyLevel);
  const halalStatus = eCodeInfo.halal || 'mushbooh'; // Default to mushbooh if not specified
  const halalColor = getHalalColor(halalStatus);

  // Get safety description based on level
  const getSafetyDescription = () => {
    switch (safetyLevel) {
      case 'safe':
        return t('common.safeDescription');
      case 'questionable':
        return t('common.questionableDescription');
      case 'harmful':
        return t('common.harmfulDescription');
      default:
        return '';
    }
  };

  // Get halal description based on status
  const getHalalDescription = () => {
    switch (halalStatus) {
      case 'halal':
        return t('common.halalDescription');
      case 'haram':
        return t('common.haramDescription');
      case 'mushbooh':
        return t('common.mushboohDescription');
      default:
        return '';
    }
  };

  return (
    <Animated.View style={cardAnimatedStyle}>
      <TouchableOpacity
        style={[
          styles.eCodeItem,
          isExpanded && styles.eCodeItemExpanded,
          { borderLeftWidth: 4, borderLeftColor: safetyColor },
        ]}
        onPress={handlePress}
        activeOpacity={0.9}
      >
        <View style={styles.eCodeHeader}>
          <View style={styles.codeContainer}>
            <View style={styles.codeBadge}>
              <Text style={styles.eCodeText}>{code}</Text>
            </View>
          </View>
          <View style={styles.headerRight}>
            <View
              style={[styles.safetyBadge, { backgroundColor: safetyColor }]}
            >
              <Text style={styles.safetyText}>
                {t(`common.${safetyLevel}Short`)}
              </Text>
            </View>
            <View style={[styles.halalBadge, { backgroundColor: halalColor }]}>
              <Text style={styles.halalText}>
                {t(`common.${halalStatus}Short`)}
              </Text>
            </View>
            <Animated.View style={[styles.chevronContainer, animatedStyle]}>
              <Ionicons name="chevron-down" size={22} color="#555" />
            </Animated.View>
          </View>
        </View>
        <Text style={styles.eCodeName} numberOfLines={1} ellipsizeMode="tail">
          {language === 'tr' ? eCodeInfo.name.TR : eCodeInfo.name.EN}
        </Text>

        <View style={styles.categoryRow}>
          <Ionicons name="folder-outline" size={16} color="#666" />
          <Text style={styles.category}>
            {language === 'tr' ? eCodeInfo.categoryTr : eCodeInfo.categoryEn}
          </Text>
        </View>

        {/* Display custom labels if any */}
        {customLabels.length > 0 && (
          <View style={styles.labelsContainer}>
            {customLabels.map((label) => (
              <View
                key={label.id}
                style={[
                  styles.labelBadge,
                  { backgroundColor: `${label.color}20` },
                ]}
              >
                <View
                  style={[styles.labelDot, { backgroundColor: label.color }]}
                />
                <Text style={styles.labelText}>{label.name}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Display avoided indicator if marked as avoided */}
        {isAvoidedAdditive && (
          <View style={styles.avoidedContainer}>
            <Ionicons name="alert-circle" size={16} color="#F44336" />
            <Text style={styles.avoidedText}>{t('common.avoided')}</Text>
          </View>
        )}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleFavoritePress}
            activeOpacity={0.7}
          >
            <Ionicons
              name={isFavorited ? 'heart' : 'heart-outline'}
              size={22}
              color={isFavorited ? '#FF6B6B' : '#777'}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleAvoidedPress}
            activeOpacity={0.7}
          >
            <Ionicons
              name={isAvoidedAdditive ? 'close-circle' : 'close-circle-outline'}
              size={22}
              color={isAvoidedAdditive ? '#F44336' : '#777'}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() =>
              router.push({
                pathname: '/labels',
                params: { code },
              })
            }
            activeOpacity={0.7}
          >
            <Ionicons name="pricetags-outline" size={22} color="#2196F3" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={(event) => {
              event.stopPropagation(); // Prevent triggering the parent TouchableOpacity
              router.push({
                pathname: '/report',
                params: { additiveCode: code },
              });
            }}
            activeOpacity={0.7}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="alert-circle-outline" size={22} color="#F44336" />
          </TouchableOpacity>
        </View>

        <Animated.View style={[styles.expandedContent, contentStyle]}>
          <View style={styles.divider} />

          <View style={styles.infoSection}>
            <View style={styles.infoTitleRow}>
              <Ionicons
                name="information-circle-outline"
                size={18}
                color="#555"
              />
              <Text style={styles.infoTitle}>{t('common.detailedInfo')}</Text>
            </View>
            <Text style={styles.infoText}>
              {language === 'tr'
                ? eCodeInfo.descriptionTr
                : eCodeInfo.descriptionEn}
            </Text>
          </View>

          <View style={styles.infoSection}>
            <View style={styles.infoTitleRow}>
              <Ionicons name="restaurant-outline" size={18} color="#555" />
              <Text style={styles.infoTitle}>{t('common.usageAreas')}</Text>
            </View>
            <Text style={styles.infoText}>
              {language === 'tr' ? eCodeInfo.usageTr : eCodeInfo.usageEn}
            </Text>
          </View>

          <View
            style={[
              styles.categorySection,
              { backgroundColor: `${safetyColor}15` },
            ]}
          >
            <View style={styles.infoTitleRow}>
              <Ionicons name="alert-circle-outline" size={18} color="#555" />
              <Text style={styles.infoTitle}>{t('common.safetySummary')}</Text>
            </View>
            <Text style={styles.safetyDescription}>
              {getSafetyDescription()}
            </Text>
          </View>

          <View
            style={[
              styles.categorySection,
              { backgroundColor: `${halalColor}15` },
            ]}
          >
            <View style={styles.infoTitleRow}>
              <Ionicons
                name="checkmark-circle-outline"
                size={18}
                color="#555"
              />
              <Text style={styles.infoTitle}>{t('common.halalSummary')}</Text>
            </View>
            <View style={styles.halalStatusRow}>
              <View
                style={[styles.halalBadge, { backgroundColor: halalColor }]}
              >
                <Text style={styles.halalText}>
                  {t(`common.${halalStatus}`)}
                </Text>
              </View>
            </View>
            <Text style={styles.safetyDescription}>
              {getHalalDescription()}
            </Text>
          </View>

          {/* Community Reviews Section */}
          <ReviewsSection additiveCode={code} />
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const getSafetyColor = (safety: 'safe' | 'questionable' | 'harmful') => {
  switch (safety) {
    case 'safe':
      return '#4CAF50';
    case 'questionable':
      return '#FF9800';
    case 'harmful':
      return '#F44336';
    default:
      return '#757575';
  }
};

const getHalalColor = (halal: 'halal' | 'haram' | 'mushbooh') => {
  switch (halal) {
    case 'halal':
      return '#4CAF50'; // Green
    case 'haram':
      return '#F44336'; // Red
    case 'mushbooh':
      return '#FF9800'; // Orange
    default:
      return '#757575'; // Gray
  }
};

const styles = StyleSheet.create({
  eCodeItem: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 12,
    marginBottom: 16,
    position: 'relative',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#f1f5f9',
    paddingBottom: 0,
  },
  eCodeItemExpanded: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 8,
    borderColor: '#e2e8f0',
  },
  eCodeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  codeContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 16,
    marginTop: 12,
  },
  actionButton: {},
  codeBadge: {
    backgroundColor: '#f1f5f9',
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 12,
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  eCodeText: {
    fontSize: 17,
    fontWeight: '700',
    color: '#1e293b',
    letterSpacing: 0.5,
  },
  eCodeName: {
    fontSize: 18,
    color: '#1e293b',
    fontWeight: '600',
    marginTop: 12,
    lineHeight: 24,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badgesContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: 4,
  },
  chevronContainer: {
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    backgroundColor: '#f8fafc',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  category: {
    fontSize: 14,
    color: '#64748b',
    marginLeft: 8,
    fontWeight: '500',
  },
  safetyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 1,
    minWidth: 50,
    alignItems: 'center',
  },
  safetyText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
    letterSpacing: 0.2,
  },
  halalBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 1,
    minWidth: 45,
    alignItems: 'center',
  },
  halalText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
    letterSpacing: 0.2,
  },
  halalStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  expandedContent: {
    marginTop: 16,
    overflow: 'hidden',
  },
  divider: {
    height: 1,
    backgroundColor: '#eaeaea',
    marginBottom: 16,
    marginTop: 4,
  },
  infoSection: {
    marginBottom: 20,
  },
  infoTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  categorySection: {
    marginBottom: 8,
    padding: 16,
    borderRadius: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 6,
  },
  infoText: {
    fontSize: 15,
    color: '#444',
    lineHeight: 22,
  },
  safetyDescription: {
    fontSize: 15,
    color: '#444',
    lineHeight: 22,
  },
  labelsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    gap: 6,
  },
  labelBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  labelDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  labelText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333',
  },
  avoidedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    backgroundColor: '#FFEBEE',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  avoidedText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#F44336',
    marginLeft: 4,
  },
});

export default ECodeItem;
