import React from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  View,
  Text,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useLocalization } from '../context/LocalizationContext';
import { MultiImageStep } from '../hooks/useMultiImageScan';

// Helper function for step colors
const getStepColor = (purpose: string): string => {
  switch (purpose) {
    case 'ingredients':
      return '#4CAF50';
    case 'nutrition':
      return '#FF9800';
    case 'general':
      return '#2196F3';
    default:
      return '#2196F3';
  }
};

interface MultiImageActionButtonsProps {
  currentStep: MultiImageStep;
  totalSteps: number;
  completedCount: number;
  isProcessing: boolean;
  isCapturing: boolean;
  allStepsCompleted: boolean;
  onTakePicture: () => void;
  onPickImage: () => void;
  onRetake: () => void;
  onNextStep: () => void;
  onFinishScanning: () => void;
}

export default function MultiImageActionButtons({
  currentStep,
  completedCount,
  isProcessing,
  isCapturing,
  allStepsCompleted,
  onTakePicture,
  onPickImage,
  onRetake,
  onNextStep,
  onFinishScanning,
}: MultiImageActionButtonsProps) {
  const { t } = useLocalization();

  const buttonScale = useSharedValue(1);
  const nextButtonOpacity = useSharedValue(0);
  const finishButtonOpacity = useSharedValue(0);

  // Check if any processing is happening
  const isAnyProcessing = isProcessing || isCapturing;

  React.useEffect(() => {
    // Show next button if current step is completed but not all steps
    if (currentStep.completed && !allStepsCompleted) {
      nextButtonOpacity.value = withTiming(1, { duration: 300 });
    } else {
      nextButtonOpacity.value = withTiming(0, { duration: 300 });
    }

    // Show finish button if at least one image is captured
    if (completedCount > 0) {
      finishButtonOpacity.value = withTiming(1, { duration: 300 });
    } else {
      finishButtonOpacity.value = withTiming(0, { duration: 300 });
    }
  }, [currentStep.completed, allStepsCompleted, completedCount]);

  const handleButtonPress = (action: () => void) => {
    // Don't allow button presses during processing
    if (isAnyProcessing) return;

    buttonScale.value = withSpring(0.95, { duration: 100 }, () => {
      buttonScale.value = withSpring(1, { duration: 100 });
    });
    action();
  };

  const primaryButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }],
    opacity: isAnyProcessing ? 0.5 : 1,
  }));

  const nextButtonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: nextButtonOpacity.value,
    transform: [
      { scale: nextButtonOpacity.value },
      { translateY: (1 - nextButtonOpacity.value) * 20 },
    ],
  }));

  const finishButtonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: finishButtonOpacity.value,
    transform: [
      { scale: finishButtonOpacity.value },
      { translateY: (1 - finishButtonOpacity.value) * 20 },
    ],
  }));

  const stepColor = getStepColor(currentStep.purpose);

  return (
    <View style={styles.container}>
      {/* Main Action Buttons - Hide when all steps are completed */}
      {!allStepsCompleted && (
        <View style={styles.actionButtons}>
          {/* Camera Button */}
          <Animated.View style={primaryButtonAnimatedStyle}>
            <TouchableOpacity
              disabled={isAnyProcessing}
              style={[
                styles.actionButton,
                styles.primaryButton,
                { backgroundColor: stepColor },
                isAnyProcessing && styles.disabledButton,
              ]}
              onPress={() => handleButtonPress(onTakePicture)}
              accessibilityLabel={
                isAnyProcessing
                  ? t('scan.imageBeingProcessed')
                  : t('scan.takePicture')
              }
              accessibilityHint={
                isAnyProcessing ? t('scan.doNotTakePhotos') : undefined
              }
            >
              {isCapturing ? (
                <ActivityIndicator size={32} color="white" />
              ) : (
                <Ionicons name="camera" size={32} color="white" />
              )}
            </TouchableOpacity>
          </Animated.View>

          {/* Gallery Button */}
          <Animated.View style={primaryButtonAnimatedStyle}>
            <TouchableOpacity
              disabled={isAnyProcessing}
              style={[
                styles.actionButton,
                styles.secondaryActionButton,
                { borderColor: stepColor },
                isAnyProcessing && styles.disabledButton,
              ]}
              onPress={() => handleButtonPress(onPickImage)}
              accessibilityLabel={
                isAnyProcessing
                  ? t('scan.imageBeingProcessed')
                  : t('scan.pickFromGallery')
              }
              accessibilityHint={
                isAnyProcessing ? t('scan.doNotTakePhotos') : undefined
              }
            >
              <Ionicons
                name="images"
                size={28}
                color={isAnyProcessing ? 'rgba(128, 128, 128, 0.5)' : stepColor}
              />
            </TouchableOpacity>
          </Animated.View>

          {/* Retake Button (only show if current step is completed) */}
          {currentStep.completed && (
            <TouchableOpacity
              disabled={isAnyProcessing}
              style={[
                styles.actionButton,
                styles.retakeButton,
                isAnyProcessing && styles.disabledButton,
              ]}
              onPress={() => handleButtonPress(onRetake)}
              accessibilityLabel={
                isAnyProcessing
                  ? t('scan.imageBeingProcessed')
                  : t('scan.retakeImage')
              }
            >
              <Ionicons
                name="refresh"
                size={24}
                color={isAnyProcessing ? 'rgba(255, 99, 71, 0.5)' : '#FF6347'}
              />
            </TouchableOpacity>
          )}
        </View>
      )}
      {/* Secondary Action Buttons */}
      <View style={styles.secondaryButtons}>
        {/* Next Step Button */}
        {currentStep.completed && !allStepsCompleted && (
          <Animated.View
            style={[styles.secondaryButton, nextButtonAnimatedStyle]}
          >
            <TouchableOpacity
              style={[styles.nextButton, { backgroundColor: stepColor }]}
              onPress={() => handleButtonPress(onNextStep)}
            >
              <Text style={styles.nextButtonText}>{t('scan.nextImage')}</Text>
              <Ionicons name="arrow-forward" size={18} color="white" />
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Finish Scanning Button - Show when at least one image is captured */}
        {completedCount > 0 && (
          <Animated.View
            style={[styles.secondaryButton, finishButtonAnimatedStyle]}
          >
            <TouchableOpacity
              style={[
                styles.finishButton,
                allStepsCompleted && styles.completedFinishButton,
                {
                  backgroundColor: allStepsCompleted
                    ? '#4CAF50'
                    : 'rgba(255, 255, 255, 0.15)',
                },
              ]}
              onPress={() => handleButtonPress(onFinishScanning)}
            >
              <Text
                style={[
                  styles.finishButtonText,
                  allStepsCompleted && styles.completedFinishButtonText,
                  {
                    color: allStepsCompleted
                      ? 'white'
                      : 'rgba(255, 255, 255, 0.9)',
                  },
                ]}
              >
                {allStepsCompleted
                  ? t('scan.reviewImages')
                  : t('scan.finishScanning')}
              </Text>
              <Ionicons
                name={allStepsCompleted ? 'checkmark-circle' : 'play-circle'}
                size={18}
                color={allStepsCompleted ? 'white' : 'rgba(255, 255, 255, 0.9)'}
              />
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Completion Message - Show when all steps are completed */}
        {allStepsCompleted && (
          <View style={styles.completionMessage}>
            <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
            <Text style={styles.completionText}>
              {t('scan.allStepsCompleted')}
            </Text>
          </View>
        )}
      </View>
      {/* Quality Indicator - Only show after image processing */}
      {/* {currentStep.completed &&
        currentStep.recognizedText &&
        currentStep.quality && (
          <View style={styles.qualityIndicator}>
            <Ionicons
              name={
                currentStep.quality === 'good' ? 'checkmark-circle' : 'warning'
              }
              size={16}
              color={currentStep.quality === 'good' ? '#4CAF50' : '#FF9800'}
            />
            <Text
              style={[
                styles.qualityText,
                {
                  color: currentStep.quality === 'good' ? '#4CAF50' : '#FF9800',
                },
              ]}
            >
              {currentStep.quality === 'good'
                ? t('scan.imageQualityGood')
                : t('scan.imageQualityPoor')}
            </Text>
          </View>
        )} */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  progressDotText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 24,
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  actionButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  primaryButton: {
    width: 76,
    height: 76,
    borderRadius: 38,
  },
  secondaryActionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderWidth: 2,
  },
  retakeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  disabledButton: {
    opacity: 0.5,
  },
  secondaryButtons: {
    alignItems: 'center',
    gap: 16,
    marginTop: 20,
  },
  secondaryButton: {
    alignItems: 'center',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 28,
    paddingVertical: 14,
    borderRadius: 28,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  nextButtonText: {
    color: 'white',
    fontSize: 15,
    fontWeight: '600',
  },
  finishButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 28,
    paddingVertical: 14,
    borderRadius: 28,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  finishButtonText: {
    fontSize: 15,
    fontWeight: '600',
  },
  qualityIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    paddingHorizontal: 18,
    paddingVertical: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 24,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  qualityText: {
    fontSize: 13,
    fontWeight: '600',
  },
  completedFinishButton: {
    transform: [{ scale: 1.05 }],
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  completedFinishButtonText: {
    fontSize: 16,
    fontWeight: '700',
  },
  completionMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: 'rgba(76, 175, 80, 0.15)',
    borderRadius: 24,
    gap: 8,
    borderWidth: 1,
    borderColor: 'rgba(76, 175, 80, 0.3)',
  },
  completionText: {
    color: '#4CAF50',
    fontSize: 14,
    fontWeight: '600',
  },
});
