import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../context/LocalizationContext';
import { FoodScore } from '../utils/FoodScoreCalculator';

interface ScoreBreakdownProps {
  score: FoodScore;
}

const ScoreBreakdown: React.FC<ScoreBreakdownProps> = ({ score }) => {
  const { t } = useLocalization();

  const getScoreColor = (scoreValue: number): string => {
    if (scoreValue >= 80) return '#4CAF50';
    if (scoreValue >= 60) return '#FFC107';
    if (scoreValue >= 40) return '#FF9800';
    return '#F44336';
  };

  const renderScoreSection = (
    title: string,
    scoreValue: number,
    weight: number,
    details: string[],
    icon: string
  ) => {
    const color = getScoreColor(scoreValue);

    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <Ionicons name={icon as any} size={24} color={color} />
            <Text style={styles.sectionTitle}>{title}</Text>
          </View>
          <View style={styles.scoreContainer}>
            <Text style={[styles.scoreText, { color }]}>{scoreValue}</Text>
            <Text style={styles.weightText}>({Math.round(weight * 100)}%)</Text>
          </View>
        </View>

        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <View
              style={[
                styles.progressBarFill,
                { width: `${scoreValue}%`, backgroundColor: color },
              ]}
            />
          </View>
        </View>

        <View style={styles.detailsContainer}>
          {details.map((detail, index) => (
            <View key={index} style={styles.detailItem}>
              <Ionicons
                name="ellipse"
                size={6}
                color="#666"
                style={styles.bulletPoint}
              />
              <Text style={styles.detailText}>{detail}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Overall Score Summary */}
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>{t('foodScore.overallScore')}</Text>
          <View style={styles.summaryScore}>
            <Text
              style={[
                styles.summaryScoreText,
                { color: getScoreColor(score.overall) },
              ]}
            >
              {score.overall}/100
            </Text>
            <Text style={styles.summaryGrade}>Grade: {score.grade}</Text>
          </View>
        </View>

        {/* Score Breakdown Sections */}
        {renderScoreSection(
          t('foodScore.additives'),
          score.breakdown.additives.score,
          score.breakdown.additives.weight,
          score.breakdown.additives.details,
          'warning-outline'
        )}

        {renderScoreSection(
          t('foodScore.nutrition'),
          score.nutrition,
          score.breakdown.nutrition.weight,
          score.breakdown.nutrition.details,
          'nutrition-outline'
        )}

        {renderScoreSection(
          t('foodScore.processing'),
          score.breakdown.processing.score,
          score.breakdown.processing.weight,
          score.breakdown.processing.details,
          'settings-outline'
        )}

        {/* AI Analysis Section */}
        {score.aiAnalysis && (
          <View style={styles.aiAnalysisContainer}>
            <View style={styles.aiAnalysisHeader}>
              <Ionicons name="sparkles" size={24} color="#6366F1" />
              <Text style={styles.aiAnalysisTitle}>
                {t('foodScore.aiAnalysis', 'AI Analysis')}
              </Text>
            </View>

            <View style={styles.aiAnalysisContent}>
              <Text style={styles.aiAnalysisSummary}>
                {score.aiAnalysis.nutritionSummary}
              </Text>

              <Text style={styles.aiAnalysisAssessment}>
                {score.aiAnalysis.overallAssessment}
              </Text>

              {score.aiAnalysis.keyNutritionPoints &&
                score.aiAnalysis.keyNutritionPoints.length > 0 && (
                  <View style={styles.aiKeyPoints}>
                    <Text style={styles.aiKeyPointsTitle}>
                      {t('foodScore.keyPoints', 'Key Points')}:
                    </Text>
                    {score.aiAnalysis.keyNutritionPoints.map((point, index) => (
                      <Text key={index} style={styles.aiKeyPoint}>
                        • {point}
                      </Text>
                    ))}
                  </View>
                )}
            </View>
          </View>
        )}

        {/* Calculation Explanation */}
        <View style={styles.explanationContainer}>
          <Text style={styles.explanationTitle}>
            {t('foodScore.howCalculated')}
          </Text>
          <Text style={styles.explanationText}>
            {score.aiAnalysis
              ? t(
                  'foodScore.aiCalculationExplanation',
                  'This score is calculated using AI analysis of nutrition data, additives, and ingredients to provide a comprehensive health assessment.'
                )
              : t('foodScore.calculationExplanation')}
          </Text>

          <View style={styles.formulaContainer}>
            <Text style={styles.formulaTitle}>{t('foodScore.formula')}:</Text>
            <Text style={styles.formulaText}>
              Overall Score = (Additives × 30%) + (Nutrition × 30%) +
              (Processing × 20%) + (Ingredients × 20%)
            </Text>
          </View>
        </View>

        {/* Recommendation */}
        <View style={styles.recommendationContainer}>
          <View style={styles.recommendationHeader}>
            <Ionicons name="bulb-outline" size={20} color="#2196F3" />
            <Text style={styles.recommendationTitle}>
              {t('foodScore.recommendation')}
            </Text>
          </View>
          <Text style={styles.recommendationText}>{score.recommendation}</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  summaryContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  summaryScore: {
    alignItems: 'center',
  },
  summaryScoreText: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  summaryGrade: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  sectionContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  scoreContainer: {
    alignItems: 'flex-end',
  },
  scoreText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  weightText: {
    fontSize: 12,
    color: '#666',
  },
  progressBarContainer: {
    marginBottom: 12,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  detailsContainer: {
    marginTop: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  bulletPoint: {
    marginTop: 6,
    marginRight: 8,
  },
  detailText: {
    flex: 1,
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  aiAnalysisContainer: {
    backgroundColor: '#f8f9ff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#6366F1',
  },
  aiAnalysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  aiAnalysisTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginLeft: 8,
  },
  aiAnalysisContent: {
    gap: 12,
  },
  aiAnalysisSummary: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    fontWeight: '500',
  },
  aiAnalysisAssessment: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  aiKeyPoints: {
    marginTop: 8,
  },
  aiKeyPointsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 6,
  },
  aiKeyPoint: {
    fontSize: 13,
    color: '#6b7280',
    lineHeight: 18,
    marginBottom: 2,
  },
  explanationContainer: {
    backgroundColor: '#f0f8ff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  explanationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  explanationText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  formulaContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
  },
  formulaTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  formulaText: {
    fontSize: 13,
    color: '#666',
    fontFamily: 'monospace',
  },
  recommendationContainer: {
    backgroundColor: '#e8f5e8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  recommendationText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default ScoreBreakdown;
