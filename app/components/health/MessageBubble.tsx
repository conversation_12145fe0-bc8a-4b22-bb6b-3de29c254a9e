import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Markdown from 'react-native-markdown-display';
import { HealthMessage } from '../../types/healthConsultation';

interface MessageBubbleProps {
  message: HealthMessage;
  isUser: boolean;
  showTimestamp?: boolean;
  onRetry?: () => void;
  animatedValue?: Animated.Value;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isUser,
  showTimestamp = false,
  onRetry,
  animatedValue,
}) => {
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getBubbleStyle = () => {
    if (message.role === 'system') {
      return [styles.bubble, styles.systemBubble];
    }

    return [styles.bubble, isUser ? styles.userBubble : styles.assistantBubble];
  };

  const getTextStyle = () => {
    if (message.role === 'system') {
      return [styles.messageText, styles.systemText];
    }
    return [
      styles.messageText,
      isUser ? styles.userText : styles.assistantText,
    ];
  };

  const renderMessageContent = () => {
    switch (message.messageType) {
      case 'recommendation':
        return (
          <View>
            <Text style={getTextStyle()}>{message.content}</Text>
            {message.metadata?.recommendationData && (
              <View style={styles.recommendationContainer}>
                <View style={styles.recommendationHeader}>
                  <Ionicons
                    name="bulb-outline"
                    size={16}
                    color={isUser ? '#fff' : '#2196F3'}
                  />
                  <Text style={[getTextStyle(), styles.recommendationTitle]}>
                    Recommendations
                  </Text>
                </View>
                {message.metadata.recommendationData.actionItems.map(
                  (item, index) => (
                    <View key={index} style={styles.actionItem}>
                      <Text style={[getTextStyle(), styles.actionItemText]}>
                        • {item}
                      </Text>
                    </View>
                  )
                )}
              </View>
            )}
          </View>
        );

      case 'assessment':
        return (
          <View>
            <Text style={getTextStyle()}>{message.content}</Text>
            {message.metadata?.assessmentData && (
              <View style={styles.assessmentContainer}>
                <Text style={[getTextStyle(), styles.assessmentTitle]}>
                  Health Assessment
                </Text>
                {/* TODO: Render assessment questions/responses */}
              </View>
            )}
          </View>
        );

      case 'system':
        return (
          <View style={styles.systemMessageContainer}>
            <Ionicons
              name="information-circle-outline"
              size={16}
              color="#FF9800"
            />
            <Text style={getTextStyle()}>{message.content}</Text>
          </View>
        );

      default:
        // AI mesajları için markdown, user mesajları için normal text
        if (message.role === 'assistant') {
          return (
            <Markdown
              style={{
                body: {
                  color: isUser ? '#fff' : '#333',
                  fontSize: 16,
                },
                paragraph: {
                  marginBottom: 4,
                },
                strong: {
                  fontWeight: '600',
                },
                bullet_list: {
                  marginBottom: 4,
                },
                ordered_list: {
                  marginBottom: 4,
                },
                list_item: {
                  marginBottom: 2,
                },
                code_inline: {
                  backgroundColor: isUser
                    ? 'rgba(255,255,255,0.2)'
                    : 'rgba(0,0,0,0.1)',
                  paddingHorizontal: 4,
                  paddingVertical: 2,
                  borderRadius: 4,
                  fontSize: 14,
                },
                code_block: {
                  backgroundColor: isUser
                    ? 'rgba(255,255,255,0.2)'
                    : 'rgba(0,0,0,0.1)',
                  padding: 8,
                  borderRadius: 6,
                  marginVertical: 4,
                },
              }}
            >
              {message.content}
            </Markdown>
          );
        }
        return <Text style={getTextStyle()}>{message.content}</Text>;
    }
  };

  const containerStyle = [
    styles.messageContainer,
    isUser ? styles.userContainer : {},
    message.role === 'system' && styles.systemContainer,
  ];

  const bubbleContent = (
    <View style={containerStyle}>
      <Animated.View
        style={[
          getBubbleStyle(),
          animatedValue && {
            opacity: animatedValue,
            transform: [
              {
                translateY: animatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0],
                }),
              },
            ],
          },
        ]}
      >
        {renderMessageContent()}

        {/* Message status and timestamp row */}
        <View style={styles.messageFooter}>
          {/* Timestamp and status in same row, right aligned */}
          <View style={styles.timestampStatusRow}>
            {showTimestamp && (
              <Text
                style={[
                  styles.timestamp,
                  isUser ? styles.userTimestamp : styles.assistantTimestamp,
                  message.role === 'system' && styles.systemTimestamp,
                ]}
              >
                {formatTime(message.timestamp)}
              </Text>
            )}

            {/* Message status indicators for user messages */}
            {isUser && message.role === 'user' && (
              <View style={styles.messageStatusContainer}>
                {message.aiResponseStatus === 'pending' ? (
                  // Tek tik - mesaj gönderildi
                  <Ionicons
                    name="checkmark"
                    size={14}
                    color="rgba(255,255,255,0.8)"
                  />
                ) : message.aiResponseStatus === 'completed' ? (
                  // Çift tik - AI cevap verdi
                  <View style={styles.doubleCheck}>
                    <Ionicons
                      name="checkmark"
                      size={14}
                      color="rgba(255,255,255,0.8)"
                    />
                    <Ionicons
                      name="checkmark"
                      size={14}
                      color="rgba(255,255,255,0.8)"
                      style={styles.secondCheck}
                    />
                  </View>
                ) : message.aiResponseStatus === 'failed' ? (
                  // Hata durumu
                  <Ionicons name="alert-circle" size={14} color="#FF6B6B" />
                ) : null}
              </View>
            )}
          </View>
        </View>
      </Animated.View>

      {/* Retry button for failed messages */}
      {message.metadata?.retryCount &&
        message.metadata.retryCount > 0 &&
        onRetry && (
          <TouchableOpacity
            style={styles.retryButton}
            onPress={onRetry}
            activeOpacity={0.7}
          >
            <Ionicons name="refresh-outline" size={16} color="#FF6B6B" />
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        )}
    </View>
  );

  return bubbleContent;
};

const styles = StyleSheet.create({
  messageContainer: {
    marginVertical: 4,
    paddingHorizontal: 16,
  },
  userContainer: {
    alignItems: 'flex-end',
  },
  systemContainer: {
    alignItems: 'center',
    marginVertical: 8,
  },
  bubble: {
    maxWidth: '85%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  userBubble: {
    backgroundColor: '#2196F3',
    borderBottomRightRadius: 8,
  },
  assistantBubble: {
    backgroundColor: '#f5f5f5',
    borderBottomLeftRadius: 8,
    maxWidth: '90%', // AI mesajları için daha geniş alan
  },

  systemBubble: {
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    borderColor: 'rgba(255, 152, 0, 0.3)',
    borderWidth: 1,
    maxWidth: '90%',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userText: {
    color: '#fff',
  },
  assistantText: {
    color: '#333',
  },
  systemText: {
    color: '#FF9800',
    fontSize: 14,
    textAlign: 'center',
  },
  timestamp: {
    fontSize: 11,
    marginTop: 4,
    opacity: 0.7,
  },
  userTimestamp: {
    color: '#fff',
    textAlign: 'right',
  },
  assistantTimestamp: {
    color: '#666',
    textAlign: 'left',
  },
  systemTimestamp: {
    color: '#FF9800',
    textAlign: 'center',
  },
  recommendationContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  recommendationTitle: {
    fontWeight: '600',
    marginLeft: 6,
  },
  actionItem: {
    marginVertical: 2,
  },
  actionItemText: {
    fontSize: 14,
  },
  assessmentContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  assessmentTitle: {
    fontWeight: '600',
    marginBottom: 6,
  },
  systemMessageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
    borderRadius: 12,
    alignSelf: 'flex-end',
  },
  retryText: {
    fontSize: 12,
    color: '#FF6B6B',
    marginLeft: 4,
    fontWeight: '500',
  },

  // Message status styles
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 4,
  },
  timestampStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  messageStatusContainer: {
    marginLeft: 6,
    alignSelf: 'flex-end',
  },
  doubleCheck: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  secondCheck: {
    marginLeft: -6, // Overlap the checkmarks slightly
  },
});
