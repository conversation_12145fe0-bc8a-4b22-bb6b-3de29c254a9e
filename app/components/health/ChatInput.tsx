import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../../context/LocalizationContext';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
  multiline?: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder,
  maxLength = 1000,
  multiline = true,
}) => {
  const { t } = useLocalization();
  const [message, setMessage] = useState('');
  const [inputHeight, setInputHeight] = useState(40);
  const textInputRef = useRef<TextInput>(null);
  const sendButtonScale = useRef(new Animated.Value(1)).current;

  const maxInputHeight = 120;
  const minInputHeight = 40;

  const handleSendMessage = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !disabled) {
      // Animate send button
      Animated.sequence([
        Animated.timing(sendButtonScale, {
          toValue: 0.8,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(sendButtonScale, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();

      onSendMessage(trimmedMessage);
      setMessage('');
      setInputHeight(minInputHeight);
    }
  };

  const handleContentSizeChange = (event: any) => {
    if (multiline) {
      const { height } = event.nativeEvent.contentSize;
      const newHeight = Math.min(
        Math.max(height, minInputHeight),
        maxInputHeight
      );
      setInputHeight(newHeight);
    }
  };

  const canSend = message.trim().length > 0 && !disabled;

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        <View style={[styles.textInputContainer, { height: inputHeight + 16 }]}>
          <TextInput
            ref={textInputRef}
            style={[
              styles.textInput,
              { height: inputHeight },
              disabled && styles.textInputDisabled,
            ]}
            value={message}
            onChangeText={setMessage}
            placeholder={placeholder || t('healthConsultation.typeMessage')}
            placeholderTextColor="#999"
            multiline={multiline}
            maxLength={maxLength}
            onContentSizeChange={handleContentSizeChange}
            editable={!disabled}
            returnKeyType="default"
            blurOnSubmit={false}
            textAlignVertical="top"
          />
        </View>

        <Animated.View style={{ transform: [{ scale: sendButtonScale }] }}>
          <TouchableOpacity
            style={[
              styles.sendButton,
              canSend ? styles.sendButtonActive : styles.sendButtonInactive,
            ]}
            onPress={handleSendMessage}
            disabled={!canSend}
            activeOpacity={0.7}
          >
            <Ionicons name="send" size={20} color={canSend ? '#fff' : '#999'} />
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Character count indicator */}
      {message.length > maxLength * 0.8 && (
        <View style={styles.characterCount}>
          <Text
            style={[
              styles.characterCountText,
              message.length >= maxLength && styles.characterCountWarning,
            ]}
          >
            {message.length}/{maxLength}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: Platform.OS === 'ios' ? 20 : 12, // Reduced padding
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
  },
  textInputContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    justifyContent: 'center',
  },
  textInput: {
    fontSize: 16,
    color: '#333',
    textAlignVertical: 'center',
    paddingVertical: 0, // Remove default padding
  },
  textInputDisabled: {
    color: '#999',
    backgroundColor: '#f0f0f0',
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sendButtonActive: {
    backgroundColor: '#2196F3',
  },
  sendButtonInactive: {
    backgroundColor: '#e0e0e0',
  },
  characterCount: {
    alignItems: 'flex-end',
    marginTop: 4,
  },
  characterCountText: {
    fontSize: 12,
    color: '#999',
  },
  characterCountWarning: {
    color: '#FF6B6B',
  },
});
