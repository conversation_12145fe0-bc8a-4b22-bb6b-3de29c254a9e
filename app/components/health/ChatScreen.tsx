import React, { useEffect, useRef, useState } from 'react';
import {
  StyleSheet,
  Alert,
  Animated,
  RefreshControl,
  FlatList,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { MessageBubble } from './MessageBubble';
import { TypingIndicator } from './TypingIndicator';
import { ChatInput } from './ChatInput';
import { useLocalization } from '../../context/LocalizationContext';
import { useSubscription } from '../../context/SubscriptionContext';
import { useRouter } from 'expo-router';

import { HealthMessage } from '../../types/healthConsultation';
import {
  getHealthMessages,
  createHealthConversation,
} from '../../services/HealthConsultationSupabase';
import { healthConsultationRealtime } from '../../services/HealthConsultationRealtime';
import { getUnifiedDeviceId } from '../../services/revenueCatService';
import { supabase } from '../../lib/supabase';

interface ChatScreenProps {
  conversationId?: string;
  topicId?: string;
  topicTitle?: string;
  onConversationCreated?: (conversation: any) => void;
}

export const ChatScreen: React.FC<ChatScreenProps> = ({
  conversationId,
  topicId,
  topicTitle,
  onConversationCreated,
}) => {
  const { t } = useLocalization();
  const router = useRouter();
  const { checkPaywallCondition, trackMessageSent, subscriptionStatus } =
    useSubscription();

  // 🎯 SIMPLE LOCAL STATE: No Context needed
  const [messages, setMessages] = useState<HealthMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<
    string | undefined
  >(conversationId);
  const [pendingMessage, setPendingMessage] = useState<string | null>(null);

  // Message status is managed by database real-time updates

  const flatListRef = useRef<FlatList>(null);
  const [refreshing, setRefreshing] = useState(false);
  const messageAnimations = useRef<Map<string, Animated.Value>>(new Map());

  // Create conversation function
  const createConversationForMessage = async (): Promise<string> => {
    try {
      const deviceId = await getUnifiedDeviceId();

      const createRequest = {
        title: topicTitle || t('healthConsultation.tabTitle'),
        deviceId,
        topic: topicId,
        language: 'tr' as const,
      };

      const response = await createHealthConversation(createRequest);
      if (!response || !response.conversation) {
        throw new Error('Failed to create conversation in database');
      }

      const newConversationId = response.conversation.id;
      console.log('🆕 Created new conversation with ID:', newConversationId);

      // Update conversation ID state - this will trigger useEffect to setup real-time
      setCurrentConversationId(newConversationId);

      // Notify parent component
      if (onConversationCreated) {
        onConversationCreated(response.conversation);
      }

      return newConversationId;
    } catch (error) {
      console.error('Failed to create conversation:', error);
      throw error;
    }
  };

  // 🎯 SIMPLE: Load messages and setup real-time subscription
  useEffect(() => {
    if (!currentConversationId) return;

    console.log(
      '🔄 ChatScreen: Loading messages and setting up real-time for:',
      currentConversationId
    );

    // Load initial messages
    const loadInitialMessages = async () => {
      try {
        setIsLoading(true);
        const loadedMessages = await getHealthMessages(currentConversationId);

        // Preserve any temporary messages that might not be in database yet
        setMessages((prevMessages) => {
          const tempMessages = prevMessages.filter(
            (msg) =>
              msg.id.startsWith('temp-') ||
              msg.conversationId === 'temp-conversation'
          );

          // Merge temp messages with loaded messages, replacing temp with real
          const mergedMessages = [...loadedMessages];
          tempMessages.forEach((tempMsg) => {
            const dbMatch = loadedMessages.find(
              (dbMsg) =>
                dbMsg.content === tempMsg.content &&
                dbMsg.role === tempMsg.role &&
                Math.abs(dbMsg.timestamp - tempMsg.timestamp) < 10000 // Within 10 seconds
            );
            if (dbMatch) {
              // Replace temp message with database message (çift tik)
              const tempIndex = mergedMessages.findIndex(
                (m) => m.id === dbMatch.id
              );
              if (tempIndex >= 0) {
                mergedMessages[tempIndex] = {
                  ...dbMatch,
                  aiResponseStatus: 'completed' as const, // Database'de varsa çift tik
                };
              }
            } else {
              // Keep temp message if no database match
              mergedMessages.push(tempMsg);
            }
          });

          // Sort by timestamp
          return mergedMessages.sort((a, b) => a.timestamp - b.timestamp);
        });

        // Check for pending messages or user messages without AI responses
        const hasPendingOrUnresponded = loadedMessages.some((msg, index) => {
          if (msg.role === 'user') {
            // Check if there's an AI response after this user message
            const hasAIResponse = loadedMessages
              .slice(index + 1)
              .some((nextMsg) => nextMsg.role === 'assistant');
            return !hasAIResponse;
          }
          return false;
        });

        // Set typing based on pending messages
        if (hasPendingOrUnresponded) {
          setIsTyping(true);
        }

        console.log(
          '✅ Loaded',
          loadedMessages.length,
          'messages, pending or unresponded:',
          hasPendingOrUnresponded
        );
      } catch (error) {
        console.error('❌ Error loading messages:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Setup real-time subscription
    const subscription = healthConsultationRealtime.subscribeToMessages(
      currentConversationId,
      (newMessage) => {
        // Handle user messages from real-time
        if (newMessage.role === 'user') {
          setMessages((prev) => {
            // Find and replace temporary message with real database message
            const tempIndex = prev.findIndex(
              (msg) =>
                (msg.id.startsWith('temp-') ||
                  msg.conversationId === 'temp-conversation') &&
                msg.content === newMessage.content &&
                msg.role === 'user'
            );

            if (tempIndex >= 0) {
              // Replace temporary message with real database message
              const updated = [...prev];
              updated[tempIndex] = {
                ...newMessage,
                // Database'de varsa her zaman çift tik
                aiResponseStatus: 'completed' as const,
              };

              // Typing durumunu kontrol et
              if (newMessage.aiResponseStatus === 'pending') {
                setIsTyping(true); // AI yazıyor
              }

              return updated;
            } else {
              // If no temp message found, check if this message already exists
              const existsIndex = prev.findIndex(
                (msg) => msg.id === newMessage.id
              );
              if (existsIndex >= 0) {
                // Update existing message status
                const updated = [...prev];
                updated[existsIndex] = {
                  ...newMessage,
                  // Database'de varsa her zaman çift tik
                  aiResponseStatus: 'completed' as const,
                };

                // Typing durumunu kontrol et
                if (newMessage.aiResponseStatus === 'pending') {
                  setIsTyping(true); // AI yazıyor
                }

                return updated;
              }
              // Add new user message if it doesn't exist
              const newUserMessage = {
                ...newMessage,
                // Database'de varsa her zaman çift tik
                aiResponseStatus: 'completed' as const,
              };

              // Typing durumunu kontrol et
              if (newMessage.aiResponseStatus === 'pending') {
                setIsTyping(true); // AI yazıyor
              }

              return [...prev, newUserMessage];
            }
          });
          return;
        }

        // Handle AI messages normally
        setMessages((prev) => {
          // Check for exact ID match
          const exactIndex = prev.findIndex((msg) => msg.id === newMessage.id);
          if (exactIndex >= 0) {
            const updated = [...prev];
            updated[exactIndex] = newMessage;

            // Check typing status after update
            if (newMessage.role === 'assistant') {
              setIsTyping(false); // AI responded, stop typing
            }

            return updated;
          }

          // Add new AI message
          const newMessages = [...prev, newMessage];

          // Check typing status after adding new message
          if (newMessage.role === 'assistant') {
            setIsTyping(false); // AI responded, stop typing
          }

          return newMessages;
        });
      },
      (error) => {
        console.error('❌ Real-time subscription error:', error);
      }
    );

    loadInitialMessages();

    // Cleanup subscription
    return () => {
      console.log(
        '🧹 Cleaning up real-time subscription for:',
        currentConversationId
      );
      healthConsultationRealtime.unsubscribeFromMessages(currentConversationId);
    };
  }, [currentConversationId]);

  // Handle pending message after conversation is created and real-time is set up
  useEffect(() => {
    if (currentConversationId && pendingMessage) {
      console.log(
        '🚀 Sending pending message after conversation setup:',
        pendingMessage
      );

      // Update the temporary message with real conversation ID
      setMessages((prevMessages) =>
        prevMessages.map((msg) =>
          msg.conversationId === 'temp-conversation' &&
          msg.content === pendingMessage
            ? { ...msg, conversationId: currentConversationId }
            : msg
        )
      );

      // Send the pending message
      sendMessageToSupabase(pendingMessage, currentConversationId);
      setPendingMessage(null);
    }
  }, [currentConversationId, pendingMessage]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0 && flatListRef.current) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length]);

  // Create animation for new messages
  const createMessageAnimation = (messageId: string) => {
    if (!messageAnimations.current.has(messageId)) {
      const animValue = new Animated.Value(0);
      messageAnimations.current.set(messageId, animValue);

      Animated.timing(animValue, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      return animValue;
    }
    return messageAnimations.current.get(messageId);
  };

  // Send message to Supabase function
  const sendMessageToSupabase = async (
    content: string,
    conversationId: string
  ) => {
    try {
      console.log('🚀 Calling Supabase function to handle message:', content);
      setIsTyping(true); // Start typing since AI will respond

      // Get unified device ID for RevenueCat integration
      const deviceId = await getUnifiedDeviceId();
      console.log('🔑 Using device ID:', deviceId);

      // Message is already in state, just send to Supabase

      // Call Supabase function - it will handle everything
      const response = await supabase.functions.invoke('send-health-message', {
        body: {
          conversationId,
          content,
          messageType: 'text',
          language: 'tr',
          deviceId, // Add device ID for RevenueCat integration
        },
      });

      console.log('🔍 Full Supabase function response:', response);

      if (response.error) {
        console.error('🚨 Supabase function error details:', {
          error: response.error,
          message: response.error.message,
          details: response.error.details,
          data: response.data,
        });
        throw response.error;
      }

      const { data } = response;

      console.log('✅ Supabase function completed:', data);
      console.log('⏳ Real-time will update UI automatically...');

      console.log(
        '✅ Supabase function completed, real-time will handle status updates'
      );

      // Track message sent for usage limits (only for non-premium users)
      if (!subscriptionStatus.isPremium) {
        await trackMessageSent(conversationId);
      }
    } catch (error) {
      // Only show alert for actual AI service failures
      // Database-first architecture handles most errors gracefully via real-time sync
      console.error('ChatScreen: Error sending message:', error);
      console.error('ChatScreen: Error details:', {
        name: (error as any)?.name,
        message: (error as any)?.message,
        stack: (error as any)?.stack,
      });

      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';

      // Only show alert for critical errors (not database save failures)
      if (
        errorMessage.includes('AI service') ||
        errorMessage.includes('network') ||
        errorMessage.includes('API')
      ) {
        Alert.alert(
          t('common.error'),
          t('healthConsultation.sendMessageError'),
          [{ text: t('common.ok') }]
        );
      } else {
        console.log('🔄 Non-critical error, real-time will handle recovery');
      }
    } finally {
      setIsTyping(false);
    }
  };

  // 🎯 SUPABASE-FIRST: Call Supabase function to handle everything
  const handleSendMessage = async (content: string) => {
    try {
      // If no conversation exists, create one but show message immediately
      if (!currentConversationId) {
        console.log(
          '🆕 No conversation exists, creating one and showing message immediately'
        );

        // Check paywall condition for premium users before creating conversation
        if (!subscriptionStatus.isPremium) {
          const shouldShowPaywallModal = await checkPaywallCondition(
            'create_conversation'
          );
          if (shouldShowPaywallModal) {
            router.push('/paywall');
            return;
          }
        }

        // 🎯 IMMEDIATE UI UPDATE: Add user message to local state immediately
        const tempUserMessage: HealthMessage = {
          id: `temp-${Date.now()}`, // Temporary ID
          conversationId: 'temp-conversation', // Temporary conversation ID
          role: 'user',
          content,
          messageType: 'text',
          timestamp: Date.now(),
          aiResponseStatus: 'pending', // Tek tik
        };

        // Add to local state immediately for instant UI feedback
        setMessages((prevMessages) => {
          const newMessages = [...prevMessages, tempUserMessage];
          // Start typing since we have a user message without AI response
          setIsTyping(true);
          return newMessages;
        });

        // Set message as pending and create conversation
        setPendingMessage(content);
        // Typing will be managed by real-time subscription
        await createConversationForMessage();
        return; // Exit here, useEffect will handle sending the message
      }

      // Check paywall condition for premium users
      if (!subscriptionStatus.isPremium) {
        const shouldShowPaywallModal = await checkPaywallCondition(
          'send_message',
          currentConversationId
        );
        if (shouldShowPaywallModal) {
          router.push('/paywall');
          return;
        }
      }

      // Add message to local state first for existing conversation
      const tempUserMessage: HealthMessage = {
        id: `temp-${Date.now()}`, // Temporary ID
        conversationId: currentConversationId,
        role: 'user',
        content,
        messageType: 'text',
        timestamp: Date.now(),
        aiResponseStatus: isTyping ? 'completed' : 'pending', // If already typing, mark as completed
      };

      setMessages((prevMessages) => [...prevMessages, tempUserMessage]);
      setIsTyping(true); // Start typing

      // Send message to Supabase
      await sendMessageToSupabase(content, currentConversationId);
    } catch (error) {
      console.error('ChatScreen: Error in handleSendMessage:', error);
      setIsTyping(false);
      Alert.alert(t('common.error'), t('healthConsultation.sendMessageError'), [
        { text: t('common.ok') },
      ]);
    }
  };

  const handleRetryMessage = async (message: HealthMessage) => {
    try {
      await handleSendMessage(message.content);
    } catch (error) {
      Alert.alert(
        t('common.error'),
        t('healthConsultation.retryMessageError'),
        [{ text: t('common.ok') }]
      );
    }
  };

  const handleRefresh = async () => {
    if (currentConversationId) {
      setRefreshing(true);
      try {
        // Simple refresh: reload messages from DB
        const loadedMessages = await getHealthMessages(currentConversationId);
        setMessages(loadedMessages);
      } catch (error) {
        console.error('Error refreshing messages:', error);
      } finally {
        setRefreshing(false);
      }
    }
  };

  // Simplified: No pagination for now
  const handleLoadMore = () => {
    console.log('Load more not implemented in simple version');
  };

  const renderMessage = ({
    item,
    index,
  }: {
    item: HealthMessage;
    index: number;
  }) => {
    const isUser = item.role === 'user';
    const showTimestamp =
      index === 0 ||
      (messages[index - 1] &&
        Math.abs(item.timestamp - messages[index - 1].timestamp) > 300000); // 5 minutes

    const animValue = createMessageAnimation(item.id);

    return (
      <MessageBubble
        message={item}
        isUser={isUser}
        showTimestamp={showTimestamp}
        onRetry={() => handleRetryMessage(item)}
        animatedValue={animValue}
      />
    );
  };

  const renderFooter = () => {
    if (isTyping) {
      return <TypingIndicator visible={true} />;
    }
    return null;
  };

  const keyExtractor = (item: HealthMessage) => item.id;

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
    >
      {/* Message List Area - This will shrink when keyboard appears */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={keyExtractor}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        inverted={false}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor="#2196F3"
            colors={['#2196F3']}
          />
        }
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 10,
        }}
        keyboardShouldPersistTaps="handled"
      />

      {/* Input Container - This stays at bottom */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isLoading || isTyping}
        placeholder={t('healthConsultation.typeMessage')}
      />
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },

  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 16,
    flexGrow: 1,
    justifyContent: 'flex-end',
  },
});
