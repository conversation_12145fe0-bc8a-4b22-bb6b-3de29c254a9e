import React from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import Animated, { FadeInDown, SlideOutRight } from 'react-native-reanimated';
import ContextMenu from 'react-native-context-menu-view';
import { HealthConversation } from '../../types/healthConsultation';

interface ConversationCardProps {
  item: HealthConversation;
  index: number;
  onPress?: (item: HealthConversation) => void;
  onDelete?: (item: HealthConversation) => void;
  onArchive?: (item: HealthConversation) => void;
  t: (key: string) => string;
  messageCounts: Record<string, number>;
  showContextMenu?: boolean; // New prop to control context menu visibility
}

const ConversationCard: React.FC<ConversationCardProps> = ({
  item,
  index,
  onPress,
  onDelete,
  onArchive,
  t,
  messageCounts,
  showContextMenu = true,
}) => {
  const handleContextMenuPress = (e: any) => {
    switch (e.nativeEvent.index) {
      case 0:
        // Open Chat
        router.push(`/health-chat?conversationId=${item.id}`);
        break;
      case 1:
        // View Details
        router.push(`/health-conversation-detail?conversationId=${item.id}`);
        break;
      case 2:
        // Archive/Unarchive
        onArchive?.(item);
        break;
      case 3:
        // Delete
        onDelete?.(item);
        break;
    }
  };

  const handlePress = () => {
    if (onPress) {
      onPress(item);
    } else {
      // Default behavior: navigate to chat
      router.push(`/health-chat?conversationId=${item.id}`);
    }
  };

  const cardContent = (
    <Pressable
      style={[styles.conversationCard, item.isArchived && styles.archivedCard]}
      onPress={handlePress}
    >
      <View style={styles.conversationContent}>
        <View style={styles.conversationHeader}>
          <Text style={styles.conversationTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <View style={styles.conversationMeta}>
            {item.isArchived && (
              <Ionicons
                name="archive"
                size={14}
                color="#FF9800"
                style={styles.archiveIcon}
              />
            )}
            <Text style={styles.conversationDate}>
              {new Date(item.updatedAt).toLocaleDateString()}
            </Text>
          </View>
        </View>

        {item.lastMessage && (
          <Text style={styles.conversationPreview} numberOfLines={2}>
            {item.lastMessage}
          </Text>
        )}

        <View style={styles.conversationFooter}>
          <Text style={styles.messageCount}>
            {messageCounts[item.id] || 0}{' '}
            {t('healthConsultation.history.messages')}
          </Text>
          <Text style={styles.conversationTime}>
            {new Date(item.updatedAt).toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        </View>
      </View>
    </Pressable>
  );

  return (
    <Animated.View
      entering={FadeInDown.delay(index * 50)}
      exiting={SlideOutRight}
    >
      {showContextMenu ? (
        <ContextMenu
          actions={[
            {
              title: t('healthConsultation.detail.openChat'),
              systemIcon: 'message',
            },
            {
              title: t('healthConsultation.detail.title'),
              systemIcon: 'info.circle',
            },
            {
              title: item.isArchived
                ? t('healthConsultation.history.active')
                : t('healthConsultation.history.archived'),
              systemIcon: 'archivebox',
            },
            {
              title: t('common.delete'),
              destructive: true,
              systemIcon: 'trash',
            },
          ]}
          onPress={handleContextMenuPress}
        >
          {cardContent}
        </ContextMenu>
      ) : (
        cardContent
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  conversationCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    flexDirection: 'row',
    alignItems: 'center',
  },
  archivedCard: {
    opacity: 0.7,
    backgroundColor: '#f5f5f5',
  },
  conversationContent: {
    flex: 1,
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  conversationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  conversationMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  archiveIcon: {
    marginRight: 4,
  },
  conversationDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  conversationPreview: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  conversationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messageCount: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  conversationTime: {
    fontSize: 12,
    color: '#9CA3AF',
  },
});

export default ConversationCard;
