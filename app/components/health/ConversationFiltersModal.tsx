import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import DateTimePicker from '@react-native-community/datetimepicker';

export type SortOption = 'date' | 'messages' | 'title' | 'created';
export type FilterOption = 'all' | 'active' | 'archived';

export interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
}

export interface FilterSortOptions {
  sortBy: SortOption;
  filterBy: FilterOption;
  dateRange: DateRange;
  messageCountRange: {
    min: number | null;
    max: number | null;
  };
}

interface ConversationFiltersModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (options: FilterSortOptions) => void;
  currentOptions: FilterSortOptions;
  t: (key: string) => string;
}

export const ConversationFiltersModal: React.FC<
  ConversationFiltersModalProps
> = ({ visible, onClose, onApply, currentOptions, t }) => {
  const [localOptions, setLocalOptions] =
    useState<FilterSortOptions>(currentOptions);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  const handleApply = () => {
    onApply(localOptions);
    onClose();
  };

  const handleReset = () => {
    const resetOptions: FilterSortOptions = {
      sortBy: 'date',
      filterBy: 'all',
      dateRange: { startDate: null, endDate: null },
      messageCountRange: { min: null, max: null },
    };
    setLocalOptions(resetOptions);
  };

  const updateSortBy = (sortBy: SortOption) => {
    setLocalOptions((prev) => ({ ...prev, sortBy }));
  };

  const updateFilterBy = (filterBy: FilterOption) => {
    setLocalOptions((prev) => ({ ...prev, filterBy }));
  };

  const updateDateRange = (
    field: 'startDate' | 'endDate',
    date: Date | null
  ) => {
    setLocalOptions((prev) => ({
      ...prev,
      dateRange: { ...prev.dateRange, [field]: date },
    }));
  };

  const updateMessageCountRange = (
    field: 'min' | 'max',
    value: number | null
  ) => {
    setLocalOptions((prev) => ({
      ...prev,
      messageCountRange: { ...prev.messageCountRange, [field]: value },
    }));
  };

  const formatDate = (date: Date | null) => {
    if (!date) return t('healthConsultation.filters.selectDate');
    return date.toLocaleDateString();
  };

  const getSortOptionLabel = (option: SortOption) => {
    switch (option) {
      case 'date':
        return t('healthConsultation.history.sortOptions.date');
      case 'messages':
        return t('healthConsultation.history.sortOptions.messages');
      case 'title':
        return t('healthConsultation.history.sortOptions.title');
      case 'created':
        return t('healthConsultation.history.sortOptions.created');
      default:
        return option;
    }
  };

  const getFilterOptionLabel = (option: FilterOption) => {
    switch (option) {
      case 'all':
        return t('healthConsultation.history.filterOptions.all');
      case 'active':
        return t('healthConsultation.history.filterOptions.active');
      case 'archived':
        return t('healthConsultation.history.filterOptions.archived');
      default:
        return option;
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <Animated.View entering={FadeInUp} style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.headerButton}>
            <Text style={styles.headerButtonText}>{t('common.cancel')}</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {t('healthConsultation.filters.title')}
          </Text>
          <TouchableOpacity onPress={handleApply} style={styles.headerButton}>
            <Text style={[styles.headerButtonText, styles.applyButton]}>
              {t('healthConsultation.filters.apply')}
            </Text>
          </TouchableOpacity>
        </Animated.View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Sort Options */}
          <Animated.View
            entering={FadeInDown.delay(100)}
            style={styles.section}
          >
            <Text style={styles.sectionTitle}>
              {t('healthConsultation.history.sortBy')}
            </Text>
            <View style={styles.optionsGrid}>
              {(['date', 'messages', 'title', 'created'] as SortOption[]).map(
                (option) => (
                  <TouchableOpacity
                    key={option}
                    style={[
                      styles.optionCard,
                      localOptions.sortBy === option && styles.selectedOption,
                    ]}
                    onPress={() => updateSortBy(option)}
                  >
                    <Ionicons
                      name={
                        option === 'date'
                          ? 'calendar-outline'
                          : option === 'messages'
                          ? 'chatbubbles-outline'
                          : option === 'title'
                          ? 'text-outline'
                          : 'time-outline'
                      }
                      size={24}
                      color={
                        localOptions.sortBy === option ? '#2196F3' : '#666'
                      }
                    />
                    <Text
                      style={[
                        styles.optionText,
                        localOptions.sortBy === option &&
                          styles.selectedOptionText,
                      ]}
                    >
                      {getSortOptionLabel(option)}
                    </Text>
                  </TouchableOpacity>
                )
              )}
            </View>
          </Animated.View>

          {/* Filter Options */}
          <Animated.View
            entering={FadeInDown.delay(200)}
            style={styles.section}
          >
            <Text style={styles.sectionTitle}>
              {t('healthConsultation.history.filterBy')}
            </Text>
            <View style={styles.optionsGrid}>
              {(['all', 'active', 'archived'] as FilterOption[]).map(
                (option) => (
                  <TouchableOpacity
                    key={option}
                    style={[
                      styles.optionCard,
                      localOptions.filterBy === option && styles.selectedOption,
                    ]}
                    onPress={() => updateFilterBy(option)}
                  >
                    <Ionicons
                      name={
                        option === 'all'
                          ? 'list-outline'
                          : option === 'active'
                          ? 'checkmark-circle-outline'
                          : 'archive-outline'
                      }
                      size={24}
                      color={
                        localOptions.filterBy === option ? '#2196F3' : '#666'
                      }
                    />
                    <Text
                      style={[
                        styles.optionText,
                        localOptions.filterBy === option &&
                          styles.selectedOptionText,
                      ]}
                    >
                      {getFilterOptionLabel(option)}
                    </Text>
                  </TouchableOpacity>
                )
              )}
            </View>
          </Animated.View>

          {/* Date Range Filter */}
          <Animated.View
            entering={FadeInDown.delay(300)}
            style={styles.section}
          >
            <Text style={styles.sectionTitle}>
              {t('healthConsultation.filters.dateRange')}
            </Text>
            <View style={styles.dateRangeContainer}>
              <TouchableOpacity
                style={styles.dateButton}
                onPress={() => setShowStartDatePicker(true)}
              >
                <Ionicons name="calendar-outline" size={20} color="#666" />
                <Text style={styles.dateButtonText}>
                  {t('healthConsultation.filters.startDate')}:{' '}
                  {formatDate(localOptions.dateRange.startDate)}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.dateButton}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Ionicons name="calendar-outline" size={20} color="#666" />
                <Text style={styles.dateButtonText}>
                  {t('healthConsultation.filters.endDate')}:{' '}
                  {formatDate(localOptions.dateRange.endDate)}
                </Text>
              </TouchableOpacity>

              {(localOptions.dateRange.startDate ||
                localOptions.dateRange.endDate) && (
                <TouchableOpacity
                  style={styles.clearDateButton}
                  onPress={() =>
                    updateDateRange('startDate', null) ||
                    updateDateRange('endDate', null)
                  }
                >
                  <Ionicons name="close-circle" size={20} color="#FF5722" />
                  <Text style={styles.clearDateText}>
                    {t('healthConsultation.filters.clearDates')}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </Animated.View>

          {/* Reset Button */}
          <Animated.View
            entering={FadeInDown.delay(400)}
            style={styles.resetSection}
          >
            <TouchableOpacity style={styles.resetButton} onPress={handleReset}>
              <Ionicons name="refresh-outline" size={20} color="#FF5722" />
              <Text style={styles.resetButtonText}>
                {t('healthConsultation.filters.reset')}
              </Text>
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>

        {/* Date Pickers */}
        {showStartDatePicker && (
          <DateTimePicker
            value={localOptions.dateRange.startDate || new Date()}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowStartDatePicker(false);
              if (selectedDate) {
                updateDateRange('startDate', selectedDate);
              }
            }}
          />
        )}

        {showEndDatePicker && (
          <DateTimePicker
            value={localOptions.dateRange.endDate || new Date()}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowEndDatePicker(false);
              if (selectedDate) {
                updateDateRange('endDate', selectedDate);
              }
            }}
          />
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  headerButtonText: {
    fontSize: 16,
    color: '#666',
  },
  applyButton: {
    color: '#2196F3',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  optionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    minWidth: '45%',
    gap: 8,
  },
  selectedOption: {
    borderColor: '#2196F3',
    backgroundColor: '#f3f8ff',
  },
  optionText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  selectedOptionText: {
    color: '#2196F3',
    fontWeight: '500',
  },
  dateRangeContainer: {
    gap: 12,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    gap: 12,
  },
  dateButtonText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  clearDateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 8,
  },
  clearDateText: {
    fontSize: 14,
    color: '#FF5722',
    fontWeight: '500',
  },
  resetSection: {
    marginTop: 32,
    marginBottom: 24,
    alignItems: 'center',
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FF5722',
    gap: 8,
  },
  resetButtonText: {
    fontSize: 14,
    color: '#FF5722',
    fontWeight: '500',
  },
});
