import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useLocalization } from '../../hooks/useLocalization';

interface QuickReply {
  id: string;
  text: string;
  icon?: string;
}

interface QuickRepliesProps {
  replies: QuickReply[];
  onReplyPress: (reply: QuickReply) => void;
  visible?: boolean;
  animatedValue?: Animated.Value;
}

export const QuickReplies: React.FC<QuickRepliesProps> = ({
  replies,
  onReplyPress,
  visible = true,
  animatedValue,
}) => {
  if (!visible || replies.length === 0) {
    return null;
  }

  const containerStyle = [
    styles.container,
    animatedValue && {
      opacity: animatedValue,
      transform: [
        {
          translateY: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [20, 0],
          }),
        },
      ],
    },
  ];

  return (
    <Animated.View style={containerStyle}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        {replies.map((reply, index) => (
          <TouchableOpacity
            key={reply.id}
            style={[
              styles.replyButton,
              index === 0 && styles.firstReply,
              index === replies.length - 1 && styles.lastReply,
            ]}
            onPress={() => onReplyPress(reply)}
            activeOpacity={0.7}
          >
            {reply.icon && (
              <Ionicons
                name={reply.icon as any}
                size={16}
                color="#2196F3"
                style={styles.replyIcon}
              />
            )}
            <Text style={styles.replyText}>{reply.text}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </Animated.View>
  );
};

// Helper function to get common quick replies with translations
export const getCommonQuickReplies = (t: any): QuickReply[] => [
  {
    id: 'tell_more',
    text: t('healthConsultation.quickReplies.common.tellMore'),
    icon: 'information-circle-outline',
  },
  {
    id: 'what_should_do',
    text: t('healthConsultation.quickReplies.common.whatShouldDo'),
    icon: 'help-circle-outline',
  },
  {
    id: 'is_serious',
    text: t('healthConsultation.quickReplies.common.isSerious'),
    icon: 'alert-circle-outline',
  },
  {
    id: 'prevention',
    text: t('healthConsultation.quickReplies.common.prevention'),
    icon: 'shield-checkmark-outline',
  },
  {
    id: 'when_doctor',
    text: t('healthConsultation.quickReplies.common.whenDoctor'),
    icon: 'medical-outline',
  },
  {
    id: 'home_remedies',
    text: t('healthConsultation.quickReplies.common.homeRemedies'),
    icon: 'home-outline',
  },
];

// Predefined quick replies for common health consultation scenarios (deprecated - use getCommonQuickReplies)
export const COMMON_QUICK_REPLIES: QuickReply[] = [
  {
    id: 'tell_more',
    text: 'Tell me more',
    icon: 'information-circle-outline',
  },
  {
    id: 'what_should_do',
    text: 'What should I do?',
    icon: 'help-circle-outline',
  },
  {
    id: 'is_serious',
    text: 'Is this serious?',
    icon: 'alert-circle-outline',
  },
  {
    id: 'prevention',
    text: 'How to prevent?',
    icon: 'shield-checkmark-outline',
  },
  {
    id: 'when_doctor',
    text: 'When to see a doctor?',
    icon: 'medical-outline',
  },
  {
    id: 'home_remedies',
    text: 'Home remedies?',
    icon: 'home-outline',
  },
];

// Helper function to get nutrition quick replies with translations
export const getNutritionQuickReplies = (t: any): QuickReply[] => [
  {
    id: 'healthy_diet',
    text: t('healthConsultation.quickReplies.nutrition.healthyDiet'),
    icon: 'nutrition-outline',
  },
  {
    id: 'weight_loss',
    text: t('healthConsultation.quickReplies.nutrition.weightLoss'),
    icon: 'fitness-outline',
  },
  {
    id: 'vitamins',
    text: t('healthConsultation.quickReplies.nutrition.vitamins'),
    icon: 'medical-outline',
  },
  {
    id: 'meal_planning',
    text: t('healthConsultation.quickReplies.nutrition.mealPlanning'),
    icon: 'calendar-outline',
  },
];

// Topic-specific quick replies (deprecated - use helper functions)
export const NUTRITION_QUICK_REPLIES: QuickReply[] = [
  {
    id: 'healthy_diet',
    text: 'Healthy diet tips',
    icon: 'nutrition-outline',
  },
  {
    id: 'weight_loss',
    text: 'Weight management',
    icon: 'fitness-outline',
  },
  {
    id: 'vitamins',
    text: 'Vitamins & minerals',
    icon: 'medical-outline',
  },
  {
    id: 'meal_planning',
    text: 'Meal planning',
    icon: 'calendar-outline',
  },
];

// Helper function to get symptoms quick replies with translations
export const getSymptomsQuickReplies = (t: any): QuickReply[] => [
  {
    id: 'headache',
    text: t('healthConsultation.quickReplies.symptoms.headache'),
    icon: 'sad-outline',
  },
  {
    id: 'fatigue',
    text: t('healthConsultation.quickReplies.symptoms.fatigue'),
    icon: 'battery-dead-outline',
  },
  {
    id: 'digestive',
    text: t('healthConsultation.quickReplies.symptoms.digestive'),
    icon: 'restaurant-outline',
  },
  {
    id: 'sleep_problems',
    text: t('healthConsultation.quickReplies.symptoms.sleepProblems'),
    icon: 'moon-outline',
  },
];

export const SYMPTOMS_QUICK_REPLIES: QuickReply[] = [
  {
    id: 'headache',
    text: 'Headache relief',
    icon: 'sad-outline',
  },
  {
    id: 'fatigue',
    text: 'Feeling tired',
    icon: 'battery-dead-outline',
  },
  {
    id: 'digestive',
    text: 'Digestive issues',
    icon: 'restaurant-outline',
  },
  {
    id: 'sleep_problems',
    text: 'Sleep problems',
    icon: 'moon-outline',
  },
];

// Helper function to get wellness quick replies with translations
export const getWellnessQuickReplies = (t: any): QuickReply[] => [
  {
    id: 'exercise',
    text: t('healthConsultation.quickReplies.wellness.exercise'),
    icon: 'fitness-outline',
  },
  {
    id: 'stress_management',
    text: t('healthConsultation.quickReplies.wellness.stressManagement'),
    icon: 'heart-outline',
  },
  {
    id: 'mental_health',
    text: t('healthConsultation.quickReplies.wellness.mentalHealth'),
    icon: 'happy-outline',
  },
  {
    id: 'sleep_hygiene',
    text: t('healthConsultation.quickReplies.wellness.sleepHygiene'),
    icon: 'bed-outline',
  },
];

// Helper function to get prevention quick replies with translations
export const getPreventionQuickReplies = (t: any): QuickReply[] => [
  {
    id: 'immune_system',
    text: t('healthConsultation.quickReplies.prevention.immuneSystem'),
    icon: 'shield-outline',
  },
  {
    id: 'screenings',
    text: t('healthConsultation.quickReplies.prevention.screenings'),
    icon: 'medical-outline',
  },
  {
    id: 'vaccinations',
    text: t('healthConsultation.quickReplies.prevention.vaccinations'),
    icon: 'shield-checkmark-outline',
  },
  {
    id: 'lifestyle',
    text: t('healthConsultation.quickReplies.prevention.lifestyle'),
    icon: 'leaf-outline',
  },
];

export const WELLNESS_QUICK_REPLIES: QuickReply[] = [
  {
    id: 'exercise',
    text: 'Exercise routine',
    icon: 'fitness-outline',
  },
  {
    id: 'stress_management',
    text: 'Stress relief',
    icon: 'heart-outline',
  },
  {
    id: 'mental_health',
    text: 'Mental wellness',
    icon: 'happy-outline',
  },
  {
    id: 'sleep_hygiene',
    text: 'Better sleep',
    icon: 'bed-outline',
  },
];

export const PREVENTION_QUICK_REPLIES: QuickReply[] = [
  {
    id: 'immune_system',
    text: 'Boost immunity',
    icon: 'shield-outline',
  },
  {
    id: 'screenings',
    text: 'Health screenings',
    icon: 'medical-outline',
  },
  {
    id: 'vaccinations',
    text: 'Vaccinations',
    icon: 'shield-checkmark-outline',
  },
  {
    id: 'lifestyle',
    text: 'Healthy lifestyle',
    icon: 'leaf-outline',
  },
];

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    backgroundColor: '#fff',
  },
  scrollView: {
    flexGrow: 0,
  },
  scrollContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  replyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    borderColor: '#2196F3',
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
  },
  firstReply: {
    marginLeft: 0,
  },
  lastReply: {
    marginRight: 16,
  },
  replyIcon: {
    marginRight: 6,
  },
  replyText: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: '500',
  },
});
