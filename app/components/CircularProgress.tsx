import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { CircularProgress as RNCircularProgress } from 'react-native-circular-progress';

interface CircularProgressProps {
  percentage: number;
  size?: number;
  width?: number;
  color?: string;
  backgroundColor?: string;
  label?: string;
  showPercentage?: boolean;
  animated?: boolean;
  duration?: number;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  percentage,
  size = 80,
  width = 8,
  color = '#4CAF50',
  backgroundColor = '#E0E0E0',
  label,
  showPercentage = true,
  animated = true,
  duration = 1000,
}) => {
  // Ensure percentage is within bounds
  const normalizedPercentage = Math.max(0, Math.min(100, percentage));

  // Get color based on percentage if not provided
  const getColorByPercentage = (percent: number): string => {
    if (percent >= 80) return '#4CAF50'; // Green
    if (percent >= 60) return '#FFC107'; // Yellow
    if (percent >= 40) return '#FF9800'; // Orange
    return '#F44336'; // Red
  };

  const progressColor =
    color === '#4CAF50' ? getColorByPercentage(normalizedPercentage) : color;

  return (
    <View style={styles.container}>
      <RNCircularProgress
        size={size}
        width={width}
        fill={normalizedPercentage}
        tintColor={progressColor}
        backgroundColor={backgroundColor}
        rotation={0}
        lineCap="round"
        duration={animated ? duration : 0}
      >
        {() => (
          <View style={styles.centerContent}>
            {showPercentage && (
              <Text
                style={[
                  styles.percentageText,
                  { fontSize: Math.max(10, size * 0.18) },
                ]}
              >
                {Math.round(normalizedPercentage)}
              </Text>
            )}
          </View>
        )}
      </RNCircularProgress>
      {label && (
        <Text
          style={[styles.labelText, { fontSize: Math.max(9, size * 0.14) }]}
        >
          {label}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  percentageText: {
    fontWeight: 'bold',
    color: '#333',
  },
  labelText: {
    marginTop: 6,
    textAlign: 'center',
    color: '#666',
    fontWeight: '500',
  },
});

export default CircularProgress;
