---
type: 'always_apply'
---

Project Structure and Architecture:

**Development Preferences:**

- Always use codebase-retrieval tool before making code edits to gather detailed information about symbols, classes, and methods involved
- Use package managers for dependency management instead of manually editing package files
- Do not suggest writing and running tests after code changes. Test via console.logs. You can ask user to share console output for debugging
- Focus on following user instructions exactly - ask before doing additional work

**Library Implementation Requirements:**

- When installing any new library, always research and review:
  - The library's current README.md file
  - Official documentation and developer website
  - Code examples shared by the developer
  - Implementation instructions and best practices
  - Latest API changes and migration guides
- Use web-search and web-fetch tools to gather this information before implementation
- Follow the library's recommended patterns and conventions exactly
- Check for React Native specific setup instructions when applicable

**TypeScript Requirements:**

- Always use proper TypeScript types and interfaces for all data types
- When unsure about data structure or API response types, add console.log statements and ask user to share the console output
- Prefer creating explicit interfaces over using 'any' type
- Use proper typing for RevenueCat, Supabase, and API responses

**Technical Stack Preferences:**

- Use Supabase as primary data source
- Prefer React Native Reanimated for animations
- Use @gorhom/bottom-sheet@^5 for bottom sheets
- Use OpenRouter API with free models
- Retrieve API keys from Supabase apiconfig rather than environment variables
- Calculate counts from existing data length rather than maintaining separate counters

**UI/UX Preferences:**

- Follow apple human interface guidelines and apple design system
- Use full screens over modals for major features
- Make UI elements immediately visible rather than animated entrances
- Use dark gray colors over pure black for text
- Prefer native iOS context menus over Alert dialogs
- Use proper Turkish characters (ç, ğ, ı, ö, ş, ü) in Turkish text

**Code Organization:**

- Maintain additives.ts as single source of truth for additives data
- Use proper nested navigation system
- Prefer database-first architecture with Supabase
- Use RevenueCat's pre-localized price strings for subscription pricing
