# Health Consultation Feature - Product Requirements Document

## 1. Executive Summary

### 1.1 Overview

The Health Consultation feature introduces an AI-powered doctor consultation service to the Additives app, providing users with personalized health guidance through conversational AI. This feature will become the primary entry point of the application, positioned as the first tab in the bottom navigation.

### 1.2 Objectives

- **Primary**: Provide users with accessible, AI-powered health consultation services
- **Secondary**: Increase user engagement and establish a foundation for premium subscription services
- **Tertiary**: Position the app as a comprehensive health and nutrition platform

### 1.3 Success Metrics

- User engagement: 70%+ of users interact with health consultation within first week
- Session duration: Average 5+ minutes per consultation session
- Retention: 60%+ of users return for additional consultations within 30 days
- Conversion: 15%+ subscription rate after 2 free consultations

## 2. Current System Analysis

### 2.1 Existing Architecture

- **Framework**: React Native with Expo (v52.0.47)
- **Navigation**: Expo Router with bottom tabs navigation
- **AI Integration**: OpenRouter API with retry logic (10-20 attempts, 5-second intervals)
- **Database**: Supabase for data persistence
- **Localization**: Turkish/English support via LocalizationContext
- **State Management**: React Context providers
- **UI Libraries**: @gorhom/bottom-sheet, React Native Reanimated, @expo/vector-icons

### 2.2 Current Tab Structure

1. **Database** (`index.tsx`) - E-code database browsing
2. **Scan** (`scan.tsx`) - Food label scanning
3. **History** (`history.tsx`) - Scan history with expandable cards

### 2.3 Existing Patterns to Follow

- **Error Handling**: Retry logic with 10-20 attempts, 5-second intervals
- **Loading States**: ActivityIndicator with Turkish progress messages
- **UI Patterns**: Sophisticated interfaces with minimal overlays
- **Data Storage**: AsyncStorage for local data, Supabase for cloud persistence
- **Animations**: React Native Reanimated for smooth transitions

## 3. Feature Requirements

### 3.1 Core Functionality

#### 3.1.1 Conversational AI Interface

- **Chat-like Interface**: Real-time conversation flow with message bubbles
- **Medical Context**: AI trained to provide health guidance with professional tone
- **Follow-up Questions**: AI proactively asks relevant follow-up questions
- **Multi-turn Conversations**: Support for extended dialogue sessions
- **Context Awareness**: AI remembers conversation context within sessions

#### 3.1.2 Conversation Management

- **Multiple Threads**: Users can maintain separate conversations for different health topics
- **Thread Naming**: Auto-generate or allow custom naming of conversation threads
- **Thread Organization**: Chronological listing with preview of last message
- **Thread Persistence**: Save conversations locally and sync to cloud

#### 3.1.3 Message Types

- **Text Messages**: Standard text-based communication
- **Quick Replies**: Predefined response options for common queries
- **Health Assessments**: Structured questionnaires for specific conditions
- **Recommendations**: Formatted advice with actionable items

### 3.2 Technical Architecture

#### 3.2.1 Navigation Integration

- **New Tab**: Add "Health Consultation" as first tab (leftmost position)
- **Default Screen**: Set as initial screen on app launch
- **Tab Icon**: Medical/health-related icon (stethoscope, medical cross)
- **Tab Label**: Localized "Sağlık Danışmanlığı" (TR) / "Health Consultation" (EN)

#### 3.2.2 AI Service Integration

- **OpenRouter API**: Utilize existing OpenRouter configuration
- **Model Selection**: Use appropriate model for conversational AI (different from E-code detection)
- **Retry Logic**: Implement existing retry pattern (10-20 attempts, 5-second intervals)
- **Error Handling**: Graceful degradation with user-friendly error messages

#### 3.2.3 Data Models

```typescript
interface HealthConversation {
  id: string;
  title: string;
  createdAt: number;
  updatedAt: number;
  messageCount: number;
  lastMessage?: string;
  isActive: boolean;
}

interface HealthMessage {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  messageType: 'text' | 'quick_reply' | 'assessment' | 'recommendation';
  metadata?: Record<string, any>;
}
```

### 3.3 User Interface Design

#### 3.3.1 Main Screen Layout

- **Header**: Title with new conversation button
- **Conversation List**: Scrollable list of conversation threads
- **Empty State**: Welcoming message with "Start New Consultation" CTA
- **Quick Actions**: Common health topics as quick-start options

#### 3.3.2 Chat Interface

- **Message Bubbles**: Distinct styling for user vs AI messages
- **Typing Indicator**: Show when AI is processing response
- **Input Field**: Text input with send button
- **Message Status**: Delivery and read indicators
- **Scroll Behavior**: Auto-scroll to latest messages

#### 3.3.3 Visual Design Principles

- **Medical Professional**: Clean, trustworthy design with medical color palette
- **Accessibility**: High contrast, readable fonts, proper touch targets
- **Consistency**: Follow existing app design patterns and spacing
- **Turkish Support**: Proper Turkish character rendering and RTL considerations

## 4. Implementation Phases

### 4.1 Phase 1: Foundation (Week 1-2)

- Create new tab structure with Health Consultation as first tab
- Set up basic navigation and routing
- Implement conversation data models and storage
- Create basic chat interface components
- Add localization keys for Turkish/English support

### 4.2 Phase 2: Core Chat Functionality (Week 3-4)

- Implement message sending/receiving
- Integrate OpenRouter API for conversational AI
- Add retry logic and error handling
- Implement conversation persistence
- Create typing indicators and loading states

### 4.3 Phase 3: Advanced Features (Week 5-6)

- Multiple conversation thread management
- Message history and search
- Quick reply options
- Health assessment questionnaires
- Recommendation formatting

### 4.4 Phase 4: Polish & Optimization (Week 7-8)

- Performance optimization
- Animation improvements with React Native Reanimated
- Comprehensive testing
- Accessibility improvements
- Final UI/UX refinements

## 5. Monetization Strategy (Future Implementation)

### 5.1 Freemium Model

- **Free Tier**: 2 messages per conversation thread
- **Premium Tier**: Unlimited messages, priority support, advanced features
- **Subscription Options**: Monthly (₺29.99) / Yearly (₺299.99)

### 5.2 Paywall Implementation

- **Trigger**: After 2nd user message in any conversation
- **UI**: Modal with subscription options and benefits
- **Architecture**: Prepare subscription management infrastructure

## 6. Technical Specifications

### 6.1 File Structure

```
app/
├── (tabs)/
│   ├── health-consultation.tsx (new main screen)
│   └── _layout.tsx (updated with new tab)
├── health-consultation/
│   ├── chat/
│   │   └── [conversationId].tsx (chat interface)
│   └── new-conversation.tsx (conversation starter)
├── components/
│   ├── health/
│   │   ├── ConversationList.tsx
│   │   ├── ChatInterface.tsx
│   │   ├── MessageBubble.tsx
│   │   ├── TypingIndicator.tsx
│   │   └── QuickReplyOptions.tsx
├── services/
│   └── HealthConsultationAI.ts (AI service)
├── context/
│   └── HealthConsultationContext.tsx (state management)
└── utils/
    └── HealthConversationStorage.ts (data persistence)
```

### 6.2 Dependencies

- **Existing**: All current dependencies are sufficient
- **No New Dependencies**: Leverage existing OpenRouter, Supabase, and UI libraries
- **Reuse Patterns**: Follow existing AI service patterns and UI components

### 6.3 Performance Considerations

- **Message Pagination**: Load messages in chunks for large conversations
- **Image Optimization**: Compress and cache any health-related images
- **Memory Management**: Proper cleanup of conversation data
- **Network Optimization**: Batch API calls where possible

## 7. Localization Requirements

### 7.1 New Translation Keys

```typescript
healthConsultation: {
  tabTitle: string;
  welcomeTitle: string;
  welcomeMessage: string;
  startNewConsultation: string;
  conversationHistory: string;
  typeMessage: string;
  sendMessage: string;
  aiTyping: string;
  errorMessage: string;
  retryMessage: string;
  // ... additional keys
}
```

### 7.2 Turkish Medical Terminology

- Ensure proper medical terminology in Turkish
- Cultural sensitivity for health discussions
- Appropriate formal/informal language usage

## 8. Quality Assurance

### 8.1 Testing Strategy

- **Unit Tests**: Core conversation logic and AI service
- **Integration Tests**: API integration and data persistence
- **UI Tests**: Chat interface and navigation
- **Accessibility Tests**: Screen reader compatibility
- **Performance Tests**: Large conversation handling

### 8.2 Medical Disclaimer

- Clear disclaimer about AI limitations
- Recommendation to consult real healthcare professionals
- Emergency contact information for urgent situations

## 9. Success Criteria

### 9.1 Technical Success

- ✅ Health Consultation tab is default landing screen
- ✅ Chat interface responds within 3 seconds average
- ✅ 99.5% uptime for AI service integration
- ✅ Conversations persist across app sessions
- ✅ Full Turkish/English localization

### 9.2 User Experience Success

- ✅ Intuitive conversation flow
- ✅ Professional medical tone in AI responses
- ✅ Smooth animations and transitions
- ✅ Accessible to users with disabilities
- ✅ Consistent with existing app design language

## 10. Risk Mitigation

### 10.1 Technical Risks

- **API Rate Limits**: Implement robust retry logic and user feedback
- **Data Privacy**: Ensure GDPR compliance for health data
- **Performance**: Optimize for low-end devices

### 10.2 Medical/Legal Risks

- **Medical Advice Disclaimer**: Clear limitations and disclaimers
- **Data Security**: Encrypt sensitive health conversations
- **Regulatory Compliance**: Follow health data regulations

## 11. Detailed Implementation Guide

### 11.1 AI Model Configuration

```typescript
// Add to AIModelConfig.ts
export type AITask =
  | 'e-code-detection'
  | 'nutrition-extraction'
  | 'food-analysis'
  | 'health-consultation'; // New task type

const MODEL_CONFIGS: Record<AITask, ModelConfig> = {
  // ... existing configs
  'health-consultation': {
    primary: 'anthropic/claude-3-haiku',
    fallback: 'openai/gpt-3.5-turbo',
    maxTokens: 800,
    temperature: 0.7, // Higher for more conversational responses
  },
};
```

### 11.2 System Prompts for Health AI

```typescript
const getHealthConsultationSystemPrompt = (language: 'en' | 'tr') => {
  if (language === 'tr') {
    return `Sen deneyimli bir sağlık danışmanısın. Kullanıcılara profesyonel, anlayışlı ve güvenilir sağlık tavsiyeleri veriyorsun.

ÖNEMLI KURALLAR:
- Her zaman profesyonel ve empatik bir dil kullan
- Kesin teşhis koyma, sadece genel sağlık bilgisi ver
- Acil durumlar için doktora başvurmayı öner
- Kişisel deneyimlerini paylaş gibi davranma
- Türkçe tıbbi terimlerini doğru kullan

Kullanıcının sorularını dikkatlice dinle ve takip soruları sor.`;
  } else {
    return `You are an experienced health consultant. You provide professional, understanding, and reliable health advice to users.

IMPORTANT RULES:
- Always use professional and empathetic language
- Don't provide definitive diagnoses, only general health information
- Recommend consulting a doctor for urgent situations
- Don't pretend to share personal experiences
- Use proper medical terminology

Listen carefully to user questions and ask follow-up questions.`;
  }
};
```

### 11.3 Database Schema Extensions

```sql
-- Add to Supabase database
CREATE TABLE health_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  device_id TEXT, -- For anonymous users
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  message_count INTEGER DEFAULT 0,
  last_message TEXT,
  is_active BOOLEAN DEFAULT true
);

CREATE TABLE health_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID REFERENCES health_conversations(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'quick_reply', 'assessment', 'recommendation')),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_health_conversations_device_id ON health_conversations(device_id);
CREATE INDEX idx_health_messages_conversation_id ON health_messages(conversation_id);
CREATE INDEX idx_health_messages_created_at ON health_messages(created_at);
```

### 11.4 Context Provider Structure

```typescript
interface HealthConsultationContextType {
  conversations: HealthConversation[];
  activeConversation: HealthConversation | null;
  messages: HealthMessage[];
  isLoading: boolean;
  isTyping: boolean;

  // Actions
  createConversation: (title?: string) => Promise<HealthConversation>;
  loadConversation: (id: string) => Promise<void>;
  sendMessage: (content: string) => Promise<void>;
  deleteConversation: (id: string) => Promise<void>;
  loadConversations: () => Promise<void>;
}
```

### 11.5 Component Architecture

```typescript
// Main Health Consultation Screen
const HealthConsultationScreen = () => {
  // List of conversations with create new option
  // Quick start health topics
  // Recent conversations preview
};

// Individual Chat Screen
const ChatScreen = ({ conversationId }: { conversationId: string }) => {
  // Message list with pagination
  // Input field with send button
  // Typing indicator
  // Quick reply options
};

// Message Components
const MessageBubble = ({ message, isUser }: MessageBubbleProps) => {
  // Styled message bubble
  // Timestamp
  // Message status indicators
};
```

### 11.6 Navigation Updates

```typescript
// Update (tabs)/_layout.tsx
<Tabs.Screen
  name="health-consultation"
  options={{
    title: t('tabs.healthConsultation'),
    headerTitle: t('healthConsultation.title'),
    headerShown: true,
    tabBarIcon: ({ size, color }) => (
      <Ionicons name="medical" size={size} color={color} />
    ),
  }}
/>
```

### 11.7 Storage Strategy

- **Local Storage**: AsyncStorage for offline conversation cache
- **Cloud Sync**: Supabase for cross-device synchronization
- **Data Retention**: 90-day retention policy for conversations
- **Privacy**: End-to-end encryption for sensitive health data

### 11.8 Error Handling Patterns

```typescript
const sendMessageWithRetry = async (content: string) => {
  const maxRetries = 15;
  const retryDelay = 5000;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await healthConsultationAPI.sendMessage(content);
      return response;
    } catch (error) {
      if (attempt === maxRetries) {
        throw new Error(t('healthConsultation.maxRetriesExceeded'));
      }

      await new Promise((resolve) => setTimeout(resolve, retryDelay));
    }
  }
};
```

### 11.9 Accessibility Features

- **Screen Reader**: Proper ARIA labels for all interactive elements
- **Voice Control**: Support for voice input and output
- **High Contrast**: Ensure proper color contrast ratios
- **Font Scaling**: Support for system font size preferences
- **Keyboard Navigation**: Full keyboard accessibility

### 11.10 Performance Optimizations

- **Message Virtualization**: Use FlatList with getItemLayout for large conversations
- **Image Lazy Loading**: Load message images on demand
- **Memory Management**: Clear old conversation data from memory
- **Network Optimization**: Batch message sending and receiving

### 11.11 Security Considerations

- **Data Encryption**: Encrypt sensitive health information
- **API Security**: Secure API endpoints with proper authentication
- **Input Validation**: Sanitize all user inputs
- **Rate Limiting**: Prevent API abuse with client-side rate limiting

### 11.12 Testing Strategy

```typescript
// Unit Tests
describe('HealthConsultationService', () => {
  test('should create new conversation', async () => {
    // Test conversation creation
  });

  test('should send message with retry logic', async () => {
    // Test message sending with network failures
  });
});

// Integration Tests
describe('Chat Interface', () => {
  test('should display messages correctly', () => {
    // Test message rendering
  });

  test('should handle typing indicators', () => {
    // Test real-time UI updates
  });
});
```

---

_This comprehensive PRD provides detailed technical specifications and implementation guidance for the Health Consultation feature, ensuring seamless integration with the existing Additives app architecture while delivering a professional health consultation experience._
