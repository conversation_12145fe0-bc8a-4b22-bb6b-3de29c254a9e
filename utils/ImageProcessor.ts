import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import TextRecognition from '@react-native-ml-kit/text-recognition';

// Cache for processed images
const imageCache = new Map<string, { text: string; timestamp: number }>();
const CACHE_EXPIRY = 1000 * 60 * 60; // 1 hour

export interface ImageProcessingOptions {
  enhanceText?: boolean;
  useCache?: boolean;
}

/**
 * Process an image and extract text from it
 * @param imageUri URI of the image to process
 * @param options Processing options
 * @returns Recognized text
 */
export async function processImage(
  imageUri: string,
  options: ImageProcessingOptions = {}
): Promise<string> {
  const { enhanceText = true, useCache = true } = options;

  // Check cache first if enabled
  if (useCache) {
    const cachedResult = imageCache.get(imageUri);
    if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_EXPIRY) {
      return cachedResult.text;
    }
  }

  try {
    // Optimize the image for text recognition
    const processedImage = await optimizeImageForTextRecognition(
      imageUri,
      enhanceText
    );

    // Use ML Kit for text recognition
    let result;
    try {
      // Use ML Kit for text recognition
      result = await TextRecognition.recognize(processedImage.uri);
    } catch (error) {
      console.error('Error in text recognition:', error);
      throw error;
    }

    // Cache the result if caching is enabled
    if (useCache) {
      imageCache.set(imageUri, {
        text: result.text,
        timestamp: Date.now(),
      });
    }

    // Clean up temporary file
    if (processedImage.uri !== imageUri) {
      await FileSystem.deleteAsync(processedImage.uri, { idempotent: true });
    }

    return result.text;
  } catch (error) {
    console.error('Error processing image:', error);
    throw error;
  }
}

/**
 * Optimize an image for text recognition
 * @param imageUri URI of the image to optimize
 * @param enhanceText Whether to apply text enhancement
 * @returns Processed image URI
 */
async function optimizeImageForTextRecognition(
  imageUri: string,
  _enhanceText: boolean // Unused parameter, but kept for API compatibility
): Promise<{ uri: string }> {
  try {
    // Apply image manipulations to improve text recognition
    const operations: ImageManipulator.Action[] = [];

    // Resize large images to improve processing speed
    operations.push({ resize: { width: 1200 } });

    // Process the image
    const processedImage = await ImageManipulator.manipulateAsync(
      imageUri,
      operations,
      { format: ImageManipulator.SaveFormat.JPEG, compress: 0.8 }
    );

    return processedImage;
  } catch (error) {
    console.error('Error optimizing image:', error);
    // Return original image if processing fails
    return { uri: imageUri };
  }
}
