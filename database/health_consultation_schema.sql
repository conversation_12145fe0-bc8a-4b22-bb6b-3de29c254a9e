-- Health Consultation Database Schema for Supabase
-- This script creates the necessary tables for the health consultation feature

-- Create health_conversations table
CREATE TABLE IF NOT EXISTS health_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  device_id TEXT, -- For anonymous users
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  message_count INTEGER DEFAULT 0,
  last_message TEXT,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB -- For storing additional data like topic, language, tags
);

-- Create health_messages table
CREATE TABLE IF NOT EXISTS health_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID REFERENCES health_conversations(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'quick_reply', 'assessment', 'recommendation')),
  metadata JSONB, -- For storing additional message data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_health_conversations_device_id ON health_conversations(device_id);
CREATE INDEX IF NOT EXISTS idx_health_conversations_created_at ON health_conversations(created_at);
CREATE INDEX IF NOT EXISTS idx_health_conversations_updated_at ON health_conversations(updated_at);
CREATE INDEX IF NOT EXISTS idx_health_conversations_is_active ON health_conversations(is_active);

CREATE INDEX IF NOT EXISTS idx_health_messages_conversation_id ON health_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_health_messages_created_at ON health_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_health_messages_role ON health_messages(role);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_health_conversation_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at on health_conversations
DROP TRIGGER IF EXISTS trigger_update_health_conversation_updated_at ON health_conversations;
CREATE TRIGGER trigger_update_health_conversation_updated_at
  BEFORE UPDATE ON health_conversations
  FOR EACH ROW
  EXECUTE FUNCTION update_health_conversation_updated_at();

-- Enable Row Level Security (RLS) for security
ALTER TABLE health_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE health_messages ENABLE ROW LEVEL SECURITY;

-- Create policies for health_conversations
-- Allow all operations for now (since we're using device_id for anonymous users)
-- In production, you might want to restrict this further
CREATE POLICY "Allow all operations on health_conversations" ON health_conversations
  FOR ALL USING (true);

-- Create policies for health_messages
-- Allow all operations for now
CREATE POLICY "Allow all operations on health_messages" ON health_messages
  FOR ALL USING (true);

-- Grant necessary permissions to the authenticated role
GRANT ALL ON health_conversations TO authenticated;
GRANT ALL ON health_messages TO authenticated;

-- Grant necessary permissions to the anon role (for anonymous users)
GRANT ALL ON health_conversations TO anon;
GRANT ALL ON health_messages TO anon;

-- Comments for documentation
COMMENT ON TABLE health_conversations IS 'Stores health consultation conversations';
COMMENT ON COLUMN health_conversations.device_id IS 'Device identifier for anonymous users';
COMMENT ON COLUMN health_conversations.metadata IS 'JSON field for storing topic, language, tags, etc.';

COMMENT ON TABLE health_messages IS 'Stores individual messages within health consultations';
COMMENT ON COLUMN health_messages.metadata IS 'JSON field for storing message-specific data like quick replies, assessments, etc.';
