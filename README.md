# Additives App

A mobile application for scanning and identifying E-codes (food additives) in products.

## Features

- Scan barcodes to retrieve product information and ingredients
- Scan text to directly detect E-codes in ingredient lists
- Browse a comprehensive database of E-codes
- Filter additives by safety status (safe, questionable, harmful)
- Multilingual support (English and Turkish)

## Recent Improvements

### Image Recognition

The app now uses an optimized image recognition system with the following improvements:

- **ExecuTorch Integration**: Uses React Native ExecuTorch v0.4.0 for faster and more accurate text recognition when available
- **Fallback Mechanism**: Automatically falls back to ML Kit if ExecuTorch is not available
- **Image Preprocessing**: Optimizes images before text recognition to improve accuracy
- **Caching**: Caches recognition results to improve performance for repeated scans

### E-code Matching

The E-code matching algorithm has been completely rewritten for better performance:

- **Trie Data Structure**: Uses a trie for efficient exact matching of E-codes
- **Optimized Approximate Matching**: Improved algorithm for matching additive names
- **Precomputed Mappings**: Faster lookups with precomputed maps for codes and names
- **Reduced Memory Usage**: More efficient data structures reduce memory consumption

## Optimized Image Processing and Text Recognition

This app uses an optimized approach for image processing and text recognition to improve performance:

- Efficient image preprocessing for better recognition results
- Optimized ML Kit integration for text recognition
- Caching mechanism to improve performance and reduce processing time
- Smart image quality settings to balance accuracy and speed

## Development

### Prerequisites

- Node.js (v16 or later)
- Yarn or npm
- Expo CLI
- iOS: XCode and CocoaPods
- Android: Android Studio and Android SDK

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/additives.git
cd additives
```

2. Install dependencies:

```bash
yarn install
```

3. Start the development server:

```bash
yarn dev
```

4. Run on a device or emulator:

```bash
# For iOS
yarn ios

# For Android
yarn android
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
