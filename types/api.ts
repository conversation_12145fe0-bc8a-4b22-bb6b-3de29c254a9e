export interface OpenFoodFactsResponse {
  code: string;
  product: Product;
  status: string;
}

export interface Product {
  _id: string;
  product_name: string;
  brands: string;
  categories: string;
  image_front_url: string;
  ingredients_text: string;
  nutriments: {
    energy_100g: number;
    proteins_100g: number;
    carbohydrates_100g: number;
    fat_100g: number;
    fiber_100g: number;
    'salt_100g': number;
    'sugars_100g': number;
    'saturated-fat_100g': number;
  };
  nutriscore_grade?: string;
  nova_group?: number;
  ecoscore_grade?: string;
  labels?: string;
  allergens_tags?: string[];
  traces_tags?: string[];
  origins?: string;
}