import React from 'react';
import Svg, { Circle, Path, SvgProps, Line } from 'react-native-svg';

const CameraPermissionDeniedIcon: React.FC<SvgProps> = (props) => {
  return (
    <Svg
      width={60}
      height={60}
      viewBox="0 0 24 24"
      fill="none"
      stroke="#ffffff"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      {/* Camera body */}
      <Path d="M2 8h4l2-3h8l2 3h4v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2z" />
      {/* Camera lens */}
      <Circle cx={12} cy={14} r={3} />
      {/* Red 'X' overlay on the lens */}
      <Line x1={10} y1={12} x2={14} y2={16} stroke="red" strokeWidth={2} />
      <Line x1={14} y1={12} x2={10} y2={16} stroke="red" strokeWidth={2} />
    </Svg>
  );
};

export default CameraPermissionDeniedIcon;
